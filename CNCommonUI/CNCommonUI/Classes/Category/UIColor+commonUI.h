//
//  UIColor+UPStockMain.h
//  UPStockMain
//
//  Created by <PERSON><PERSON><PERSON> on 2020/6/11.
//  Copyright © 2020 UpChina. All rights reserved.
//
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIColor (commonUI)
// 快讯标题背景色(#F4F4F4)
@property(class, nonatomic, readonly) UIColor * upstockmain_flash_title_bg_color;
// 快讯股票背景色(#F5F5F5)
@property(class, nonatomic, readonly) UIColor * upstockmain_flash_stock_bg_color;

// 快讯股票背景色(#7F111111)
@property(class, nonatomic, readonly) UIColor * upstockmain_flash_font_shadeBGColor;

// 首证头条类型背景色(#19D22325)
@property(class, nonatomic, readonly) UIColor * upstockMain_headline_news_type_bg_color;

// 首证头条标题颜色(#111111)
@property(class, nonatomic, readonly) UIColor * upsotckMain_headline_news_title_color;

// 问董秘卡片背景色(#F2F2F2->#2F3342)
@property(class, nonatomic, readonly) UIColor * upstockMain_optinal_qa_card_bg_color;

// 7x24竖线(#EBECED->#0F1319)
@property(class, nonatomic, readonly) UIColor * upstockmain_flash_vertical_color;

// 新股申购标题(#FD5C32->#F8F8F8)
@property(class, nonatomic, readonly) UIColor * upstockmain_newstock_title_color;

// 自选资讯未登录页面(#F1F1F1->#F8F8F8)
@property(class, nonatomic, readonly) UIColor * upstockmain_option_new_nologin_color;


// 标题文本色(#222222)
@property(class, nonatomic, readonly) UIColor * upmarket2_majorTextColor;

// 遮罩背景色(#80111111)
@property(class, nonatomic, readonly) UIColor * upmarket2_maskBgColor;

// 长按背景色(#F1F6FE)
@property(class, nonatomic, readonly) UIColor * upmarket2_indexNameBgColor;

// 长按背景色(#F6F6F6->2F3342)
@property(class, nonatomic, readonly) UIColor * upmarket2_longPressMaskBgColor;

// 长按背景色(#5C5C5C->F8F8F8)
@property(class, nonatomic, readonly) UIColor * upmarket2_longPressTimeColor;

// 叠加Header背景色(#E63131->#222531)
@property(class, nonatomic, readonly) UIColor * upmarket2_maskStockTitleBgColor;

// 叠加Header自定义文本色(#FFFFFF->#898F92)
@property(class, nonatomic, readonly) UIColor * upmarket2_maskStockCustomTitleColor;

// 五档按钮背景色(#F2F2F2->#2F3342)
@property(class, nonatomic, readonly) UIColor * upmarket2_tradeViewBtnBgColor;

// 市场Tab标题被选中颜色(#898F92->#898F92)
@property(class, nonatomic, readonly) UIColor * upmarket2_tabTitleSelectedColor;

// 叠加按钮边线色(#33111111)
@property(class, nonatomic, readonly) UIColor * upmarket2_maskStockButtonBorderColor;

// 叠加按钮点击时文本色(#FFFFFF)
@property(class, nonatomic, readonly) UIColor * upmarket2_maskStockButtonTitleSelectColor;

// 叠加按钮未点击时文本色(#5C5C5C)
@property(class, nonatomic, readonly) UIColor * upmarket2_maskStockButtonTitleUnselectColor;

// 历史分时按钮可用文本色(#5C5C5C->F8F8F8)
@property(class, nonatomic, readonly) UIColor * upmarket2_historyButtonEnableColor;

// 历史分时按钮不可用文本色(#909090->909090)
@property(class, nonatomic, readonly) UIColor * upmarket2_historyButtonDisableColor;

// header颜色(#f2f2f2)
@property(class, nonatomic, readonly) UIColor * upmarket2_settingHeaderColor;

// 个股广告背景色(#F2F3F4)
@property(class, nonatomic, readonly) UIColor * upmarket2_adBGColor;

// 量比大于3颜色(#E051B6)
@property(class, nonatomic, readonly) UIColor * upmarket2_lbSpecialColor;

// 盘口按钮背景色(#F5F9FD)
@property(class, nonatomic, readonly) UIColor * upmarket2_handicapButtonBGColor;

// 遮挡的背景颜色(#000000)
@property(class, nonatomic, readonly) UIColor * upmarket2_shadeBGColor;

// 查看十档按钮颜色(#2277CC)
@property(class, nonatomic, readonly) UIColor * upmarket2_fiveViewTenButtonColor;

// 底部更多按钮字体颜色(#FFFFFF->222531)
@property(class, nonatomic, readonly) UIColor * upmarket2_footerBgColor;

// 底部更多按钮字体颜色(#666666)
@property(class, nonatomic, readonly) UIColor * upmarket2_moreViewTitleColor;

// SearchText字体颜色(#111111)
@property(class, nonatomic, readonly) UIColor * upmarket2_newOptionPlaceHolderColor;

// 确定按钮字体颜色(#D22325)
@property(class, nonatomic, readonly) UIColor * upmarket2_ensureColor;

// 板块异动列表时间字体颜色(#FF9D03)
@property(class, nonatomic, readonly) UIColor * upmarket2_blockChangeCell_timeColor;

// 板块异动列表时间下竖线颜色(#F2A848)
@property(class, nonatomic, readonly) UIColor * upmarket2_blockChangeCell_lineColor;

// A股内板块异动上方切换按钮选择时的字体颜色(#E43434)
@property(class, nonatomic, readonly) UIColor * upmarket2_AStcok_blockChangeBtnTitle_selColor;

// 资金流向列表代码字体颜色(#666666)
@property(class, nonatomic, readonly) UIColor * upmarket2_fundList_codeColor;

// pageController选中颜色(#909090->FF4600)
@property(class, nonatomic, readonly) UIColor * upmarket2_page_selectedColor;

//指示器未选中颜色,#DADAE6
@property(class, nonatomic, readonly) UIColor * upmarket2_page_normalColor;

// 资金流向地图背景颜色
@property(class, nonatomic, readonly) UIColor * upmarket2_treeMap_rise5Color; // 涨大于 0.05
@property(class, nonatomic, readonly) UIColor * upmarket2_treeMap_rise2Color; // 涨大于 0.02
@property(class, nonatomic, readonly) UIColor * upmarket2_treeMap_riseColor; // 涨大于 0
@property(class, nonatomic, readonly) UIColor * upmarket2_treeMap_equalColor; // 平
@property(class, nonatomic, readonly) UIColor * upmarket2_treeMap_fall5Color; // 跌大于 0.05
@property(class, nonatomic, readonly) UIColor * upmarket2_treeMap_fall2Color; // 跌大于 0.02
@property(class, nonatomic, readonly) UIColor * upmarket2_treeMap_fallColor; // 跌大于 0

// 大盘指数
@property(class, nonatomic, readonly) UIColor * upmarket2_indexStock_rise_gradient1_bg_Color; // 涨背景色1
@property(class, nonatomic, readonly) UIColor * upmarket2_indexStock_rise_gradient2_bg_Color; // 涨背景色2
@property(class, nonatomic, readonly) UIColor * upmarket2_indexStock_fall_gradient1_bg_Color; // 跌背景色1
@property(class, nonatomic, readonly) UIColor * upmarket2_indexStock_fall_gradient2_bg_Color; // 跌背景色2
@property(class, nonatomic, readonly) UIColor * upmarket2_indexStock_equal_gradient1_bg_Color; // 平背景色1
@property(class, nonatomic, readonly) UIColor * upmarket2_indexStock_equal_gradient2_bg_Color; // 平背景色2
// 榜单
@property(class, nonatomic, readonly) UIColor * upmarket2_board_left_scroll_bg_Color; // 左侧滚动背景 (#F9F8F8)

/* A股 - 板块*/
@property(class, nonatomic, readonly) UIColor * upmarket2_block_fund_in_bg_start_Color; // 板块资金流入背景开始色 (#FB7C7C)
@property(class, nonatomic, readonly) UIColor * upmarket2_block_fund_out_bg_start_Color; // 板块资金流出背景开始色 (#4FDC9F)
@property(class, nonatomic, readonly) UIColor * upmarket2_block_fund_line_bg_Color; // 板块资金线背景色 (#DDDDDD)

/* 市场 - 更多*/
@property(class, nonatomic, readonly) UIColor * upmarket2_more_btn_border_Color; // 更多按钮的边框色 (#D3D3D3)

// 个股设置详情页恢复默认按钮disable状态颜色(#4C333333)
@property(class, nonatomic, readonly) UIColor * upmarket2_stock_setting_reset_disable_Color;

// 队列手数文本色
@property(class, nonatomic, readonly) UIColor * upmarket2_extra_queue_vol_text_Color;

// 队列 title颜色(#666666)
@property(class, nonatomic, readonly) UIColor * upmarket2_extra_queue_title_Color;

// 除权除息 背景颜色(#FFE0C0)
@property(class, nonatomic, readonly) UIColor * upmarket2_qx_bg_Color;

///@brief 行情设置segment背景色normal (Light: #FFFFFF    Dark: #272A38)
@property(class, nonatomic, readonly) UIColor * upmarket2_segment_normal_bgColor;
///@brief 行情设置segment背景色selected (Light: #D22325    Dark: #2F3342)
@property(class, nonatomic, readonly) UIColor * upmarket2_segment_selected_bgColor;
///@brief 行情设置segment标题颜色normal (Light: #D22325    Dark: #898F92)
@property(class, nonatomic, readonly) UIColor * upmarket2_segment_normal_title_Color;
///@brief 行情设置segment标题颜色selected (Light: #FFFFFF    Dark: #FF4600)
@property(class, nonatomic, readonly) UIColor * upmarket2_segment_selected_title_Color;
///@brief 行情设置segment边框颜色(Light: #D22325    Dark: #0F1319)
@property(class, nonatomic, readonly) UIColor * upmarket2_segment_border_color;

#pragma mark 通用颜色
///市场文字颜色，#222222
@property(class, nonatomic, readonly) UIColor * up_market2_title_color;

///市场文字颜色2，#9295B2
@property(class, nonatomic, readonly) UIColor * up_market2_title_sec_color;

///市场文字颜色3，#787D87
@property(class, nonatomic, readonly) UIColor * up_market2_title_thi_color;

///市场文字颜色4，#666666
@property(class, nonatomic, readonly) UIColor * up_market2_title_fourth_color;

///市场tab未选中文字颜色,#787D87
@property(class, nonatomic, readonly) UIColor * up_market2_tab_normal_color;

///市场tab选中文字颜色,#222222
@property(class, nonatomic, readonly) UIColor * up_market2_tab_select_color;

///市场tab未选中背景颜色,#F1F3FC
@property(class, nonatomic, readonly) UIColor * up_market2_tab_bg_normal_color;

///市场tab未选中背景边框颜色
@property(class, nonatomic, readonly) UIColor * up_market2_tab_bg_border_normal_color;

///市场分割线颜色,#F3F3F7
@property(class, nonatomic, readonly) UIColor * up_market2_divider_line_color;

///市场分割线颜色2,#F4F6F8
@property(class, nonatomic, readonly) UIColor * up_market2_divider_line_sec_color;

///市场-更多-边框线颜色
@property(class, nonatomic, readonly) UIColor * up_market2_more_item_border_color;

///异动播报item背景色,#F8F8FF
@property(class, nonatomic, readonly) UIColor * up_market2_yidong_item_bg;

///异动播报按钮背景色
@property(class, nonatomic, readonly) UIColor * up_market2_yidong_btn_bg;

///异动播报股票文字颜色，#657180
@property (class, nonatomic, readonly) UIColor * up_market2_yidong_stock;

///备注笔记分割线,#D6D6F2
@property (class, nonatomic, readonly) UIColor * up_market2_note_divider_color;

///备注笔记股票代码颜色,#8282AA
@property (class, nonatomic, readonly) UIColor * up_market2_note_stock_code_color;

///备注笔记输入框提示符颜色,#9BA5B8
@property (class, nonatomic, readonly) UIColor * up_market2_note_placeholder_color;

///备注笔记输入框背景颜色,#FAFBFD
@property (class, nonatomic, readonly) UIColor * up_market2_note_textview_bg_color;

///备注笔记列表背景颜色,#F7F9FA
@property (class, nonatomic, readonly) UIColor * up_market2_note_list_bg_color;

@end

NS_ASSUME_NONNULL_END
