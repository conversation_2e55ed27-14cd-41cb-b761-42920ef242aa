//
//  UPUserPrivilegeUtil.h
//  UPCommon
//
//  Created by park<PERSON><PERSON><PERSON> on 2018/8/2.
//  Copyright © 2018年 UPChina. All rights reserved.
//

#import <Foundation/Foundation.h>

#define kUPUserPrivilegeCDXH  @"1016"     // 超跌信号
#define kUPUserPrivilegeLTQB  @"1018"     // 龙头启爆
#define kUPUserPrivilegeQSZS  @"1019"     // 趋势主升


// MARK: 权限类型
typedef NS_ENUM(NSUInteger, UPUserRightType) {
    UPUserRightTypePT = 0,                  //普通
    UPUserRightTypeQL = 1,                  // 擒龙
    UPUserRightTypeQLTimeOut = 1 << 1,     // 擒龙过期
    UPUserRightTypeZZ = 1 << 2,             // 至尊
    UPUserRightTypeZZTimeOut = 1 << 3,      // 至尊过期
};

#define kUPUserPrivilegeQL  @"1010"     // 擒龙版
#define kUPUserPrivilegeZZ  @"1031"     // 至尊版
#define kUPUserPrivilegeDF1  @"1169"     // 东方研选
#define kUPUserPrivilegeDF2  @"1170"     // 东方研选
#define kUPUserPrivilegeLTSXY  @"1028"     // 龙头实训营
#define kUPUserPrivilegeDSTY  @"1167"     // 大师投研
#define kUPUserPrivilegeAll  @"SUCCESS"     // 全部权限



/**
 @param isShow 是否显示入口
 @param isCanRouter 是否可以跳转
 @param rightId 权限id，大部分地方是根据权限id是否为空进行判断跳转
 */
@interface UPUserRightsUtilModel : NSObject

/**
 是否显示入口
 */
@property (nonatomic, assign) BOOL isShow;

/**
 是否可以跳转
 */
@property (nonatomic, assign) BOOL isCanRouter;

/**
 是否显示顶部角标
 */
@property (nonatomic, assign) BOOL isShowBageImg;

/**
 需要的对应权限字符串
 */
@property (nonatomic, strong) NSString * rightId;

+ (instancetype)modelWithIsShow:(BOOL)isShow
                    isCanRouter:(BOOL)isCanRouter
                        rightId:(NSString*)rightId;

+ (instancetype)modelWithIsShow:(BOOL)isShow
                    isCanRouter:(BOOL)isCanRouter
                        rightId:(NSString*)rightId
                  isShowBageImg:(BOOL)isShowBageImg;

@end

typedef void(^UPUserRightsUtilBlock)(BOOL isShow,BOOL isCanRouter,NSString* rightId);
typedef void(^UPUserRightsUtilModelBlock)(UPUserRightsUtilModel * rightModel);



