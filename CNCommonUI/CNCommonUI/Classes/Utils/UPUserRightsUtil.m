//
//  UPUserPrivilegeUtil.m
//  UPCommon
//
//  Created by parkerxu on 2018/8/2.
//  Copyright © 2018年 UPChina. All rights reserved.
//

#import <UPUserSDK/UPUserSDK.h>
#import "UPUserRightsUtil.h"

@implementation UPUserRightsUtilModel

+ (instancetype)modelWithIsShow:(BOOL)isShow
                    isCanRouter:(BOOL)isCanRouter
                        rightId:(NSString*)rightId{
    return [self modelWithIsShow:isShow isCanRouter:isCanRouter rightId:rightId isShowBageImg:YES];
}

+ (instancetype)modelWithIsShow:(BOOL)isShow
                    isCanRouter:(BOOL)isCanRouter
                        rightId:(NSString*)rightId
                  isShowBageImg:(BOOL)isShowBageImg{
    UPUserRightsUtilModel * model = [UPUserRightsUtilModel new];
    model.isShow = isShow;
    model.isCanRouter = isCanRouter;
    model.isShowBageImg = isShowBageImg;
    model.rightId = rightId;
    
    return model;
}


@end


