//
//  UPMarketFreezeColumnContainerView.h
//  UPMarket2
//
//  Created by 彭继宗 on 2023/6/27.
//

#import <UIKit/UIKit.h>
#import "UPMarketFreezeColumnView.h"

NS_ASSUME_NONNULL_BEGIN

@protocol UPMarketFreezeColumnContainerDelegate <NSObject>

// Class必须继承自DFStockFreezeColumnViewCell
- (Class)freezeColumnViewHeaderCellClassFor:(UPMarketFreezeColumnViewColumn *)column;
// Class必须继承自DFStockFreezeColumnViewCell
- (Class)freezeColumnViewDataCellClassFor:(UPMarketFreezeColumnViewColumn *)column;

- (void)freezeColumnViewConfigDataCell:(UPMarketFreezeColumnViewCell *)dataCell
                                column:(UPMarketFreezeColumnViewColumn *)column
                            indexOfRow:(NSInteger)indexOfRow;

- (void)freezeColumnViewHeaderDidSelect:(UPMarketFreezeColumnViewColumn *)column;

- (void)freezeColumnViewRowDidSelect:(NSInteger)indexOfRow column:(NSInteger)column;

- (void)freezeColumnViewConfigHeaderCell:(UPMarketFreezeColumnViewCell *)columnCell
                  column:(UPMarketFreezeColumnViewColumn *)column;

- (void)scrollViewDidScrollToOffsetX:(CGFloat)offsetX index:(NSInteger)index;

@end


@interface UPMarketFreezeColumnContainerView : UIView

@property (nonatomic, weak) id<UPMarketFreezeColumnContainerDelegate> delegate;
@property (nonatomic, assign) NSInteger index;
@property (nonatomic, strong) NSArray<UPMarketFreezeColumnViewColumn *> *columns;

- (void)updateOffset:(CGFloat)offset;
@end

NS_ASSUME_NONNULL_END
