//
//  UPMarketFreezeColumnContainerView.m
//  UPMarket2
//
//  Created by 彭继宗 on 2023/6/27.
//

#import "UPMarketFreezeColumnContainerView.h"


@interface UPMarketFreezeColumnContainerCollectionView : UICollectionView

@end

@implementation UPMarketFreezeColumnContainerCollectionView

- (void)setContentOffset:(CGPoint)contentOffset
{
    if (contentOffset.x == 0 || contentOffset.x == super.contentOffset.x) {
        return;
    }
    [super setContentOffset:contentOffset];
}

@end

@interface UPMarketFreezeColumnContainerView () <UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>

@property (nonatomic, strong) UPMarketFreezeColumnContainerCollectionView *collectionView;

@property (nonatomic, strong) NSMutableSet *registeredClass;

@end

@implementation UPMarketFreezeColumnContainerView

- (instancetype)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame]) {
        [self setupViews];
        [self setupConstraints];
    }
    return self;
}

- (void)setupViews
{
    [self addSubview:self.collectionView];
}

- (void)setupConstraints
{
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        [make edges];
    }];
}

#pragma mark - UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.columns.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    
    UPFreezeColumnViewCell *cell = nil;
    
    UPMarketFreezeColumnViewColumn * column = self.columns[indexPath.row];
    
    Class clazz = self.index >= 0 ? [self.delegate freezeColumnViewDataCellClassFor:column] : [self.delegate freezeColumnViewHeaderCellClassFor:column];

    if(clazz) {
        NSString * className = NSStringFromClass(clazz);
        
        if(![self.registeredClass containsObject:className]) {
            [collectionView registerClass:clazz forCellWithReuseIdentifier:className];
            [self.registeredClass addObject:className];
        }
        
        cell = [collectionView dequeueReusableCellWithReuseIdentifier:className forIndexPath:indexPath];
        cell.backgroundColor = UIColor.clearColor;
        
        [self configDataCellFor:(UPMarketFreezeColumnViewCell *)cell column:column indexOfRow:self.index];
    }
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath
{
    UPMarketFreezeColumnViewColumn *column = self.columns[indexPath.item];
    return CGSizeMake(column.width, self.up_height);
}

- (void)collectionView:(UICollectionView *)collectionView willDisplayCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath
{
    UPMarketFreezeColumnViewColumn * column = self.columns[indexPath.row];
    
    [self configDataCellFor:(UPMarketFreezeColumnViewCell *)cell column:column indexOfRow:self.index];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView
{
    if ([self.delegate respondsToSelector:@selector(scrollViewDidScrollToOffsetX:index:)]) {
        [self.delegate scrollViewDidScrollToOffsetX:scrollView.contentOffset.x index:self.index];
    }
}

- (void)configDataCellFor:(UPMarketFreezeColumnViewCell *)cell column:(UPMarketFreezeColumnViewColumn *)column indexOfRow:(NSInteger)indexOfRow {
    
    cell.isLastColumn = column.isLastColumn;
    
    __strong id<UPMarketFreezeColumnContainerDelegate> delegate = self.delegate;
    
    if (self.index >= 0) {
        if(delegate && [delegate respondsToSelector:@selector(freezeColumnViewConfigDataCell:column:indexOfRow:)]) {
            [delegate freezeColumnViewConfigDataCell:cell column:column indexOfRow:indexOfRow];
        }
    }else{
        if(delegate && [delegate respondsToSelector:@selector(freezeColumnViewConfigHeaderCell:column:)]) {
            [delegate freezeColumnViewConfigHeaderCell:cell column:column];
        }
    }
}

#pragma mark - UICollectionViewDelegate

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    if (self.index == -1) {
        if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnViewHeaderDidSelect:)]) {
            return  [self.delegate freezeColumnViewHeaderDidSelect:self.columns[indexPath.row]];
        }
    }else{
        if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnViewRowDidSelect:column:)]) {
            return  [self.delegate freezeColumnViewRowDidSelect:self.index column:indexPath.row];
        }
    }
}

- (void)updateOffset:(CGFloat)offset
{
    self.collectionView.contentOffset = CGPointMake(offset, 0);
}

#pragma mark - SETTER && GETTER
- (void)setColumns:(NSArray<UPMarketFreezeColumnViewColumn *> *)columns
{
    _columns = columns;
    CGPoint savedContentOffset = self.collectionView.contentOffset;
    [self.collectionView reloadData];
}



- (UPMarketFreezeColumnContainerCollectionView *)collectionView {
    if (!_collectionView) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.minimumLineSpacing = 0;
        layout.minimumInteritemSpacing = 0;
        layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;

        _collectionView = [[UPMarketFreezeColumnContainerCollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
        _collectionView.dataSource = self;
        _collectionView.delegate = self;
        _collectionView.showsHorizontalScrollIndicator = NO;
        [_collectionView registerClass:[UICollectionViewCell class] forCellWithReuseIdentifier:@"UICollectionViewCell"];
        _collectionView.backgroundColor = UIColor.clearColor;
    }
    return _collectionView;
}


- (NSMutableSet *)registeredClass {
    if (!_registeredClass) {
        _registeredClass = [[NSMutableSet alloc] init];
    }
    return _registeredClass;
}

@end
