//
//  DFStockFreezeColumnView.h
//  UPMarket2
//
//  Created by 彭继宗 on 2023/6/27.
//

#import <UIKit/UIKit.h>
#import "UPMarketFreezeColumnView.h"
@class DFStockFreezeColumnView;
NS_ASSUME_NONNULL_BEGIN

// MARK: DFStockFreezeColumnViewDelegate

@protocol DFStockFreezeColumnViewDelegate <NSObject>
@optional

- (NSInteger)numberOfRowsInFreezeColumnView:(DFStockFreezeColumnView *)freezeColumnView;

- (NSArray<UPMarketFreezeColumnViewColumn *> *)columnsInFreezeColumnView:(DFStockFreezeColumnView *)freezeColumnView;

// Class必须继承自DFStockFreezeColumnViewCell
- (Class)freezeColumnView:(DFStockFreezeColumnView *)freezeColumnView
       headerCellClassFor:(UPMarketFreezeColumnViewColumn *)column;

// Class必须继承自UPMarketFreezeFooterView 每一行的section footer的类型目前只能注册一个类型
- (UIView *)freezeColumnView:(DFStockFreezeColumnView *)freezeColumnView viewForFooterInRow:(NSInteger)row;

- (UIView *)freezeColumnViewCollectionFooterView:(DFStockFreezeColumnView *)freezeColumnView;

- (Class)freezeColumnView:(DFStockFreezeColumnView *)freezeColumnView
         dataCellClassFor:(UPMarketFreezeColumnViewColumn *)column;

- (void)freezeColumnView:(DFStockFreezeColumnView *)freezeColumnView
        configHeaderCell:(UPMarketFreezeColumnViewCell *)columnCell
                  column:(UPMarketFreezeColumnViewColumn *)column;

- (void)freezeColumnView:(DFStockFreezeColumnView *)freezeColumnView
 configSectionFooterView:(UPMarketFreezeFooterView *)footerView
                 section:(NSInteger)section;

- (void)freezeColumnView:(DFStockFreezeColumnView *)freezeColumnView
        configCollectionFooterView:(UPMarketFreezeColumnViewColumn *)footerView;

- (void)freezeColumnView:(DFStockFreezeColumnView *)freezeColumnView
          configDataCell:(UPMarketFreezeColumnViewCell *)dataCell
                  column:(UPMarketFreezeColumnViewColumn *)column
              indexOfRow:(NSInteger)indexOfRow;

- (void)freezeColumnView:(DFStockFreezeColumnView *)freezeColumnView
     visibleRowDidChange:(NSRange)visibleRowRange;

- (void)freezeColumnView:(DFStockFreezeColumnView *)freezeColumnView
         headerDidSelect:(UPMarketFreezeColumnViewColumn *)column;

- (void)freezeColumnView:(DFStockFreezeColumnView *)freezeColumnView
            rowDidSelect:(NSInteger)indexOfRow
                  column:(NSInteger)column;

- (void)freezeColumnView:(DFStockFreezeColumnView *)freezeColumnView
               didScroll:(CGPoint)offset;

- (CGFloat)footerHeightForFreezeColumnView:(DFStockFreezeColumnView *)freezeColumnView
                                   section:(NSInteger)section;

@end

@interface DFStockFreezeColumnView : UIView

@property (nonatomic, copy) void (^freezeScrollViewDidScrollBlock)(UIScrollView *scrollView);

@property(nonatomic, weak) id<DFStockFreezeColumnViewDelegate> delegate;

@property(nonatomic, strong, readonly) UIScrollView * scrollView;

@property(nonatomic, assign) CGPoint contentOffset;
@property(nonatomic, assign) CGSize contentSize;

@property(nonatomic, assign) BOOL showsScrollIndicator;

@property(nonatomic, assign) CGFloat headerHeight; // 表头高度
@property(nonatomic, assign) CGFloat rowHeight; // 行高度

@property(nonatomic, strong) UIColor * listBackgroundColor; // collectionView背景色

@property(nonatomic, strong) UIColor * rowBackgroundColor; // 常态背景色
@property(nonatomic, strong) UIColor * rowBackgroundColorSelected; // 选中背景色

@property(nonatomic, strong) UIColor * separatorColor; // 分割线颜色
@property(nonatomic, assign) UIEdgeInsets separatorInset; // 分割线位置
@property(nonatomic, assign) CGFloat separatorHeight; // 分割线高度
@property(nonatomic, assign) BOOL showsHeaderSeparator; // header下面是是否显示分割线, 默认NO
@property(nonatomic, assign) BOOL showsLastRowSeparator; // 最后一行是否显示分割线, 默认NO
@property(nonatomic, assign) BOOL enableDynamicFooterHeight; // 是否展示不同高度的footer, 默认NO

- (void)reloadData;


@end

NS_ASSUME_NONNULL_END
