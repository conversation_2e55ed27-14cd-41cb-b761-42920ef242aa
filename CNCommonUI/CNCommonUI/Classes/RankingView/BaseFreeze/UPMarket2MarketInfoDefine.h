//
//  UPMarket2MarketInfoDefine.h
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/5/8.
//  Copyright © 2020 UpChina. All rights reserved.
//

#ifndef UPMarket2MarketInfoDefine_h
#define UPMarket2MarketInfoDefine_h

// 指数
#define UPMarket2MarketInfoHeaderIndexTag 61

// 涨跌统计
#define UPMarket2MarketInfoZDStatisticsStaticTag 62

// 资金流入
#define UPMarket2MarketInfoCapitalInflowStaticTag 63

// 个股异动列表
#define UPMarket2MarketInfoStockChangeColumnTag 64

// 短线精灵列表
#define UPMarket2MarketInfoDXJLTableTag 65

// 热门板块
#define UPMarket2MarketInfoHotBlockContentTag 66

// 榜单
#define UPMarket2MarketInfoStockRankingTag 31
#define UPMarket2MarketInfoStockRankingFlowTag 32
#define UPMarket2MarketInfoStockRankingAHTag 33
#define UPMarket2MarketInfoBondHGTag 34
#define UPMarket2MarketInfoOptionListTag 35


// 新股
#define UPMarket2MarketInfoNewStockTag 36

// 板块
#define UPMarket2MarketInfoBlockHeaderTag 20
#define UPMarket2MarketInfoBlockIndustryTag 25
#define UPMarket2MarketInfoBlockConceptTag 26
#define UPMarket2MarketInfoBlockRegionTag 27
#define UPMarket2MarketInfoBlockColumnTag 21

// 期货
#define UPMarketFutureZJSTag 61
#define UPMarketFutureSQSTag 62
#define UPMarketFutureDSSTag 63
#define UPMarketFutureZSSTag 64

// 基金
#define kUPMarket2MarketInfoFundHSTag 71
#define kUPMarket2MarketInfoFundSHTag 72
#define kUPMarket2MarketInfoFundSZTag 73
#define kUPMarket2MarketInfoFundETFTag 74
#define kUPMarket2MarketInfoFundLOFTag 75
#define kUPMarket2MarketInfoFundFBTag 76
#define kUPMarket2MarketInfoFundHBTag 77

/// 全球
#define kUPMarket2MarketInfoGlobalHSIndexTag 51
#define kUPMarket2MarketInfoGlobalZYIndexTag 52
#define kUPMarket2MarketInfoGlobalHLIndexTag 53

/// 板块异动
//分时
#define UPMarket2BlockChangeHeaderStockMinuteTag 41
//异动领涨股
#define UPMarket2BlockChangeHeaderSubjectChangeTopTag 42

/// 资金流向
#define UPMarket2ZJFYBlockRankTag 43
#define UPMarket2ZJFYConstituentStockRankTag 44

#define MAX3(A,B,C) MAX(MAX(A, B), C)
#define MIN3(A,B,C) MIN(MIN(A, B), C)
#define MAX4(A,B,C,D) MAX(MAX(MAX(A, B), C), D)
#define MIN4(A,B,C,D) MIN(MIN(MIN(A, B), C), D)
#define MAX5(A,B,C,D,E) MAX(MAX(MAX(MAX(A, B), C), D), E)
#define MIN5(A,B,C,D,E) MIN(MIN(MIN(MIN(A, B), C), D), E)
#define MAX6(A,B,C,D,E,F) MAX(MAX(MAX(MAX(MAX(A, B), C), D), E), F)
#define MIN6(A,B,C,D,E,F) MIN(MIN(MIN(MIN(MIN(A, B), C), D), E), F)
#define MAX7(A,B,C,D,E,F,H) MAX(MAX(MAX(MAX(MAX(MAX(A, B), C), D), E), F),H)
#define MIN7(A,B,C,D,E,F,H) MIN(MIN(MIN(MIN(MIN(MIN(A, B), C), D), E), F), H)

#endif /* UPMarket2MarketInfoDefine_h */
