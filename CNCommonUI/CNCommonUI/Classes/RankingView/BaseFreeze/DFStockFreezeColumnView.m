//
//  DFStockFreezeColumnView.m
//  UPMarket2
//
//  Created by 彭继宗 on 2023/6/27.
//

#import "DFStockFreezeColumnView.h"
#import "DFStockFreezeTableViewCell.h"


@interface DFStockFreezeColumnView () <UITableViewDelegate, UITableViewDataSource, DFStockFreezeTableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;

@property(nonatomic, assign) NSInteger numberOfRows;
@property(nonatomic, copy) NSArray<UPMarketFreezeColumnViewColumn *> * columns;

@property(nonatomic, strong) NSMutableSet * registeredClass;
@property(nonatomic, strong) Class registeredSectionFooterClass;
@property(nonatomic, strong) Class registeredCollectionFooterClass;

@property(nonatomic, assign) CGFloat initialContentOffsetX;

@property (nonatomic, strong) DFStockFreezeTableViewCell *headerCell;

@end

@implementation DFStockFreezeColumnView

- (instancetype)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame]) {
        [self setupViews];
        [self setupConstraints];
    }
    return self;
}

- (void)setupViews
{
    [self addSubview:self.tableView];
}

- (void)setupConstraints
{
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        [make edges];
    }];
}

#pragma mark - SETTER && GETTER



#pragma mark - tableView DataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.numberOfRows;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    DFStockFreezeTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass(DFStockFreezeTableViewCell.class)];
    cell.delegate = self;
    [cell updateWithColumns:self.columns index:indexPath.row rowHeight:self.rowHeight];
    [cell updateOffset:self.initialContentOffsetX];
    
    if ([self.delegate respondsToSelector:@selector(freezeColumnView:viewForFooterInRow:)]) {
        UIView *footerView = [self.delegate freezeColumnView:self viewForFooterInRow:indexPath.row];
        [cell updatefooterView:footerView];
    }else{
        [cell updatefooterView:nil];
    }
    
    return cell;
}

- (void)scrollWithOffsetX:(CGFloat)offsetX index:(NSInteger)index
{
    if (offsetX < 0) {
        self.initialContentOffsetX = floorf(offsetX);
    }else if (offsetX > 0){
        self.initialContentOffsetX = ceilf(offsetX);
    }else{
        self.initialContentOffsetX = 1;
    }
    
    NSMutableArray<NSIndexPath *> *allIndexPaths = [self.tableView indexPathsForVisibleRows].mutableCopy;
    [allIndexPaths removeObject:[NSIndexPath indexPathForRow:index inSection:0]];
    
    for (NSIndexPath *indexPath in allIndexPaths) {
        DFStockFreezeTableViewCell *cell = [self.tableView cellForRowAtIndexPath:indexPath];
        [cell updateOffset:self.initialContentOffsetX];
    }
    
    [self.headerCell updateOffset:self.initialContentOffsetX];
}

#pragma mark - tableView Delegate
- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    DFStockFreezeTableViewCell *cell = [[DFStockFreezeTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"DFStockFreezeTableHeaderView"];
    cell.delegate = self;
    [cell updateWithColumns:self.columns index:-1 rowHeight:self.rowHeight];
    [cell updateOffset:self.initialContentOffsetX];
    if (section == 0) {
        self.headerCell = cell;
    }
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    return self.headerHeight;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension;
}

-(void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    [self performRowDidSelect:indexPath.row column:0]; // 去掉表头
}


#pragma mark- DFStockFreezeTableViewDelegate
- (void)scrollViewDidScrollToOffsetX:(CGFloat)offsetX index:(NSInteger)index
{
    [self scrollWithOffsetX:offsetX index:index];
}

- (Class)freezeColumnViewDataCellClassFor:(UPMarketFreezeColumnViewColumn *)column
{
    Class clazz = [self loadDataCellClassFor:column];

    return clazz;
}

- (Class)freezeColumnViewHeaderCellClassFor:(UPMarketFreezeColumnViewColumn *)column
{
    Class clazz = [self loadHeaderCellClassFor:column];

    return clazz;
}

- (void)freezeColumnViewHeaderDidSelectRow:(UPMarketFreezeColumnViewColumn *)column
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnView:headerDidSelect:)]) {
        [self.delegate freezeColumnView:self headerDidSelect:column];
    }
}

- (void)freezeColumnViewRowDidSelectRow:(NSInteger)indexOfRow column:(NSInteger)column
{
    [self performRowDidSelect:indexOfRow column:column]; // 去掉表头
}

- (void)freezeColumnViewConfigDataCell:(UPMarketFreezeColumnViewCell *)dataCell
                                column:(UPMarketFreezeColumnViewColumn *)column
                            indexOfRow:(NSInteger)indexOfRow
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnView:configDataCell:column:indexOfRow:)]) {
        [self.delegate freezeColumnView:self configDataCell:dataCell column:column indexOfRow:indexOfRow];
    }
}

- (void)freezeColumnViewConfigHeaderCell:(UPMarketFreezeColumnViewCell *)columnCell column:(UPMarketFreezeColumnViewColumn *)column
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnView:configHeaderCell:column:)]) {
        [self.delegate freezeColumnView:self configHeaderCell:columnCell column:column];
    }
}

- (void)freezeColumnViewConfigHeaderCellOnHeaderClick:(DFStockFreezeTableViewCell *)cell
{

    
    [self performHeaderDidSelect:self.columns.firstObject];
}

#pragma mark - SETTER && GETTER

// MARK: Public

- (void)reloadData {
    self.numberOfRows = [self loadNumberOfRows];
    self.columns = [self loadColumns];

    if ([self.delegate respondsToSelector:@selector(freezeColumnViewCollectionFooterView:)]) {
        UIView *footerView = [self.delegate freezeColumnViewCollectionFooterView:self];
        self.tableView.tableFooterView = footerView;
    }
    
    [self.tableView reloadData];
}

// MARK: Getter & Setter

- (void)setColumns:(NSArray<UPMarketFreezeColumnViewColumn *> *)columns {
    _columns = columns;
    
    if (columns.count) {
        columns.lastObject.isLastColumn = YES;
    }
}


- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.backgroundColor = [UIColor whiteColor];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.estimatedRowHeight = 40;
        if (@available(iOS 11.0, *)) {
            _tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        }
        [_tableView registerClass:[DFStockFreezeTableViewCell class] forCellReuseIdentifier:NSStringFromClass(DFStockFreezeTableViewCell.class)];
        [_tableView registerClass:DFStockFreezeTableViewCell.class forCellReuseIdentifier:@"DFStockFreezeTableHeaderView"];
    }
    return _tableView;
}

- (UIScrollView *)scrollView{
    return self.tableView;
}

-(void)scrollViewDidScroll:(UIScrollView *)scrollView {

    [self performDidScroll:scrollView.contentOffset];
    
    !self.freezeScrollViewDidScrollBlock?:self.freezeScrollViewDidScrollBlock(scrollView);

    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(scrollDidStop) object:nil];
    [self performSelector:@selector(scrollDidStop) withObject:nil afterDelay:0.3];
}

#pragma mark- privite
- (void)scrollDidStop {
    NSInteger firstRowIndex = [[self.tableView.indexPathsForVisibleRows valueForKeyPath:@"@min.row"] integerValue];
    NSInteger lastRowIndex = [[self.tableView.indexPathsForVisibleRows valueForKeyPath:@"@max.row"] integerValue];

    NSInteger start = firstRowIndex;
    NSInteger length = lastRowIndex - firstRowIndex;
    
    NSRange range = NSMakeRange(start,length);
    
    [self performVisibleRowDidChange:range];
}

-(NSInteger)loadNumberOfRows {
    __strong id<DFStockFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(numberOfRowsInFreezeColumnView:)]) {
        return [delegate numberOfRowsInFreezeColumnView:self];
    }

    return 0;
}

-(NSArray<UPMarketFreezeColumnViewColumn *> *)loadColumns {
    __strong id<DFStockFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(columnsInFreezeColumnView:)]) {
        return [delegate columnsInFreezeColumnView:self];
    }
    
    return nil;
}

- (Class)loadHeaderCellClassFor:(UPMarketFreezeColumnViewColumn *)column {
    __strong id<DFStockFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:headerCellClassFor:)]) {
        return [delegate freezeColumnView:self headerCellClassFor:column];
    }
    
    return nil;
}

- (Class)loadDataCellClassFor:(UPMarketFreezeColumnViewColumn *)column {
    __strong id<DFStockFreezeColumnViewDelegate> delegate = self.delegate;

    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:dataCellClassFor:)]) {
        return [delegate freezeColumnView:self dataCellClassFor:column];
    }
    
    return nil;
}

- (void)configHeaderCellFor:(UPMarketFreezeColumnViewCell *)cell column:(UPMarketFreezeColumnViewColumn *)column {
    
    cell.isLastColumn = column.isLastColumn;
    
    __strong id<DFStockFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:configHeaderCell:column:)]) {
        [delegate freezeColumnView:self configHeaderCell:cell column:column];
    }
}

- (void)configSectionFooterViewFor:(UPMarketFreezeFooterView *)footerView section:(NSInteger)section{
    
    __strong id<DFStockFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:configSectionFooterView:section:)]) {
        [delegate freezeColumnView:self configSectionFooterView:footerView section:section];
    }
}

- (void)configCollectionFooterViewFor:(UPMarketFreezeFooterView *)footerView{
    
    __strong id<DFStockFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:configCollectionFooterView:)]) {
        [delegate freezeColumnView:self configCollectionFooterView:footerView];
    }
}


- (void)configDataCellFor:(UPMarketFreezeColumnViewCell *)cell column:(UPMarketFreezeColumnViewColumn *)column indexOfRow:(NSInteger)indexOfRow {
    
    cell.isLastColumn = column.isLastColumn;
    
    __strong id<DFStockFreezeColumnViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:configDataCell:column:indexOfRow:)]) {
        [delegate freezeColumnView:self configDataCell:cell column:column indexOfRow:indexOfRow];
    }
}

- (void)performVisibleRowDidChange:(NSRange)visibleRowRange {
    __strong id<DFStockFreezeColumnViewDelegate> delegate = self.delegate;

    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:visibleRowDidChange:)]) {
        [delegate freezeColumnView:self visibleRowDidChange:visibleRowRange];
    }
}

- (void)performHeaderDidSelect:(UPMarketFreezeColumnViewColumn *)column {
    __strong id<DFStockFreezeColumnViewDelegate> delegate = self.delegate;

    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:headerDidSelect:)]) {
        [delegate freezeColumnView:self headerDidSelect:column];
    }
}

- (void)performRowDidSelect:(NSInteger)indexOfRow column:(NSInteger)column{
    __strong id<DFStockFreezeColumnViewDelegate> delegate = self.delegate;

    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:rowDidSelect:column:)]) {
        [delegate freezeColumnView:self rowDidSelect:indexOfRow column:column];
    }
}

- (void)performDidScroll:(CGPoint)offset {
    __strong id<DFStockFreezeColumnViewDelegate> delegate = self.delegate;

    if(delegate && [delegate respondsToSelector:@selector(freezeColumnView:didScroll:)]) {
        [delegate freezeColumnView:self didScroll:offset];
    }
}



@end
