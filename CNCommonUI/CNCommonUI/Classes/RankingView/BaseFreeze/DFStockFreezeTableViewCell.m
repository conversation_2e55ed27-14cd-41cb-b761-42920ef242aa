//
//  DFStockFreezeTableViewCell.m
//  UPMarket2
//
//  Created by 彭继宗 on 2023/6/27.
//

#import <CNCommonUI/DFStockFreezeTableViewCell.h>
#import "UPMarketFreezeColumnContainerView.h"

@interface DFStockFreezeTableViewCell ()<UPMarketFreezeColumnContainerDelegate>

@property (nonatomic, assign) NSInteger index;
@property (nonatomic, strong) NSArray<UPMarketFreezeColumnViewColumn *> *columns;


@property (nonatomic, strong) UIStackView *stackView;

@property (nonatomic, strong) UIView *dataContentView;
@property (nonatomic, strong) UIView *footerContentView;
@property (nonatomic, strong) UIView *freezeView;
@property (nonatomic, weak) UPMarketFreezeColumnViewCell *freezeCell;

@property (nonatomic, strong) UPMarketFreezeColumnContainerView *columnContainerView;

@property (nonatomic, strong) MASConstraint *dataHeight;

@end

@implementation DFStockFreezeTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupViews];
        [self setupConstraints];
    }
    return self;
}

- (void)setupViews
{
    [self.contentView addSubview:self.stackView];
    
    [self.stackView addArrangedSubview:self.dataContentView];
    [self.stackView addArrangedSubview:self.footerContentView];
    
    [self.dataContentView addSubview:self.freezeView];
    [self.dataContentView addSubview:self.columnContainerView];
}

- (void)setupConstraints
{
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        [make edges];
    }];
    
    [self.dataContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.stackView);
        self.dataHeight = make.height.mas_equalTo(40);
    }];
    [self.footerContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.stackView);
    }];
    
    [self.freezeView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.bottom.equalTo(self.dataContentView);
    }];
    
    [self.columnContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.right.bottom.equalTo(self.dataContentView);
        make.left.equalTo(self.freezeView.mas_right);
    }];
}


- (void)setupFreezeRowWithColumn:(UPMarketFreezeColumnViewColumn *)column
{
    [self.freezeCell removeFromSuperview];
    
    Class cls = self.index >= 0 ? [self freezeColumnViewDataCellClassFor:column] : [self freezeColumnViewHeaderCellClassFor:column];
    
    UPMarketFreezeColumnViewCell *freezeCell = [[cls alloc] init];
    
    if (self.index >= 0) {
        [self freezeColumnViewConfigDataCell:freezeCell column:column indexOfRow:self.index];
    }else{
        [self freezeColumnViewConfigHeaderCell:freezeCell column:column];
    }
    
    self.freezeCell = freezeCell;
    
    [self.freezeView addSubview:freezeCell];
    [freezeCell mas_makeConstraints:^(MASConstraintMaker *make) {
        [make edges];
        make.width.mas_equalTo(column.width);
    }];
    [self.freezeView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(column.width);
    }];

    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapAnyWhere:)];
    [self.freezeView addGestureRecognizer:tap];
}

- (void)tapAnyWhere:(UITapGestureRecognizer *)tap
{
    if (self.index == -1) {
        if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnViewHeaderDidSelectRow:)]) {
            [self.delegate freezeColumnViewHeaderDidSelectRow:self.columns.firstObject];
        }
    }else{
        if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnViewRowDidSelectRow:column:)]) {
            [self.delegate freezeColumnViewRowDidSelectRow:self.index column:self.columns.firstObject];
        }
    }
}


- (void)updateOffset:(CGFloat)offset
{
    [self.columnContainerView updateOffset:offset];
}


- (void)updateWithColumns:(NSArray<UPMarketFreezeColumnViewColumn *> *)columns index:(NSInteger)index rowHeight:(CGFloat)rowHeight
{
    self.index = index;
    self.columns = columns;
    
    self.dataHeight.offset = rowHeight;
    self.columnContainerView.index = index;
    //手动删除第一个
    NSMutableArray *moveColumns = columns.mutableCopy;
    UPMarketFreezeColumnViewColumn *freezeColumn = moveColumns.firstObject;
    [moveColumns removeObject:freezeColumn];
    self.columnContainerView.columns = moveColumns.copy;
    
    [self setupFreezeRowWithColumn:freezeColumn];
}

- (void)updatefooterView:(UIView *)footerView
{
    for (UIView *view in self.footerContentView.subviews) {
        [view removeFromSuperview];
    }
    
    if (footerView) {
        self.footerContentView.hidden = NO;
        [self.footerContentView addSubview:footerView];
        [footerView mas_makeConstraints:^(MASConstraintMaker *make) {
            [make edges];
        }];
    }else{
        self.footerContentView.hidden = YES;
    }
}

#pragma mark- delegate
- (void)scrollViewDidScrollToOffsetX:(CGFloat)offsetX index:(NSInteger)index
{
    if ([self.delegate respondsToSelector:@selector(scrollViewDidScrollToOffsetX:index:)]) {
        [self.delegate scrollViewDidScrollToOffsetX:offsetX index:index];
    }
}


-(Class)freezeColumnViewHeaderCellClassFor:(UPMarketFreezeColumnViewColumn *)column
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnViewHeaderCellClassFor:)]) {
        return  [self.delegate freezeColumnViewHeaderCellClassFor:column];
    }
    return nil;
}

- (Class)freezeColumnViewDataCellClassFor:(UPMarketFreezeColumnViewColumn *)column
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnViewDataCellClassFor:)]) {
        return  [self.delegate freezeColumnViewDataCellClassFor:column];
    }
    return nil;
}

- (void)freezeColumnViewConfigHeaderCell:(UPMarketFreezeColumnViewCell *)columnCell column:(UPMarketFreezeColumnViewColumn *)column
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnViewConfigHeaderCell:column:)]) {
        [self.delegate freezeColumnViewConfigHeaderCell:columnCell column:column];
    }
}

- (void)freezeColumnViewConfigDataCell:(UPMarketFreezeColumnViewCell *)dataCell column:(UPMarketFreezeColumnViewColumn *)column indexOfRow:(NSInteger)indexOfRow
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnViewConfigDataCell:column:indexOfRow:)]) {
        [self.delegate freezeColumnViewConfigDataCell:dataCell column:column indexOfRow:indexOfRow];
    }
}

- (void)freezeColumnViewHeaderDidSelect:(UPMarketFreezeColumnViewColumn *)column
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnViewHeaderDidSelectRow:)]) {
        [self.delegate freezeColumnViewHeaderDidSelectRow:column];
    }
}

- (void)freezeColumnViewRowDidSelect:(NSInteger)indexOfRow column:(NSInteger)column
{
    if(self.delegate && [self.delegate respondsToSelector:@selector(freezeColumnViewRowDidSelectRow:column:)]) {
        [self.delegate freezeColumnViewRowDidSelectRow:indexOfRow column:column];
    }
}

- (UIView *)freezeView {
    if (!_freezeView) {
        _freezeView = [[UIView alloc] init];
        _freezeView.backgroundColor = UIColor.whiteColor;
    }
    return _freezeView;
}


- (UPMarketFreezeColumnContainerView *)columnContainerView {
    if (!_columnContainerView) {
        _columnContainerView = [[UPMarketFreezeColumnContainerView alloc] init];
        _columnContainerView.delegate = self;
    }
    return _columnContainerView;
}


- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [UIStackView new];
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.distribution = UIStackViewDistributionFill;
        _stackView.alignment = UIStackViewAlignmentLeading;
        _stackView.spacing = 0;
    }
    return _stackView;
}


- (UIView *)dataContentView {
    if (!_dataContentView) {
        _dataContentView = [[UIView alloc] init];
    }
    return _dataContentView;
}


- (UIView *)footerContentView {
    if (!_footerContentView) {
        _footerContentView = [[UIView alloc] init];
        _footerContentView.hidden = YES;
    }
    return _footerContentView;
}

@end
