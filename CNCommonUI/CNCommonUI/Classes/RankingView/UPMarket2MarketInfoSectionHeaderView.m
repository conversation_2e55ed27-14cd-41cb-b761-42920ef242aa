//
//  UPMarket2MarketInfoSectionHeaderView.m
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/4/14.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2MarketInfoSectionHeaderView.h"

@interface UPMarket2MarketInfoSectionHeaderView ()

@property (nonatomic, strong) UIButton *sectionTitleButton;
@property (nonatomic, strong) UIButton *moreImgView;

@end

@implementation UPMarket2MarketInfoSectionHeaderView
- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        _isFold = YES;
        [self setupUI];
        [self setConstraints];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = UIColor.up_contentBgColor;
    
    [self addSubview:self.sectionTitleButton];
    [self addSubview:self.moreImgView];
}

- (void)setConstraints {
    [self.sectionTitleButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.top.equalTo(self);
        make.width.equalTo(@162);
        make.left.equalTo(@15);
    }];
    [self.moreImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.bottom.top.equalTo(self);
        make.width.equalTo(@35);
    }];
}

// MARK: - Private
- (void)sectionTitleButtonClick {
    if (self.isFold) {
        self.sectionModel.open = !self.sectionModel.open;
        [self setOpenImage];
        
        if ([self.delegate respondsToSelector:@selector(headerView:didClickTitleButtonAndViewIsON:)]) {
            [self.delegate headerView:self didClickTitleButtonAndViewIsON:self.sectionModel.open];
        }
    } else {
        [self setNoOpenImage];
    }
}

- (void)moreImgViewClick {
    if ([self.delegate respondsToSelector:@selector(headerViewDidClickMore:)]) {
        [self.delegate headerViewDidClickMore:self];
    }
}

/// 设置打开的图片
- (void)setOpenImage {
    CGFloat left = 15;
    UIImage * img = nil;
//    if (self.sectionModel.open) {
//        img = UPTImg(@"行情/板块-区头-展开");
//        [_sectionTitleButton setImage:img forState:UIControlStateNormal];
//    } else {
//        img = UPTImg(@"行情/板块-区头-收起");
//        [_sectionTitleButton setImage:img forState:UIControlStateNormal];
////        _sectionTitleButton.titleEdgeInsets = UIEdgeInsetsMake(0, left, 0, 0);
//    }
//    CGFloat titleLeftPadding = left - img.size.width;
//    _sectionTitleButton.titleEdgeInsets = UIEdgeInsetsMake(0, titleLeftPadding, 0, 0);
//    CGSize titleSize = [_sectionTitleButton.titleLabel.text sizeWithAttributes:@{NSFontAttributeName : _sectionTitleButton.titleLabel.font}];
//    _sectionTitleButton.imageEdgeInsets = UIEdgeInsetsMake(0, titleSize.width + titleLeftPadding  + 10 + img.size.width, 0, 0);
}

- (void)setNoOpenImage {
//    CGFloat left = 15;
//    [_sectionTitleButton setImage:nil forState:UIControlStateNormal];
//    _sectionTitleButton.titleEdgeInsets = UIEdgeInsetsMake(0, left, 0, 0);
}
// MARK: - Getter & Setter
- (UIButton *)sectionTitleButton {
    if (!_sectionTitleButton) {
        _sectionTitleButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_sectionTitleButton setTitleColor:UIColor.up_textPrimaryColor forState:UIControlStateNormal];
        _sectionTitleButton.titleLabel.font = [UIFont up_fontOfSize:18 weight:UIFontWeightSemibold];
//        [_sectionTitleButton addTarget:self action:@selector(sectionTitleButtonClick) forControlEvents:UIControlEventTouchUpInside];
        
        [_sectionTitleButton setTitle:_sectionModel.title forState:UIControlStateNormal];
        
        _sectionTitleButton.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
    }
    return _sectionTitleButton;
}

- (UIButton *)moreImgView {
    if (!_moreImgView) {
        _moreImgView = [UIButton buttonWithType:UIButtonTypeCustom];
        [_moreImgView setImage:UPTImg(@"行情/区头-更多") forState:UIControlStateNormal];
        [_moreImgView addTarget:self action:@selector(moreImgViewClick) forControlEvents:UIControlEventTouchUpInside];
        [_moreImgView setTitleColor:UIColor.up_market2_title_thi_color forState:UIControlStateNormal];
//        [_moreImgView setTitle:@"查看更多" forState:UIControlStateNormal];
//        _moreImgView.titleLabel.font = [UIFont up_fontOfSize:UPWidth(14)];
        
//        CGFloat speace = 3;
//        CGSize imageSize = _moreImgView.imageView.image.size;
//        CGSize titleSize = [_moreImgView.titleLabel.text sizeWithAttributes:@{NSFontAttributeName : _moreImgView.titleLabel.font}];
//
//        _moreImgView.imageEdgeInsets = UIEdgeInsetsMake(0, titleSize.width + speace, 0.0, -titleSize.width - speace);
//        _moreImgView.titleEdgeInsets = UIEdgeInsetsMake(0, -imageSize.width - speace, 0, imageSize.width + speace);

    }
    return _moreImgView;
}

- (void)setSectionModel:(UPMarket2MarketInfoSectionHeaderModel *)sectionModel {
    _sectionModel = sectionModel;
    [self.sectionTitleButton setTitle:sectionModel.title forState:UIControlStateNormal];
    if (self.isFold) {
        [self setOpenImage];
    } else {
        [self setNoOpenImage];
    }
}
@end
