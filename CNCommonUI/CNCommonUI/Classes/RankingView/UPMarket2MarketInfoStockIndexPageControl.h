//
//  UPMarket2MarketInfoStockIndexPageControl.h
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/4/7.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, UPMarket2MarketInfoStockIndexPageControlStyle) {
    UPMarket2MarketInfoStockIndexPageControlStyleLight,
    UPMarket2MarketInfoStockIndexPageControlStyleGray,
};

@interface UPMarket2MarketInfoStockIndexPageControl : UIView

+ (instancetype)pageControl;

@property (nonatomic, assign) NSInteger numberOfPages;
@property (nonatomic, assign) NSInteger currentPage;

@property(nonatomic, strong) UIColor *normalColor;
@property(nonatomic, strong) UIColor *selectedColor;

@property (nonatomic, assign) UPMarket2MarketInfoStockIndexPageControlStyle style;

@end

NS_ASSUME_NONNULL_END
