//
//  UPMarket2MarketInfoSectionHeaderView.h
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/4/14.
//  Copyright © 2020 UpChina. All rights reserved.
//
/// 市场-分区时-区头
#import "UPMarket2MarketInfoSectionHeaderModel.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2MarketInfoSectionHeaderView;

@protocol UPMarket2MarketInfoSectionHeaderDelegate <NSObject>

/// 展开、收缩
- (void)headerView:(UPMarket2MarketInfoSectionHeaderView *)headerView didClickTitleButtonAndViewIsON:(BOOL)isOn;

/// 点击更多
- (void)headerViewDidClickMore:(UPMarket2MarketInfoSectionHeaderView *)headerView;
@end

@interface UPMarket2MarketInfoSectionHeaderView : UIView

@property (nonatomic, weak) id<UPMarket2MarketInfoSectionHeaderDelegate> delegate;
@property (nonatomic, strong) UPMarket2MarketInfoSectionHeaderModel *sectionModel;
@property (nonatomic, assign) NSUInteger section;
@property (nonatomic, assign) BOOL isFold;  // 是否可折叠（默认YES，如果是NO前面没有折叠小图标）

@end

NS_ASSUME_NONNULL_END
