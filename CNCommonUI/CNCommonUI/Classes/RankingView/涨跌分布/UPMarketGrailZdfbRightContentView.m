//
//  UPMarketGrailZdfbRightContentView.m
//  UPMarket2
//
//  Created by 彭继宗 on 2023/5/6.
//

#import "UPMarketGrailZdfbRightContentView.h"
#pragma mark - UPMarketGrailZdfbItemView
static NSString * const kTitleKey = @"kTitleKey";
static NSString * const kTagKey = @"kTagKey";

typedef NS_ENUM(NSInteger,UPMarketGrailZdfbItemTag) {
    UPMarketGrailZdfbItemTagZhangTing = 0,
    UPMarketGrailZdfbItemTagDieTing,
    UPMarketGrailZdfbItemTagLianBan,
};


@interface UPMarketGrailZdfbItemView : UPBaseView

+ (instancetype)initWithTitle:(NSString*)title tag:(UPMarketGrailZdfbItemTag)tag;

@property (nonatomic, assign) UPMarketGrailZdfbItemTag itemTag;

@property (nonatomic, strong) UILabel * titleLabel;

@property (nonatomic, strong) UILabel * todayLabel;

@property (nonatomic, strong) UILabel * yesterdayLabel;

@property(nonatomic, strong) UPMarketFuPanFZDNumTrend *zdlNum;




@end


#pragma mark - UPMarketGrailZdfbRightContentView
@interface UPMarketGrailZdfbRightContentView ()

@property (nonatomic, strong) NSArray<NSDictionary*> * titleDictDataArray;

@property (nonatomic, strong) UIStackView *stackView;

@end

@implementation UPMarketGrailZdfbRightContentView

- (instancetype)initWithFrame:(CGRect)frame
{
    if (self = [super initWithFrame:frame]) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews
{
    
    
    [self addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];

    
}


- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [UIStackView new];
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.distribution = UIStackViewDistributionFill;
        _stackView.alignment = UIStackViewAlignmentCenter;
        _stackView.spacing = 10;
    }
    return _stackView;
}




- (void)layoutSubviews{
    [super layoutSubviews];
    [self.stackView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    for (NSDictionary * dict in self.titleDictDataArray) {
        NSString * title = dict[kTitleKey];
        NSNumber * tagItem = dict[kTagKey];
        UPMarketGrailZdfbItemView * itemView = [UPMarketGrailZdfbItemView initWithTitle:title tag:tagItem.integerValue];
        itemView.layer.cornerRadius = 4.f;
        [self.stackView addArrangedSubview:itemView];
        [itemView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.equalTo(self.stackView);
            make.width.equalTo(@(UPWidth(self.up_width - 20)/3));
        }];
    }
//    NSInteger lineNumber = 3;
//    CGFloat itemMargin = 10;
//    CGFloat itemWidth = self.up_width;
//    CGFloat itemHeight = UPHeight(40);
//    CGFloat startX = 0;
//    CGFloat startY = 0;
//    for (int i=0; i<self.subviews.count; i++) {
//        UIView * subView = self.subviews[i];
//        subView.frame = CGRectMake(startX, startY, itemWidth, itemHeight);
////        if (i % lineNumber == 2) {
////            startX = 0;
//            startY = startY + itemHeight + itemMargin;
////        }else{
////            startX = startX + itemMargin + itemWidth;
////        }
//    }
}

#pragma mark - SETTER && GETTER
- (void)setZdlNum:(UPMarketFuPanFZDNumTrend *)zdlNum {
    _zdlNum = zdlNum;
    
    for (UIView * view in self.stackView.subviews) {
        if ([view isKindOfClass:[UPMarketGrailZdfbItemView class]]) {
            UPMarketGrailZdfbItemView * itemView = (UPMarketGrailZdfbItemView*)view;
            itemView.zdlNum = self.zdlNum;
        }
    }
    
}
- (NSArray<NSDictionary *> *)titleDictDataArray{
    if (!_titleDictDataArray) {
        _titleDictDataArray = @[
//            @{kTitleKey : @"下跌",kTagKey : @(UPMarketGrailZdfbItemTagXiaDie)},
//            @{kTitleKey : @"平家",kTagKey : @(UPMarketGrailZdfbItemTagPingJia)},
//            @{kTitleKey : @"上涨",kTagKey : @(UPMarketGrailZdfbItemTagShangZhang)},
            @{kTitleKey : @"涨停板",kTagKey : @(UPMarketGrailZdfbItemTagZhangTing)},
            @{kTitleKey : @"跌停板",kTagKey : @(UPMarketGrailZdfbItemTagDieTing)},
            @{kTitleKey : @"连板数",kTagKey : @(UPMarketGrailZdfbItemTagLianBan)},
//            @{kTitleKey : @"停牌",kTagKey : @(UPMarketGrailZdfbItemTagTingPai)},
        ];
    }
    return _titleDictDataArray;
}

@end


@implementation UPMarketGrailZdfbItemView

+ (instancetype)initWithTitle:(NSString*)title tag:(UPMarketGrailZdfbItemTag)tag{
    UPMarketGrailZdfbItemView * itemView = [UPMarketGrailZdfbItemView new];
    itemView.titleLabel.text = title;
    itemView.itemTag = tag;
    return itemView;
}


- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        UILabel *tpday = [UILabel new];
        tpday.textColor = [UIColor up_colorFromHexString:@"#787D87"];
        tpday.font = [UIFont up_fontOfSize:UPWidth(12)];
        tpday.text = @"今";
        UILabel *ysday = [UILabel new];
        ysday.textColor = [UIColor up_colorFromHexString:@"#787D87"];
        ysday.font = [UIFont up_fontOfSize:UPWidth(12)];
        ysday.text = @"昨";
        
        
        [self addSubview:self.titleLabel];
        [self addSubview:tpday];
        [self addSubview:self.todayLabel];
        [self addSubview:ysday];
        [self addSubview:self.yesterdayLabel];

        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self);
            make.top.equalTo(self.mas_top).offset(5);
        }];
        
        [tpday mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.mas_left).offset(10);
            make.top.equalTo(self.titleLabel.mas_bottom).offset(5);
            make.bottom.equalTo(self.mas_bottom).offset(-5);

        }];
        
        [self.todayLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(tpday.mas_right).offset(3);
            make.centerY.equalTo(tpday.mas_centerY);
        }];
        [self.yesterdayLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(UPWidth(-5));
            make.centerY.equalTo(self.todayLabel.mas_centerY);
        }];
        
        [ysday mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.yesterdayLabel.mas_left).offset(UPWidth(-5));
            make.centerY.equalTo(self.todayLabel.mas_centerY);
        }];
        
     
        
        
        [self.titleLabel setContentCompressionResistancePriority:751 forAxis:UILayoutConstraintAxisHorizontal];
        
    }
    return self;
}

- (UILabel *)titleLabel{
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.textColor = [UIColor up_colorFromHexString:@"#222222"];
        _titleLabel.font = [UIFont up_fontOfSize:UPWidth(12)];
    }
    return _titleLabel;
}

- (UILabel *)todayLabel{
    if (!_todayLabel) {
        _todayLabel = [UILabel new];
        _todayLabel.textColor = [UIColor up_colorFromHexString:@"#666666"];
        _todayLabel.font = [UIFont up_fontOfSize:UPWidth(12)];
        _todayLabel.adjustsFontSizeToFitWidth = YES;
        _todayLabel.textAlignment = NSTextAlignmentRight;
        _todayLabel.text = @"--";
    }
    return _todayLabel;
}

- (UILabel *)yesterdayLabel{
    if (!_yesterdayLabel) {
        _yesterdayLabel = [UILabel new];
        _yesterdayLabel.textColor = [UIColor up_colorFromHexString:@"#666666"];
        _yesterdayLabel.font = [UIFont up_fontOfSize:UPWidth(12)];
        _yesterdayLabel.adjustsFontSizeToFitWidth = YES;
        _yesterdayLabel.textAlignment = NSTextAlignmentRight;
        _yesterdayLabel.text = @"--";
    }
    return _yesterdayLabel;
}


- (void)setZdlNum:(UPMarketFuPanFZDNumTrend *)zdlNum {
    switch (self.itemTag) {
        case UPMarketGrailZdfbItemTagZhangTing:
            self.titleLabel.textColor = UIColor.up_riseColor;
            self.todayLabel.text = [NSString stringWithFormat:@"%d",zdlNum.ZTNum];
            self.todayLabel.textColor = UIColor.up_riseColor;
            self.yesterdayLabel.text = [NSString stringWithFormat:@"%d",zdlNum.LastZTNum];
            self.yesterdayLabel.textColor = UIColor.up_riseColor;
            self.layer.borderColor = [UIColor up_colorFromHexString:@"#D71B1E"].CGColor;
            self.layer.borderWidth = 0.5;
            break;
        case UPMarketGrailZdfbItemTagDieTing:
            self.titleLabel.textColor = UIColor.up_fallColor;
            self.todayLabel.text = [NSString stringWithFormat:@"%d",zdlNum.DTNum];
            self.todayLabel.textColor = UIColor.up_fallColor;
            self.yesterdayLabel.text = [NSString stringWithFormat:@"%d",zdlNum.LastDTNum];
            self.yesterdayLabel.textColor = UIColor.up_fallColor;
            self.layer.borderColor = [UIColor up_colorFromHexString:@"#1BA180"].CGColor;
            self.layer.borderWidth = 0.5;
            break;
        case UPMarketGrailZdfbItemTagLianBan:
            self.titleLabel.textColor = UIColor.up_textPrimaryColor;
            self.todayLabel.text = [NSString stringWithFormat:@"%d",zdlNum.ConBoardNum];
            self.todayLabel.textColor = UIColor.up_textPrimaryColor;
            self.yesterdayLabel.text = [NSString stringWithFormat:@"%d",zdlNum.LastConBoardNum];
            self.yesterdayLabel.textColor = UIColor.up_textPrimaryColor;
            self.layer.borderColor = [UIColor up_colorFromHexString:@"#D9D9D9"].CGColor;
            self.layer.borderWidth = 0.5;

            break;
    }
}

@end

