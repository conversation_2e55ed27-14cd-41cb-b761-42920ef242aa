//
//  UPMarket2MarketInfoStockOptionTimeSelectView.m
//  UPMarket2
//
//  Created by <PERSON><PERSON><PERSON> on 2020/5/14.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2MarketInfoStockOptionTimeSelectView.h"
// 视图
@interface UPMarket2MarketInfoStockOptionTimeSelectView () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;     // 列表
@property (nonatomic, assign) NSInteger c_index;   // 当前选择的index
@property (nonatomic, strong) UITableViewCell *c_cell;    // 当前选择的cell

@property (nonatomic, strong) NSMutableArray *optionTArray;

@end


@implementation UPMarket2MarketInfoStockOptionTimeSelectView
- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        [self setConstraints];
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.tableView];
}

- (void)setConstraints {
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

// MARK: - Public
- (void)setOptionTArray:(NSMutableArray *)optionTArray selectIndex:(NSInteger)index {
    _optionTArray = optionTArray;
    [_tableView reloadData];
    
    self.c_index = index;
}

// MARK: - Getter & Setter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.backgroundColor = UIColor.up_contentBgColor;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
        _tableView.separatorColor = UIColor.up_dividerColor;
        _tableView.delegate = self;
        _tableView.dataSource = self;
        [_tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"UITableViewCell"];
    }
    return _tableView;
}

// MARK: - UITableViewDelegate
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return kTimeRowHeight;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [_tableView cellForRowAtIndexPath:indexPath];
    [self selectCell:indexPath cell:cell];
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    UIEdgeInsets edge = UIEdgeInsetsMake(0, 0, 0, 0);
    if ([cell respondsToSelector:@selector(setSeparatorInset:)]) {
        [cell setSeparatorInset:edge];
    }
    
    if ([cell respondsToSelector:@selector(setLayoutMargins:)]) {
        [cell setLayoutMargins:edge];
    }
}
// MARK: - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return _optionTArray.count + 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UITableViewCell"
                                                                       forIndexPath:indexPath];
    cell.textLabel.textAlignment = NSTextAlignmentCenter;
    cell.textLabel.font = [UIFont up_fontOfSize:17];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    cell.backgroundColor = tableView.backgroundColor;
    if (indexPath.row < _optionTArray.count) {
        UPMarketOptionTData *model = _optionTArray[indexPath.row];
        NSDate *date = [NSDate up_dateFromString:[NSString stringWithFormat:@"%d", model.endDate] format:@"yyyyMMdd"];
        NSString *sTime = [date up_formatDate:@"yyyy年MM月dd日"];
        
        cell.textLabel.text = [NSString stringWithFormat:@"%@(%d天)", sTime, model.leftDays];
        if (_c_index == indexPath.row) {  // 设置当前选择的字体
            self.c_cell = cell;
            cell.textLabel.textColor = UIColor.up_controlSelectedColor;
        } else {
            cell.textLabel.textColor = UIColor.up_textSecondary1Color;
        }
    } else {
        cell.textLabel.text = @"取消";
        cell.textLabel.textColor = UIColor.up_controlNormalColor;
    }
    
    return cell;
}

// MARK: - Private
- (void)selectCell:(NSIndexPath *)indexPath cell:(UITableViewCell *)cell {
    BOOL bEffect = YES;
    if (indexPath.row < _optionTArray.count) {
        if (_c_cell) {
            _c_cell.textLabel.textColor = UIColor.up_textSecondary1Color;
        } else {
            bEffect = NO;
        }
        self.c_index = indexPath.row;
        self.c_cell = cell;
        cell.textLabel.textColor = UIColor.up_controlSelectedColor;
        if (bEffect) {
            if (self.clickCallback) {
                self.clickCallback(_c_index);
            }
        }
    }
    if (bEffect) {
        if (self.dismissViewCallback) {
            self.dismissViewCallback();
        }
    }
}
@end
