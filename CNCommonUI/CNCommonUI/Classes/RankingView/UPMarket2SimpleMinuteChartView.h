//
//  UPMarket2SimpleMinuteChartView.h
//  UPMarket2
//
//  Created by kds on 2022/9/5.
//

#import <UPMarket2/UPMarket2.h>
#import "UPMarket2MarketInfoStockIndexModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface UPMarket2SimpleMinuteChartView : UIView

//绘图的区域
@property (nonatomic, assign) CGRect drawingRect;

// 线与线之间的间距 - 宽
@property (nonatomic, assign) CGFloat itemWidth;

// 线与线之间的间距 - 高
@property (nonatomic, assign) CGFloat unitHeight;

//最大值
@property(nonatomic, assign) double maxValue;

//最小值
@property(nonatomic, assign) double minValue;

@property (nonatomic, strong) UPMarket2MarketInfoStockIndexModel * model;

@end

NS_ASSUME_NONNULL_END
