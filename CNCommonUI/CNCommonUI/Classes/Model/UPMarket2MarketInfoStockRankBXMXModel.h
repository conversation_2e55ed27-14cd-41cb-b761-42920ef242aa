//
//  UPMarket2MarketInfoStockRankBXMXModel.h
//  UPMarket2
//
//  Created by lizhixiang on 2023/6/25.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN


@interface UPMarket2MarketInfoStockRankLDQDZSModel : NSObject
@property (nonatomic, copy) NSString *sName;
@property (nonatomic, assign) int martetCode;
@property (nonatomic, copy) NSString *code;
@property (nonatomic, copy) NSString *induName;

@property (nonatomic,  assign) double  fChgRatio;
@property (nonatomic,  assign) double fNowPrice;
@property (nonatomic,  assign) double  netBuy;
@property (nonatomic,  assign) double mainRatio;
@property (nonatomic,  assign) double  dLiangBi;
@property (nonatomic,  assign) double fTurnoverRate;
@property (nonatomic,  assign) double fAmount;
@property (nonatomic,  assign) double flowMarketValue;
@property (nonatomic,  copy) NSString *ltqdType;


@end

@interface UPMarket2MarketInfoStockRankBXMXModel : NSObject

//sName, fChgRatio, fNowPrice, BM_hold_amount, BM_hold_ratio, BM_pre_buy_money, BM_one_buy_increase_extent, BM_pct_chg, BM_five_buy_money, BM_five_increase_extent, BM_pct_5_chg, BM_twenty_buy_money, BM_twenty_increase_extent, BM_pct_20_chg, induName

@property (nonatomic, copy) NSString *sName;
@property (nonatomic, assign) int martetCode;
@property (nonatomic, copy) NSString *code;

@property (nonatomic,  assign) double  fChgRatio;
@property (nonatomic,  assign) double fNowPrice;
@property (nonatomic, assign) double BM_hold_amount;
@property (nonatomic, assign) double BM_hold_ratio;
@property (nonatomic,  assign) double  BM_pct_chg;
@property (nonatomic, assign) double BM_one_buy_increase_extent;
@property (nonatomic, assign) double BM_flow ;
@property (nonatomic,  assign) double BM_five_buy_money;
@property (nonatomic, assign) double  BM_five_increase_extent;
@property (nonatomic, assign) double BM_pct_5_chg;
@property (nonatomic,  assign) double BM_twenty_buy_money;
@property (nonatomic,  assign) double BM_twenty_increase_extent;
@property (nonatomic, assign) double BM_pct_20_chg;
@property (nonatomic, copy) NSString *induName;

@property (nonatomic, copy) NSString *BM_icon_flag;

@property (nonatomic, assign) BOOL BM_flag_week_flow;
@property (nonatomic, assign) BOOL BM_flag_month_flow ;
@property (nonatomic, assign) BOOL BM_flag_create ;
@property (nonatomic, assign) BOOL BM_flag_add ;
@property (nonatomic, assign) BOOL BM_flag_up ;
@property (nonatomic, assign) BOOL BM_flag_hightest ;
@property (nonatomic, assign) BOOL BM_flag_buy_against ;
@property (nonatomic, assign) BOOL BM_flag_continuous_buy  ;


@end

NS_ASSUME_NONNULL_END
