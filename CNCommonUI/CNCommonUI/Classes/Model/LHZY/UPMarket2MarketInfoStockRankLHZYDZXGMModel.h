//
//  UPMarket2MarketInfoStockRankLHZYDZXGMModel.h
//  UPMarket2
//
//  Created by lizhixiang on 2023/8/23.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPMarket2MarketInfoStockRankLHZYDZXGMModel : NSObject
//LHZY.0daysReason.stock_name,fChgRatio,szzl,fNowPrice,dLiangBi,fTurnoverRate,fAmount,,mainRatio,induName

@property (nonatomic, copy) NSString *code;
@property (nonatomic, assign) int marketCode;

@property (nonatomic, copy) NSString *stock_name;
@property (nonatomic,  assign) double  fChgRatio;
@property (nonatomic, assign) int szzl;
@property (nonatomic,  assign) double  fNowPrice;
@property (nonatomic, assign) double dLiangBi;
@property (nonatomic, assign) double fTurnoverRate;
@property (nonatomic,  assign) double  fAmount;
@property (nonatomic,  assign) double  netBuy;
@property (nonatomic, assign) double mainRatio;
@property (nonatomic, copy) NSString *induName;


@end

NS_ASSUME_NONNULL_END
