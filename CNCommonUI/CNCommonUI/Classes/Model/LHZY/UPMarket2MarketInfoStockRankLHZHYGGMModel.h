//
//  UPMarket2MarketInfoStockRankLHZHYGGMModel.h
//  UPMarket2
//
//  Created by liz<PERSON>xiang on 2023/8/17.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPMarket2MarketInfoStockRankLHZHYGGMModel : NSObject

@property (nonatomic, copy) NSString *stock_name;
@property (nonatomic, copy) NSString *code;
@property (nonatomic, assign) int marketCode;

@property (nonatomic, assign) int flag_monopolist_num;
@property (nonatomic, assign) int flag_big_num;
@property (nonatomic, assign) int flag_controlpanel_num;
@property (nonatomic, assign) double pct_chg;
@property (nonatomic, assign) double seat_net_buy;
@property (nonatomic, assign) int szzl;
@property (nonatomic, assign) int flag_over_num;
@property (nonatomic, assign) int flag_week_num;
@property (nonatomic, assign) int flag_multibuy_num;
@property (nonatomic, assign) double org_net_buy;
@property (nonatomic, assign) int flag_hotmoney_num;
@property (nonatomic, assign) int flag_strong_num;
@property (nonatomic, assign) int flag_explosive_num;
@end

NS_ASSUME_NONNULL_END
