//
//  UPDZXGZuHeModel.h
//  UPStockMain
//
//  Created by l<PERSON><PERSON><PERSON><PERSON> on 2023/8/24.
//  Copyright © 2023 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN

@interface UPDZXGZuHeDetailModel : NSObject

@property (nonatomic, assign) int detailId;
@property (nonatomic, copy) NSString *scence;
@property (nonatomic, copy) NSString *factorName;
@property (nonatomic, copy) NSString *displayName;
@property (nonatomic, assign) int dataType;
@property (nonatomic, copy) NSString *condition;
@property (nonatomic, assign) int conditionType;
@property (nonatomic, copy) NSString *conditionMultiple;


@end

@interface UPDZXGZuHeModel : NSObject

@property (nonatomic, assign) int zuheId;

@property (nonatomic, copy) NSString *name;
@property (nonatomic, copy) NSString *content;
@property (nonatomic, copy) NSString *userId;

@property (nonatomic, assign) int dateType;
@property (nonatomic, copy) NSString *condition;
@property (nonatomic, assign) int factorIds;
@property (nonatomic, copy) NSArray *factorList;


@end


NS_ASSUME_NONNULL_END
