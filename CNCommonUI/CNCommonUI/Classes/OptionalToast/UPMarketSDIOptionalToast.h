//
//  UPMarketSDIOptionalToast.h
//  UPMarket2
//
//  Created by j<PERSON><PERSON> on 2020/5/5.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger, UPMarketOptionStatus) {
    UPMarketOptionStatusAddSuccess = 0,
    UPMarketOptionStatusAddFail,
    UPMarketOptionStatusAddLimit,       // 自选股已达上限，添加失败
    UPMarketOptionStatusAddNameEmpty,   // 自选分组名称为空
    UPMarketOptionStatusAddGroupExist,  // 自选已经存在
    UPMarketOptionStatusDelSuccess,
    UPMarketOptionStatusDelFail
};

@protocol UPMarketSDIOptionalToastDelegate <NSObject>

- (void)sdiOptionalToastEditGroupClicked;

@end

@interface UPMarketSDIOptionalToast : NSObject

@property (nonatomic, weak) id<UPMarketSDIOptionalToastDelegate> delegate;

- (void)showInView:(UIView *)parentView groupList:(NSArray<NSNumber *> *)groupIds optionalStatus:(UPMarketOptionStatus)status;

@end

NS_ASSUME_NONNULL_END
