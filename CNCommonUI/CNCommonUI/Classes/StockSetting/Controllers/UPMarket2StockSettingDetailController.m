//
//  UPMarket2StockSettingDetailController.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/31.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDetailController.h"
#import "UPMarket2StockSettingDetailSectionCell.h"
#import "UPMarket2StockSettingDetailSliderCell.h"
#import "UPMarket2StockSettingDetailTipCell.h"
#import "UPMarket2StockSettingDetailDescCell.h"
#import "UPMarket2StockSettingDetailSwitchCell.h"
#import "UPMarket2StockSettingDetailDataSet.h"
#import "UPMarket2StockSettingDetailManager.h"
#import "UPMarket2StockSettingDetailStateManager.h"
#import "UPMarket2StockSettingDetailNaviView.h"

@interface UPMarket2StockSettingDetailController ()<UITableViewDelegate, UITableViewDataSource, UPMarket2StockSettingDetailSliderCellDelegate, UPMarket2StockSettingDetailSwitchCellDelegate, UPMarket2StockSettingDetailNaviViewDelegate>

@property (nonatomic, copy) NSString *typeTitle;
@property (nonatomic, strong) UPMarket2StockSettingDetailNaviView *naviView;

/// 是否可恢复设置
@property (nonatomic, assign, getter=isResettable) BOOL resettable;

@property (nonatomic, copy) NSArray<UPMarket2StockSettingDetailDataSet *> *dataSource;

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) UPMarket2StockSettingDetailDataBaseModel *baseModel;

@property (nonatomic, assign) BOOL stateChanged;

@property (nonatomic, strong) UIButton *resetBtn;

@end

@implementation UPMarket2StockSettingDetailController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.hidesNavigationBarWhenPush = YES;
    
    [self configData];
    
    [self setupUI];
    [self setupForDismissKeyboard];
    
    if (self.typeTitle) {
        self.naviView.typeTitle = self.typeTitle;
    }
    
    [self.naviView setResetable:self.isResettable];
    if (self.isResettable) {
        [self.naviView setResettableBtnEnable:![self.baseModel isDefaultValue]];
    }
}

- (void)configData {
    self.dataSource = [UPMarket2StockSettingDetailManager detailDatasWithType:self.type indexConfigInfo:self.indexConfigInfo];
    self.typeTitle = [UPMarket2StockSettingDetailManager typeTitleWithType:self.type indexConfigInfo:self.indexConfigInfo];
    self.resettable = [UPMarket2StockSettingDetailManager resettableWithType:self.type];
    if (self.indexConfigInfo) {
        self.baseModel = [UPMarket2StockSettingDetailStateManager dynamicModelForIndexId:self.indexConfigInfo.jce_indexDescInfo.jce_formulaID];
    }else{
        self.baseModel = [UPMarket2StockSettingDetailStateManager modelWithType:self.type];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];

    [self.view endEditing:YES];
    
    [UPMarket2StockSettingDetailStateManager saveModel:self.baseModel type:self.type];
    
    if (self.stateChangedBlock) {
        self.stateChangedBlock([self isChanged]);
    }
}

- (void)viewWillLayoutSubviews {
    [super viewWillLayoutSubviews];
    _naviView.frame = CGRectMake(0, 0, CGRectGetWidth(self.view.bounds), self.up_safeAreaInsets.top + 44);
    _tableView.frame = CGRectMake(0, CGRectGetMaxY(self.naviView.frame), CGRectGetWidth(self.view.bounds), CGRectGetHeight(self.view.bounds)-CGRectGetMaxY(self.naviView.frame));
}

- (BOOL)isChanged {
    for (UPMarket2StockSettingDetailDataSet *dataSet in self.dataSource) {
        if (dataSet.isChanged) {
            self.stateChanged = YES;
            break;
        }
    }
    return self.stateChanged;
}

- (void)setupUI {
    
    self.naviView = [[UPMarket2StockSettingDetailNaviView alloc] init];
    self.naviView.delegate = self;
    [self.view addSubview:self.naviView];
    [self.view addSubview:self.tableView];
}

- (void)resetBtnClick{
    // 重置状态
    [UPMarket2StockSettingDetailStateManager resetModel:self.baseModel type:self.type];
    // 重新获取数据源
    self.dataSource = [UPMarket2StockSettingDetailManager detailDatasWithType:self.type];
    //FIXME: 恢复默认设置认为状态有改变(可考虑更好的方式, 待优化)
    self.stateChanged = YES;
    // 刷新列表
    [self.tableView reloadData];
    
    [self.naviView setResettableBtnEnable:![self.baseModel isDefaultValue]];
}

- (void)dealloc {
    NSLog(@"%s", __func__);
    self.tableView.delegate = nil;
    self.tableView.dataSource = nil;
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setupForDismissKeyboard {
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tap:)];
    NSNotificationCenter *center = [NSNotificationCenter defaultCenter];
    NSOperationQueue *mainQueue = [NSOperationQueue mainQueue];
    WeakSelf(weakSelf);
    [center addObserverForName:UIKeyboardWillShowNotification object:nil queue:mainQueue usingBlock:^(NSNotification * _Nonnull note) {
        [weakSelf.view addGestureRecognizer:tap];
    }];
    [center addObserverForName:UIKeyboardWillHideNotification object:nil queue:mainQueue usingBlock:^(NSNotification * _Nonnull note) {
        [weakSelf.view removeGestureRecognizer:tap];
    }];
}

- (void)tap:(UITapGestureRecognizer *)recognizer {
    [self.view endEditing:YES];
}

/// MARK: - UPMarket2StockSettingDetailNaviViewDelegate
- (void)up_market2StockSettingDetailNaviView:(UPMarket2StockSettingDetailNaviView *)view didClickBackBtn:(UIButton *)btn {
    [self dismiss];
}

- (void)up_market2StockSettingDetailNaviView:(UPMarket2StockSettingDetailNaviView *)view didClickResetBtn:(UIButton *)btn {
    [self resetBtnClick];
}

/// MARK: Lazy loading
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataSource.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UPMarket2StockSettingDetailDataSet *dataSet = self.dataSource[indexPath.row];
    if (dataSet.cellStyle == UPMarket2StockSettingDetailStyleSection) {
        UPMarket2StockSettingDetailSectionCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingDetailSectionCell cellIdentifier] forIndexPath:indexPath];
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingDetailStyleTip) {
        UPMarket2StockSettingDetailTipCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingDetailTipCell cellIdentifier] forIndexPath:indexPath];
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingDetailStyleSlider) {
        UPMarket2StockSettingDetailSliderCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingDetailSliderCell cellIdentifier] forIndexPath:indexPath];
        cell.delegate = self;
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingDetailStyleDesc) {
        UPMarket2StockSettingDetailDescCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingDetailDescCell cellIdentifier] forIndexPath:indexPath];
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingDetailStyleSwitch) {
        UPMarket2StockSettingDetailSwitchCell *cell = [tableView dequeueReusableCellWithIdentifier:[UPMarket2StockSettingDetailSwitchCell cellIdentifier] forIndexPath:indexPath];
        cell.delegate = self;
        [cell configUIWithDataSet:dataSet];
        return cell;
    }
    else {
        NSAssert(NO, @"check dataSet cellStyle");
        return nil;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
	UPMarket2StockSettingDetailDataSet *dataSet = self.dataSource[indexPath.row];
    if (dataSet.cellStyle == UPMarket2StockSettingDetailStyleSection) {
        return UPWidth(46);
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingDetailStyleTip) {
        return UPWidth(30);
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingDetailStyleSlider) {
        return UPWidth(50);
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingDetailStyleDesc) {
        return UITableViewAutomaticDimension;
    }
    else if (dataSet.cellStyle == UPMarket2StockSettingDetailStyleSwitch) {
        return UPWidth(50);
    }
    else {
        return UPWidth(44);
    }
}

- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UPWidth(44);
}

/// MARK: UPMarket2StockSettingDetailSliderCellDelegate
- (void)up_market2StockSettingDetailSliderCell:(UPMarket2StockSettingDetailSliderCell *)cell valueChanged:(NSInteger)value {
    NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
    UPMarket2StockSettingDetailDataSet *dataSet = [self.dataSource objectAtIndex:indexPath.row];
    dataSet.sliderDataSet.value = value;
    [UPMarket2StockSettingDetailStateManager updateDataWithType:self.type model:self.baseModel dataSet:dataSet value:value];
    [self.naviView setResettableBtnEnable:![self.baseModel isDefaultValue]];
}

/// MARK: UPMarket2StockSettingDetailSwitchCellDelegate
- (void)up_market2StockSettingDetailSwitchCell:(UPMarket2StockSettingDetailSwitchCell *)cell switchOn:(BOOL)isOn {
    NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
    UPMarket2StockSettingDetailDataSet *dataSet = [self.dataSource objectAtIndex:indexPath.row];
    dataSet.switchDataSet.on = isOn;
    [UPMarket2StockSettingDetailStateManager updateDataWithType:self.type model:self.baseModel dataSet:dataSet enable:isOn];
    [self.naviView setResettableBtnEnable:![self.baseModel isDefaultValue]];
}

- (void)up_market2StockSettingDetailSwitchCell:(UPMarket2StockSettingDetailSwitchCell *)cell valueChanged:(NSInteger)value {
    NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
    UPMarket2StockSettingDetailDataSet *dataSet = [self.dataSource objectAtIndex:indexPath.row];
    dataSet.switchDataSet.value = value;
    [UPMarket2StockSettingDetailStateManager updateDataWithType:self.type model:self.baseModel dataSet:dataSet value:value];
    [self.naviView setResettableBtnEnable:![self.baseModel isDefaultValue]];
}

/// MARK: Lazy Loading
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:(UITableViewStylePlain)];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.tableFooterView = [[UIView alloc] init];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = UIColor.up_listBgColor;
        _tableView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;
        _tableView.separatorColor = UIColor.up_dividerColor;
        // register
        [_tableView registerClass:[UPMarket2StockSettingDetailSectionCell class] forCellReuseIdentifier:[UPMarket2StockSettingDetailSectionCell cellIdentifier]];
        [_tableView registerClass:[UPMarket2StockSettingDetailTipCell class] forCellReuseIdentifier:[UPMarket2StockSettingDetailTipCell cellIdentifier]];
        [_tableView registerClass:[UPMarket2StockSettingDetailSliderCell class] forCellReuseIdentifier:[UPMarket2StockSettingDetailSliderCell cellIdentifier]];
        [_tableView registerClass:[UPMarket2StockSettingDetailDescCell class] forCellReuseIdentifier:[UPMarket2StockSettingDetailDescCell cellIdentifier]];
        [_tableView registerClass:[UPMarket2StockSettingDetailSwitchCell class] forCellReuseIdentifier:[UPMarket2StockSettingDetailSwitchCell cellIdentifier]];
    }
    return _tableView;
}

/// MARK: - 处理横屏
- (BOOL)prefersStatusBarHidden {
    UIInterfaceOrientation interfaceOrientation = [[UIApplication sharedApplication] statusBarOrientation];
    if (interfaceOrientation == UIInterfaceOrientationLandscapeLeft || interfaceOrientation == UIInterfaceOrientationLandscapeRight) {
        return YES;
    }
    return NO;
}

-(BOOL)shouldAutorotate {
    return YES;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    
    UIInterfaceOrientation interfaceOrientation = [[UIApplication sharedApplication] statusBarOrientation];
    
    if (interfaceOrientation == UIInterfaceOrientationPortrait) {
        return UIInterfaceOrientationMaskPortrait;
    } else if (interfaceOrientation == UIInterfaceOrientationLandscapeLeft || interfaceOrientation == UIInterfaceOrientationLandscapeRight) {
        return UIInterfaceOrientationMaskLandscape;
    }
    return UIInterfaceOrientationMaskAll;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return UIInterfaceOrientationPortrait;
}

@end
