//
//  UPMarket2StepperView.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/27.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StepperView.h"

@interface UPMarket2StepperView ()

@property (nonatomic, strong) UIButton *reduceBtn;

@property (nonatomic, strong) UIView *leftSeperateLine;

@property (nonatomic, strong) UILabel *countLabel;

@property (nonatomic, strong) UIView *rightSeperateLine;

@property (nonatomic, strong) UIButton *addBtn;

@property (nonatomic, strong) UIView *maskView;

@end

@implementation UPMarket2StepperView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        self.value = 1;
        self.minValue = 1;
        self.maxValue = 4;
        self.stepValue = 1;
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.reduceBtn];
    [self addSubview:self.leftSeperateLine];
    [self addSubview:self.countLabel];
    [self addSubview:self.rightSeperateLine];
    [self addSubview:self.addBtn];
    [self addSubview:self.maskView];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self layoutContraints];
}

- (void)layoutContraints {
    // FIXME: -- 外部提供
    CGFloat btnWidth = UPWidth(50);
    CGFloat height = CGRectGetHeight(self.bounds);
    self.reduceBtn.frame = CGRectMake(0, 0, btnWidth, height);
    self.leftSeperateLine.frame = CGRectMake(CGRectGetMaxX(self.reduceBtn.frame), 0, 1, height);
    self.countLabel.frame = CGRectMake(CGRectGetMaxX(self.leftSeperateLine.frame), 0, CGRectGetWidth(self.bounds)-2*btnWidth-2, height);
    self.rightSeperateLine.frame = CGRectMake(CGRectGetMaxX(self.countLabel.frame), 0, 1, height);
    self.addBtn.frame = CGRectMake(CGRectGetMaxX(self.rightSeperateLine.frame), 0, btnWidth, height);
    
    self.layer.cornerRadius = height*0.5;
    self.layer.borderColor = UIColor.up_textSecondary2Color.CGColor;
    self.layer.borderWidth = 1;
    
    [self.maskView mas_makeConstraints:^(MASConstraintMaker *make) {
        [make edges];
    }];
}

- (void)btn_reduceClick:(UIButton *)btn {
    self.value -= self.stepValue;
    if (self.value <= self.minValue) {
        self.value = self.minValue;
        self.reduceBtn.enabled = NO;
    }
    self.addBtn.enabled = YES;
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StepperView:valueChanged:)]) {
        [self.delegate up_market2StepperView:self valueChanged:self.value];
    }
}

- (void)setDisable:(BOOL)disable
{
    _disable = disable;
    
    if (disable) {
        self.maskView.hidden = NO;
        self.alpha = 0.5;
    }else{
        self.maskView.hidden = YES;
        self.alpha = 1.0;
    }
    
}

- (void)btn_addClick:(UIButton *)btn {
    self.value += self.stepValue;
    if (self.value >= self.maxValue) {
        self.value = self.maxValue;
        self.addBtn.enabled = NO;
    }
    self.reduceBtn.enabled = YES;
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StepperView:valueChanged:)]) {
        [self.delegate up_market2StepperView:self valueChanged:self.value];
    }
}

- (void)setValue:(NSUInteger)value {
    _value = value;
    if (self.value >= self.maxValue) {
        self.addBtn.enabled = NO;
    } else {
        self.addBtn.enabled = YES;
    }
    
    if (self.value <= self.minValue) {
        self.reduceBtn.enabled = NO;
    } else {
        self.reduceBtn.enabled = YES;
    }

    self.countLabel.text = [NSString stringWithFormat:@"%zd", value];
}

    // lazy loading
- (UIButton *)reduceBtn {
    if (!_reduceBtn) {
        _reduceBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_reduceBtn setTitle:@"-" forState:UIControlStateNormal];
        [_reduceBtn setTitleColor:UIColor.up_textPrimaryColor forState:UIControlStateNormal];
        [_reduceBtn setTitleColor:UIColor.up_textSecondary1Color forState:UIControlStateDisabled];
        [_reduceBtn setTitleColor:UIColor.up_textSecondary1Color forState:UIControlStateHighlighted];
        [_reduceBtn addTarget:self action:@selector(btn_reduceClick:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _reduceBtn;
}

- (UIButton *)addBtn {
    if (!_addBtn) {
        _addBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_addBtn setTitle:@"+" forState:UIControlStateNormal];
        [_addBtn setTitleColor:UIColor.up_textPrimaryColor forState:UIControlStateNormal];
        [_addBtn setTitleColor:UIColor.up_textSecondary1Color forState:UIControlStateDisabled];
        [_addBtn setTitleColor:UIColor.up_textSecondary1Color forState:UIControlStateHighlighted];
        [_addBtn addTarget:self action:@selector(btn_addClick:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _addBtn;
}

- (UIView *)leftSeperateLine {
    if (!_leftSeperateLine) {
        _leftSeperateLine = [[UIView alloc] init];
        _leftSeperateLine.backgroundColor = UIColor.up_textSecondary2Color;
    }
    return _leftSeperateLine;
}

- (UIView *)rightSeperateLine {
    if (!_rightSeperateLine) {
        _rightSeperateLine = [[UIView alloc] init];
        _rightSeperateLine.backgroundColor = UIColor.up_textSecondary2Color;
    }
    return _rightSeperateLine;
}

- (UILabel *)countLabel {
    if (!_countLabel) {
        _countLabel = [[UILabel alloc] init];
        _countLabel.textAlignment = NSTextAlignmentCenter;
        _countLabel.textColor = UIColor.up_textPrimaryColor;
    }
    return _countLabel;
}


- (UIView *)maskView {
    if (!_maskView) {
        _maskView = [[UIView alloc] init];
        _maskView.userInteractionEnabled = YES;
        _maskView.hidden = YES;
    }
    return _maskView;
}

@end
