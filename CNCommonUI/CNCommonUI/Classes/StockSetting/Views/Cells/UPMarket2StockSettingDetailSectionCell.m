//
//  UPMarket2StockSettingDetailSectionCell.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/30.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDetailSectionCell.h"

@interface UPMarket2StockSettingDetailSectionCell ()

@property (nonatomic, strong) UIView *verticalLine;

@property (nonatomic, strong) UILabel *titleLabel;

@end

@implementation UPMarket2StockSettingDetailSectionCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

+ (NSString *)cellIdentifier {
    return NSStringFromClass(self);
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = UIColor.up_contentBgColor;
    [self.contentView addSubview:self.verticalLine];
    [self.contentView addSubview:self.titleLabel];
    [self layoutConstraints];
}

- (void)layoutConstraints {
    [self.verticalLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.contentView);
        make.height.mas_equalTo(UPWidth(20));
        make.width.mas_equalTo(UPWidth(3));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.verticalLine.mas_right).mas_offset(UPWidth(12));
    }];
}

- (void)configUIWithDataSet:(UPMarket2StockSettingDetailDataSet *)dataSet {
    if (dataSet.sectionDataSet) {
        self.titleLabel.text = dataSet.sectionDataSet.title;
    }
}

/// MARK: Lazy loading
- (UIView *)verticalLine {
    if (!_verticalLine) {
        _verticalLine = [[UIView alloc] init];
        _verticalLine.backgroundColor = UIColor.up_brandColor;
    }
    return _verticalLine;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.font = [UIFont up_fontOfSize:UPWidth(16)];
    }
    return _titleLabel;
}

@end
