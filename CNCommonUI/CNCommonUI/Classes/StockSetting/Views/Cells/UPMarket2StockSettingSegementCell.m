//
//  UPMarket2StockSettingSegementCell.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/27.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingSegementCell.h"
#import "UPMarket2SegmentView.h"

@interface UPMarket2StockSettingSegementCell ()<UPMarket2SegmentViewDelegate>
{
    UPMarket2StockSettingCellDataSet *_dataSet;
}

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UILabel *detailLabel;


@property (nonatomic, strong) UIButton *helpBtn;

@property (nonatomic, strong) UPMarket2SegmentView *segmentView;

@end

@implementation UPMarket2StockSettingSegementCell

+ (NSString *)cellIdentifier {
    return NSStringFromClass(self);
}

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
        self.backgroundColor = UIColor.up_contentBgColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)chooseCFQ {
    
}

- (void)setupUI {
    [self addSubview:self.titleLabel];
    [self.contentView addSubview:self.helpBtn];
    [self.contentView addSubview:self.segmentView];
    [self addSubview:self.detailLabel];

    [self layoutConstraints];
}

- (void)layoutConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.equalTo(self).mas_offset(UPWidth(15));
    }];
    
    [self.segmentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
        make.width.mas_equalTo(UPWidth(100));
        make.height.mas_equalTo(UPWidth(30));
    }];
    
    [self.detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.segmentView);
        make.top.equalTo(self.segmentView.mas_bottom).offset(UPHeight(2));
    }];
    
    [self.helpBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.segmentView.mas_left).mas_offset(-12);
        make.width.height.mas_equalTo(20);
    }];
}

- (void)configUIWithDataSet:(UPMarket2StockSettingCellDataSet *)dataSet {
    _dataSet = dataSet;
    self.titleLabel.text = dataSet.title;

    if (!dataSet.segmentDataSet) {
        return;
    }
    self.helpBtn.hidden = !dataSet.segmentDataSet.needHelp;
    
    self.segmentView.items = dataSet.segmentDataSet.items;
    self.segmentView.selectedSegmentIndex = dataSet.segmentDataSet.selectSegmentIndex;
    if ([dataSet.title isEqualToString:@"除复权"]) {
        if (dataSet.segmentDataSet.selectSegmentIndex == 0) {
            self.detailLabel.hidden = YES;
        } else {
            self.detailLabel.hidden = NO;

        }
    }
    NSUInteger itemsCount = dataSet.segmentDataSet.items.count;
    [self.segmentView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(itemsCount * dataSet.segmentDataSet.itemWidth);
    }];
}

- (void)btn_click:(UIButton *)btn {
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingSegementCell:didClickHelpBtn:)]) {
        [self.delegate up_market2StockSettingSegementCell:self didClickHelpBtn:btn];
    }
}

/// MARK: UPMarket2SegmentViewDelegate
- (void)upMarkett2SegementView:(UPMarket2SegmentView *)segementView didClickIndex:(NSInteger)index {
    if (_dataSet.segmentDataSet) {
        _dataSet.segmentDataSet.initialIndex = _dataSet.segmentDataSet.selectSegmentIndex;
        _dataSet.segmentDataSet.selectSegmentIndex = index;
        if (_dataSet.type == UPMarket2StockSettingTypeKLineCFQ) {
            if (index == 0) {
                self.detailLabel.hidden = YES;
            } else {
                self.detailLabel.hidden = NO;
            }
            
        }
    }
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingSegementCell:didClickSegementIndex:)]) {
        [self.delegate up_market2StockSettingSegementCell:self didClickSegementIndex:index];
    }
}

/// MARK: Lazy loading
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
    }
    return _titleLabel;
}

- (UILabel *)detailLabel {
    if (!_detailLabel) {
        _detailLabel = [[UILabel alloc] init];
        _detailLabel.textColor = [UIColor up_colorFromHexString:@"#FFFF9100"];
        _detailLabel.font = [UIFont up_fontOfSize:11];
        _detailLabel.text = @"建议修改为前复权，否则会造成指标走势畸变";
        _detailLabel.hidden = YES;
    }
    return _detailLabel;
}


- (UIButton *)helpBtn {
    if (!_helpBtn) {
        _helpBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_helpBtn setImage:UPTImg(@"指标设置/指标设置-问号") forState:UIControlStateNormal];
        [_helpBtn addTarget:self action:@selector(btn_click:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _helpBtn;
}

- (UPMarket2SegmentView *)segmentView {
    if (!_segmentView) {
        _segmentView  = [[UPMarket2SegmentView alloc] init];
        _segmentView.delegate = self;
    }
    return _segmentView;
}

@end
