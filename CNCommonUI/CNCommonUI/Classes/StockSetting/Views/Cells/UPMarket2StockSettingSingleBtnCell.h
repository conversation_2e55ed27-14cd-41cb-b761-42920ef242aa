//
//  UPMarket2StockSettingSingleBtnCell.h
//  UPMarket2
//
//  Created by 方恒 on 2020/3/27.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "UPMarket2StockSettingCellDataSet.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingSingleBtnCell;

@protocol UPMarket2StockSettingSingleBtnCellDelegate <NSObject>

- (void)up_market2StockSettingSingleBtnCell:(UPMarket2StockSettingSingleBtnCell *)cell didClickSingleBtn:(UIButton *)btn;

@optional
- (BOOL)up_market2StockSettingSingleSwitchCell:(UPMarket2StockSettingSingleBtnCell *)cell switchControlChangedOn:(BOOL)isOn;


@end

@interface UPMarket2StockSettingSingleBtnCell : UITableViewCell

@property (nonatomic, strong,readonly) UPMarket2StockSettingCellDataSet *dataSet;

+ (NSString *)cellIdentifier;

- (void)configUIWithDataSet:(UPMarket2StockSettingCellDataSet *)dataSet;

@property (nonatomic, weak) id<UPMarket2StockSettingSingleBtnCellDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
