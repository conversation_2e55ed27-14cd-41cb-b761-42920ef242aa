//
//  UPMarket2StockSettingSingleBtnCell.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/27.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingSingleBtnCell.h"
#import "UPMarket2StockSettingTagView.h"
#import "UPMarket2StockSettingManager.h"

@interface UPMarket2StockSettingSingleBtnCell ()

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UIButton *rightBtn;

@property (nonatomic, strong) UIImageView *vipImageview;

@property (nonatomic, strong) UPMarket2StockSettingCellDataSet *dataSet;

@property (nonatomic, strong) UIButton *switchButton;

@end

@implementation UPMarket2StockSettingSingleBtnCell

+ (NSString *)cellIdentifier {
    return NSStringFromClass(self);
}

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
        self.backgroundColor = UIColor.up_contentBgColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.titleLabel];
    [self.contentView addSubview:self.rightBtn];
    [self.contentView addSubview:self.switchButton];

    [self.contentView addSubview:self.vipImageview];
    [self layoutConstraints];
}

- (void)layoutConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.equalTo(self).mas_offset(UPWidth(15));
    }];
    [self.switchButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.contentView).mas_offset(-UPWidth(15));
        make.height.mas_equalTo(UPWidth(25));
        make.width.mas_equalTo(UPHeight(44));
    }];
    
    [self.rightBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.switchButton.mas_left).mas_offset(-16);

        make.width.mas_equalTo(20);
        make.height.mas_equalTo(20);
    }];
    [self.vipImageview mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.titleLabel);
        make.left.equalTo(self.titleLabel.mas_right).offset(2);
    }];
}



- (void)configUIWithDataSet:(UPMarket2StockSettingCellDataSet *)dataSet {
    self.dataSet = dataSet;
    self.titleLabel.text = dataSet.title;
    if (dataSet.singleBtnStyle == UPMarket2StockSettingSingleBtnStyleSet) {
        _rightBtn.hidden = NO;
    
        [_rightBtn setImage:UPTImg(@"指标设置/指标设置-设置") forState:UIControlStateNormal];
    } else if (dataSet.singleBtnStyle == UPMarket2StockSettingSingleBtnStyleHelp) {
        _rightBtn.hidden = NO;
        [_rightBtn setImage:UPTImg(@"指标设置/指标设置-问号") forState:UIControlStateNormal];
    } else{
        _rightBtn.hidden = YES;
    }
    
    BOOL hidden = [UPMarket2StockSettingManager.sharedInstance isHiddenForIndexId:dataSet.indexID];
    
    self.switchButton.selected = !hidden;
    if ([dataSet.title isEqualToString:@"分时走势"]) {
        self.switchButton.hidden = YES;
    }  else {
        self.switchButton.hidden = NO;
    }
    
    if (dataSet.showVip) {
        self.vipImageview.hidden = NO;
    }else{
        self.vipImageview.hidden = YES;
    }
}

- (void)btn_click:(UIButton *)btn {
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingSingleBtnCell:didClickSingleBtn:)]) {
        [self.delegate up_market2StockSettingSingleBtnCell:self didClickSingleBtn:btn];
    }
}

    /// MARK: Lazy loading
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
    }
    return _titleLabel;
}


- (UIButton *)rightBtn {
    if (!_rightBtn) {
        _rightBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _rightBtn.imageView.contentMode = UIViewContentModeScaleAspectFit;
        [_rightBtn addTarget:self action:@selector(btn_click:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _rightBtn;
}

- (UIImageView *)vipImageview
{
    if (!_vipImageview) {
        _vipImageview = [[UIImageView alloc] initWithImage:UPTImg(@"个股/icon-vip")];
    }
    return _vipImageview;
}

- (void)switchButtonChanged:(UIButton *)sender {
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingSingleSwitchCell:switchControlChangedOn:)]) {
       BOOL success =  [self.delegate up_market2StockSettingSingleSwitchCell:self switchControlChangedOn:!sender.isSelected];
        if (success) {
            sender.selected = !sender.selected;
        }else{
            [UPToastView show:@"最少保持开启一个指标"];
        }
    }
}

- (UIButton *)switchButton {
    if (!_switchButton) {
        _switchButton = [[UIButton alloc] init];
        [_switchButton setImage:UPTImg(@"指标设置/switchOff") forState:UIControlStateNormal];
        [_switchButton setImage:UPTImg(@"指标设置/switchOn") forState:UIControlStateSelected];
        [_switchButton addTarget:self action:@selector(switchButtonChanged:) forControlEvents:UIControlEventTouchUpInside];
    }
    
    return _switchButton;
}


@end
