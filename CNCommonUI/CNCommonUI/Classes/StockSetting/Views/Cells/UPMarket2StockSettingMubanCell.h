//
//  UPMarket2StockSettingMubanCell.h
//  UPMarket2
//
//  Created by lizhixiang on 2023/9/22.
//

#import <UIKit/UIKit.h>
#import "UPMarket2StockSettingCellDataSet.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingMubanCell;

@protocol UPMarket2StockSettingMubanCellDelegate <NSObject>

- (void)up_market2StockSettingMubanDeleteCell:(UPMarket2StockSettingMubanCell *)cell;

- (void)up_market2StockSettingMuBanRenameCell:(UPMarket2StockSettingMubanCell *)cell;


@end

@interface UPMarket2StockSettingMubanCell : UITableViewCell

@property (nonatomic, strong,readonly) UPMarket2StockSettingCellDataSet *dataSet;

+ (NSString *)cellIdentifier;

- (void)configUIWithDataSet:(UPMarket2StockSettingCellDataSet *)dataSet;

@property (nonatomic, weak) id<UPMarket2StockSettingMubanCellDelegate> delegate;

@end



NS_ASSUME_NONNULL_END
