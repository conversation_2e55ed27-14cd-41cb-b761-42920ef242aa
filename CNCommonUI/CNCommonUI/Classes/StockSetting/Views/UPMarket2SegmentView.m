//
//  UPMarket2SegmentView.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/27.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2SegmentView.h"

@implementation UPMarket2SegementAppearance

- (instancetype)init
{
    self = [super init];
    if (self) {
            // 设置默认属性
        _selectedBgColor = UIColor.upmarket2_segment_selected_bgColor;
        _normalBgColor = UIColor.upmarket2_segment_normal_bgColor;
        _selectedTextColor = UIColor.upmarket2_segment_selected_title_Color;
        _normalTextColor = UIColor.upmarket2_segment_normal_title_Color;
        _borderColor = UIColor.upmarket2_segment_border_color;
        _font = [UIFont up_fontOfSize:14];
        _dividerWidth = 1;
    }
    return self;
}

@end

@interface UPMarket2SegmentView ()

@property (nonatomic, copy) NSArray *itemBtnArr;
// 间隔线数组
@property (nonatomic, copy) NSArray *dividerArr;

@property (nonatomic, strong) UIView *wrapView;

@end

@implementation UPMarket2SegmentView

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

- (instancetype)initWithItems:(NSArray *)items appearance:(UPMarket2SegementAppearance *)appearance
{
    self = [super init];
    if (self) {
        _appearance = appearance;
        _items = items;
        [self setupUI];
    }
    return self;
}

- (void)setItems:(NSArray *)items {
    _items = items;
    
    if (nil == _appearance) {
        // 没有则使用默认的
        self.appearance = [[UPMarket2SegementAppearance alloc] init];
    }
    [self setupUI];
}

- (void)setAppearance:(UPMarket2SegementAppearance *)appearance {
    _appearance = appearance;
}

- (void)setupUI {
    self.wrapView = [[UIView alloc] init];
    [self addSubview:self.wrapView];
    
    [self createItemBtnArrAndDividerArr];
    
    self.wrapView.layer.cornerRadius = 3;
    self.wrapView.layer.masksToBounds = YES;
    
    self.backgroundColor = _appearance.borderColor;
//    self.layer.borderColor = _appearance.selectedBgColor.CGColor;
    self.layer.masksToBounds = YES;
//    self.layer.borderWidth = _appearance.dividerWidth;
    self.layer.cornerRadius = 4;
    self.selectedSegmentIndex = 0;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    [self layoutConstraints];
}

- (void)layoutConstraints {
    if (self.itemBtnArr.count == 0) {
        return;
    }
    
    self.wrapView.frame = CGRectMake(_appearance.dividerWidth, _appearance.dividerWidth, CGRectGetWidth(self.bounds) - 2 * _appearance.dividerWidth, CGRectGetHeight(self.bounds) - 2 * _appearance.dividerWidth);
    
    CGFloat itemBtnWidth = floor(CGRectGetWidth(self.wrapView.frame) - (self.dividerArr.count) * _appearance.dividerWidth) * 1.0 / self.itemBtnArr.count;
    CGFloat itemBtnHeight = floor(CGRectGetHeight(self.wrapView.frame));
    
    for (int i = 0; i < self.itemBtnArr.count; i++) {
        UIButton *itemBtn = self.itemBtnArr[i];
        itemBtn.frame = (CGRectMake(i * (itemBtnWidth + _appearance.dividerWidth), 0, itemBtnWidth, itemBtnHeight));
    }
    
    for (int i = 0; i < self.dividerArr.count; i++) {
        UIView *dividerView = self.dividerArr[i];
        dividerView.frame = (CGRectMake(itemBtnWidth + i * (itemBtnWidth + _appearance.dividerWidth), 0, _appearance.dividerWidth, itemBtnHeight));
    }
}

- (void)setSelectedSegmentIndex:(NSUInteger)selectedSegmentIndex {
    _selectedSegmentIndex = selectedSegmentIndex;
    
    if (_selectedSegmentIndex >= self.itemBtnArr.count) {
        return;
    }
    
    [self resetBtnState];
    
    UIButton *itemBtn = [self.itemBtnArr objectAtIndex:_selectedSegmentIndex];
    itemBtn.selected = YES;
    itemBtn.userInteractionEnabled = NO;
}

- (void)itemBtn_click:(UIButton *)btn {
    
    NSUInteger index = [self.itemBtnArr indexOfObject:btn];
    self.selectedSegmentIndex = index;
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(upMarkett2SegementView:didClickIndex:)]) {
        [self.delegate upMarkett2SegementView:self didClickIndex:index];
    }
}

- (void)resetBtnState {
    [self.itemBtnArr enumerateObjectsUsingBlock:^(UIButton *  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.selected = NO;
        obj.userInteractionEnabled = YES;
    }];
}

- (void)createItemBtnArrAndDividerArr {
    NSMutableArray *mutableItemBtnArr = [NSMutableArray arrayWithCapacity:_items.count];
    NSMutableArray *mutableDividerArr = [NSMutableArray array];

    for (int i = 0; i < _items.count; i++) {
        UIButton *itemBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [itemBtn setTitle:_items[i] forState:UIControlStateNormal];
        [itemBtn up_setBackgroundColor:_appearance.selectedBgColor forState:UIControlStateSelected];
        [itemBtn up_setBackgroundColor:_appearance.selectedBgColor forState:UIControlStateHighlighted];
        [itemBtn up_setBackgroundColor:_appearance.normalBgColor forState:UIControlStateNormal];
        [itemBtn setTitleColor:_appearance.selectedTextColor forState:UIControlStateSelected];
        [itemBtn setTitleColor:_appearance.selectedTextColor forState:UIControlStateHighlighted];
        [itemBtn setTitleColor:_appearance.normalTextColor forState:UIControlStateNormal];
        [itemBtn addTarget:self action:@selector(itemBtn_click:) forControlEvents:UIControlEventTouchUpInside];
        if (_appearance.font) {
            itemBtn.titleLabel.font = _appearance.font;
        }
        [mutableItemBtnArr addObject:itemBtn];
        [self.wrapView addSubview:itemBtn];
        if (i > 0) {
            UIView *dividerView = [[UIView alloc] init];
            [mutableDividerArr addObject:dividerView];
            [dividerView setBackgroundColor:_appearance.borderColor];
            [self.wrapView addSubview:dividerView];
        }
    }
    self.itemBtnArr = [NSArray arrayWithArray:mutableItemBtnArr];
    self.dividerArr = [NSArray arrayWithArray:mutableDividerArr];
}

@end
