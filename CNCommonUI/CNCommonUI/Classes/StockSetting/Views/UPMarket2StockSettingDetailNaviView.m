//
//  UPMarket2StockSettingDetailNaviView.m
//  UPMarket2
//
//  Created by 方恒 on 2020/6/22.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDetailNaviView.h"

@interface UPMarket2StockSettingDetailNaviView ()

@property (nonatomic, strong) UIButton *backBtn;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *resetBtn;

@end

@implementation UPMarket2StockSettingDetailNaviView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        [self layoutConstraints];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = UIColor.up_titleBarBgColor;
    
    _backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [_backBtn setImage:UPTImg(@"指标设置/指标设置-返回") forState:UIControlStateNormal];
    [_backBtn addTarget:self action:@selector(backBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    
    _titleLabel = [[UILabel alloc] init];
    _titleLabel.textColor = UIColor.up_textPrimaryColor;
    _titleLabel.font = [UIFont up_boldFontOfSize:UPWidth(19)];
    _titleLabel.textAlignment = NSTextAlignmentCenter;
    
    _resetBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [_resetBtn setTitle:@"恢复默认" forState:UIControlStateNormal];
    [_resetBtn setTitleColor:UIColor.up_textPrimaryColor forState:UIControlStateNormal];
    [_resetBtn setTitleColor:UIColor.upmarket2_stock_setting_reset_disable_Color forState:UIControlStateDisabled];
    _resetBtn.titleLabel.font = [UIFont up_fontOfSize:UPWidth(14)];
    [_resetBtn addTarget:self action:@selector(resetBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    
    
    [self addSubview:self.backBtn];
    [self addSubview:self.titleLabel];
    [self addSubview:self.resetBtn];
}

- (void)layoutConstraints {
    [self.backBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).mas_offset(12);
        make.width.height.mas_equalTo(30);
        make.bottom.equalTo(self).mas_offset(-7);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.centerY.equalTo(self.backBtn);
    }];
    
    [self.resetBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).mas_offset(-UPWidth(15));
        make.height.mas_equalTo(30);
        make.width.mas_equalTo(UPWidth(80));
        make.centerY.equalTo(self.backBtn);
    }];
}

- (void)setTypeTitle:(NSString *)typeTitle {
    _typeTitle = typeTitle;
    self.titleLabel.text = typeTitle;
}

- (void)setResetable:(BOOL)resetable {
    self.resetBtn.hidden = !resetable;
}

- (void)setResettableBtnEnable:(BOOL)enable {
    self.resetBtn.enabled = enable;
}

- (void)backBtnClicked:(UIButton *)btn {
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingDetailNaviView:didClickBackBtn:)]) {
        [self.delegate up_market2StockSettingDetailNaviView:self didClickBackBtn:btn];
    }
}

- (void)resetBtnClick:(UIButton *)btn {
    if (self.delegate && [self.delegate respondsToSelector:@selector(up_market2StockSettingDetailNaviView:didClickResetBtn:)]) {
        [self.delegate up_market2StockSettingDetailNaviView:self didClickResetBtn:btn];
    }
}

@end
