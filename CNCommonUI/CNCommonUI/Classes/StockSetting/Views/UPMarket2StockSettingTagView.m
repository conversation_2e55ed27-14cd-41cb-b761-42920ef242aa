//
//  UPMarket2StockSettingTagView.m
//  UPMarket2
//
//  Created by 方恒 on 2020/4/29.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingTagView.h"

@interface UPMarket2StockSettingTagView ()

@property (nonatomic, strong) NSString *title;
@property (nonatomic, strong) UIColor *color;


@end

@implementation UPMarket2StockSettingTagView

+ (instancetype)tagWithTitle:(NSString *)title tintColor:(UIColor *)color {
    UPMarket2StockSettingTagView *tagView = [[UPMarket2StockSettingTagView alloc] init];
    tagView.backgroundColor = [UIColor clearColor];
    [tagView drawWithTitle:title color:color];
    
    return tagView;
}

- (void)drawWithTitle:(NSString *)title color:(UIColor *)color {
    self.title = title;
    self.color = color;
    
    CGRect textRect = [title boundingRectWithSize:CGSizeMake(MAXFLOAT, 0)
                                          options:NSStringDrawingTruncatesLastVisibleLine | NSStringDrawingUsesLineFragmentOrigin |
                       NSStringDrawingUsesFontLeading
                                       attributes:@{
                                           NSFontAttributeName : [UIFont up_boldFontOfSize:8],
                                           NSForegroundColorAttributeName : [UIColor whiteColor]
                                       }
                                          context:nil];
    CGRect frame = self.frame;
    frame.size = CGSizeMake(textRect.size.width + 10, textRect.size.height + 3);
    self.frame = frame;
    [self setNeedsDisplay];
}

- (void)drawRect:(CGRect)rect {
    [super drawRect:rect];
    
    CGFloat width = self.frame.size.width;
    CGFloat height = self.frame.size.height;
    
    UIBezierPath *path = [UIBezierPath bezierPath];
    [path moveToPoint:CGPointMake(0, height)];
    
    [path addLineToPoint:CGPointMake(width - height / 2.f, height)];
    [path addArcWithCenter:CGPointMake(width - height / 2.f, height / 2.f)
                    radius:height / 2.f
                startAngle:M_PI_2
                  endAngle:M_PI * 1.5
                 clockwise:NO];
    [path addLineToPoint:CGPointMake(height / 2.f, 0.f)];
    [path addArcWithCenter:CGPointMake(height / 2.f, height / 2.f)
                    radius:height / 2.f
                startAngle:M_PI * 1.5
                  endAngle:M_PI
                 clockwise:NO];
    [path addLineToPoint:CGPointMake(0.f, height)];
    [path closePath];
    
    [self.color setFill];
    [path fill];
    
    CGRect textRect = CGRectMake(5, 1.5, width - 5, height - 3);
    [self.title drawInRect:textRect
            withAttributes:@{NSFontAttributeName : [UIFont up_boldFontOfSize:8], NSForegroundColorAttributeName : [UIColor whiteColor]}];
    
}

@end
