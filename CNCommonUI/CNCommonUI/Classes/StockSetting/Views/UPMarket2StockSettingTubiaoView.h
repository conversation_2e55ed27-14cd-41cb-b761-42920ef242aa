//
//  UPMarket2StockSettingTubiaoView.h
//  UPMarket2
//
//  Created by lizhixiang on 2023/3/17.
//

#import <UIKit/UIKit.h>
#import "UPMarket2StockSettingCellDataSet.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingTubiaoView;

@protocol UPMarket2StockSettingTubiaoViewDelegate <NSObject>

- (void)up_market2StockSettingTubiaoView:(UPMarket2StockSettingTubiaoView *)view didClickKLineType:(UPMarket2StockSettingType)type switchControlChangedOn:(BOOL)isOn;

@end


@interface UPMarket2StockSettingTubiaoView : UIView

@property (nonatomic, weak) id<UPMarket2StockSettingTubiaoViewDelegate> delegate;

@property (nonatomic, strong) NSArray *dataSetArr;

- (void)reloadData;

@property (nonatomic, assign, readonly) BOOL isChanged;

@end

NS_ASSUME_NONNULL_END
