//
//  UPMarket2StockSettingAlertView.m
//  UPMarket2
//
//  Created by 方恒 on 2020/4/13.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingAlertView.h"
#import <UPBaseUI/UPPopupView.h>

@interface UPMarket2StockSettingAlertView ()<UPPopupViewDelegate>

@property (nonatomic, strong) UPPopupView *alertPopupView;

@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *message;

@property (nonatomic, strong) NSAttributedString *attributedText;

@end

@implementation UPMarket2StockSettingAlertView

+ (instancetype)alertWithTitle:(NSString *)title message:(NSString *)message {
    return [[UPMarket2StockSettingAlertView alloc] initWithTitle:title message:message];
}

+ (instancetype)alertWithTitle:(NSString *)title attributedMessage:(NSAttributedString *)attributedText {
    UPMarket2StockSettingAlertView *alertView = [[UPMarket2StockSettingAlertView alloc] initWithTitle:title message:nil];
    alertView.attributedText = attributedText;
    
    return alertView;
}

- (instancetype)initWithTitle:(NSString *)title message:(NSString *)message
{
    self = [super init];
    if (self) {
        self.title = title;
        self.message = message;
    }
    return self;
}

- (void)showInView:(UIView *)view {
    NSAssert(NSThread.isMainThread, @"Must be called in main thread");
    
    if (self.alertPopupView.isShowing) {
        return;
    }
    
    if (view) {
        UIView *internalView = [self buildAlertView];
        
        self.alertPopupView.contentSize = internalView.bounds.size;
        self.alertPopupView.contentView = internalView;
        self.alertPopupView.context = self;
        
        CGPoint pos = CGPointMake(
                                  (view.bounds.size.width - self.alertPopupView.contentSize.width) / 2,
                                  (view.bounds.size.height - self.alertPopupView.contentSize.height) / 2);
        
        [self.alertPopupView showInView:view atPosition:pos];
    }
}

- (void)hidden {
    [self.alertPopupView hide];
}

- (UIView *)buildAlertView {
    UIView *alertView = [[UIView alloc] init];
    alertView.backgroundColor = UIColor.up_contentBgColor;
    alertView.layer.cornerRadius = UPWidth(12);
    alertView.up_width = UPWidth(280);
    if (self.alertHeight > 0) {
        alertView.up_height = self.alertHeight;
    } else {
        alertView.up_height = UPWidth(195);
    }
    
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.textColor = UIColor.up_textPrimaryColor;
    titleLabel.font = [UIFont up_boldFontOfSize:UPWidth(17)];
    titleLabel.text = self.title;
    
    UILabel *messageLabel = [[UILabel alloc] init];
    messageLabel.textColor = UIColor.up_textSecondaryColor;
    messageLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
    messageLabel.numberOfLines = 0;
    if (self.message) {
        messageLabel.text = self.message;
    }
    if (self.attributedText) {
        messageLabel.attributedText = self.attributedText;
    }
    
    UIView *seperateLine = [[UIView alloc] init];
    seperateLine.backgroundColor = UIColor.up_dividerColor;
    
    UIButton *sureBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [sureBtn setTitle:@"知道了" forState:UIControlStateNormal];
    [sureBtn setTitleColor:UIColor.up_brandColor forState:UIControlStateNormal];
    sureBtn.titleLabel.font = [UIFont up_fontOfSize:UPWidth(17)];
    [sureBtn addTarget:self action:@selector(btn_click:) forControlEvents:UIControlEventTouchUpInside];
    
    [alertView addSubview:titleLabel];
    [alertView addSubview:messageLabel];
    [alertView addSubview:seperateLine];
    [alertView addSubview:sureBtn];
    
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(alertView);
        make.top.equalTo(alertView).mas_offset(UPWidth(25));
    }];
    
    [messageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(alertView).mas_offset(UPWidth(20));
        make.right.equalTo(alertView).mas_offset(-UPWidth(20));
        make.top.equalTo(titleLabel.mas_bottom).mas_offset(UPWidth(20));
    }];
    
    [seperateLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(alertView);
        make.top.equalTo(messageLabel.mas_bottom).mas_offset(UPWidth(19));
        make.height.mas_equalTo(UPWidth(0.5));
    }];
    
    [sureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(alertView);
        make.top.equalTo(seperateLine.mas_bottom).mas_offset(0);
        make.bottom.equalTo(alertView);
        make.height.mas_equalTo(UPWidth(43));
    }];
    
    
    return alertView;
}

/// MARK: UPPopupViewDelegate
- (void)upPopupView:(UPPopupView *)popupView didHide:(BOOL)touchOutside {
    [self.alertPopupView.contentView removeFromSuperview];
    self.alertPopupView.contentView = nil;
    self.alertPopupView.context = nil;
}

- (void)btn_click:(UIButton *)btn {
    [self.alertPopupView hide];
}

/// MARK: Lazy loading
- (UPPopupView *)alertPopupView {
    if (!_alertPopupView) {
        _alertPopupView = [[UPPopupView alloc] init];
        _alertPopupView.delegate = self;
        _alertPopupView.hidesWhenTouchOutside = YES;
        _alertPopupView.backgroundColor = [UIColor colorWithRed:17/255.0 green:17/255.0 blue:17/255.0 alpha:0.5];
    }
    return _alertPopupView;
}

@end
