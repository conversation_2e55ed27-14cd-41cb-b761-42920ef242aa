//
//  UPMarket2StockSettingDetailNaviView.h
//  UPMarket2
//
//  Created by 方恒 on 2020/6/22.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingDetailNaviView;

@protocol UPMarket2StockSettingDetailNaviViewDelegate <NSObject>

- (void)up_market2StockSettingDetailNaviView:(UPMarket2StockSettingDetailNaviView *)view didClickBackBtn:(UIButton *)btn;
- (void)up_market2StockSettingDetailNaviView:(UPMarket2StockSettingDetailNaviView *)view didClickResetBtn:(UIButton *)btn;

@end

@interface UPMarket2StockSettingDetailNaviView : UIView

@property (nonatomic, weak) id<UPMarket2StockSettingDetailNaviViewDelegate> delegate;
@property (nonatomic, copy) NSString *typeTitle;

- (void)setResetable:(BOOL)resetable;
- (void)setResettableBtnEnable:(BOOL)enable;

@end

NS_ASSUME_NONNULL_END
