//
//  UPMarket2StockSettingKlineView.h
//  UPMarket2
//
//  Created by fang on 2020/3/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "UPMarket2StockSettingCellDataSet.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingKlineView;

@protocol UPMarket2StockSettingKlineViewDelegate <NSObject>

- (void)up_market2StockSettingKLineView:(UPMarket2StockSettingKlineView *)view didClickKLineType:(UPMarket2StockSettingType)type indexConfigInfo:(MarketIndicatorSysIndexConfigInfo *)indexConfigInfo;

- (void)up_market2StockSettingKLineView:(UPMarket2StockSettingKlineView *)view didClickKLineType:(UPMarket2StockSettingType)type atSegmentIndex:(NSInteger)index;

- (void)up_market2StockSettingKLineView:(UPMarket2StockSettingKlineView *)view didClickKLineType:(UPMarket2StockSettingType)type stepperValueChanged:(NSUInteger)value;

- (void)up_market2StockSettingKLineView:(UPMarket2StockSettingKlineView *)view didClickKLineType:(UPMarket2StockSettingType)type switchControlChangedOn:(BOOL)isOn indexId:(NSInteger)indexId;

@end

@interface UPMarket2StockSettingKlineView : UIView

@property (nonatomic, strong) UPMarketUIBaseModel *stockModel;

@property (nonatomic, weak) id<UPMarket2StockSettingKlineViewDelegate> delegate;

@property (nonatomic, assign) id indexHost;

@property (nonatomic, strong) id customConfig;

@property (nonatomic, strong) NSArray *dataSetArr;

@property (nonatomic, assign, readonly) BOOL isChanged;

@property (nonatomic, assign) BOOL isTS;

@property (nonatomic, assign) BOOL isIgnoreScroll;

- (void)reloadSettingWhere;

@end

NS_ASSUME_NONNULL_END
