//
//  UPMarket2StepperView.h
//  UPMarket2
//
//  Created by 方恒 on 2020/3/27.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPBaseUI/UPBaseView.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StepperView;

@protocol UPMarket2StepperViewDelegate <NSObject>

- (void)up_market2StepperView:(UPMarket2StepperView *)view valueChanged:(NSUInteger)value;

@end

@interface UPMarket2StepperView : UPBaseView

// default NO.
@property (nonatomic, assign) BOOL disable;
// default 1.
@property (nonatomic, assign) NSUInteger value;
// default 1. must be less than maximumValue
@property (nonatomic, assign) NSInteger minValue;
// default 3. must be greater than minimumValue
@property (nonatomic, assign) NSInteger maxValue;
// default 1. must be greater than 0
@property (nonatomic, assign) NSInteger stepValue;

@property (nonatomic, weak) id<UPMarket2StepperViewDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
