//
//  UPStockSettingFMDBHelper.m
//  UPMarket2
//
//  Created by lizhixiang on 2023/5/15.
//

#import "UPStockSettingFMDBHelper.h"
#import <UPUserSDK/UPUserSDK.h>

static NSString * const kStockSettingDBName = @"table_stocksetting";
static NSString * const kColumnID = @"_id";
static NSString * const kColumnSetCode = @"setCode";
static NSString * const kColumnCode = @"code";
static NSString * const kColumnCreateTime = @"createTime";
static NSString * const kColumnUID = @"uid";

#define UPDatabaseStockSettingVersion 1

@implementation UPStockSettingFMDBHelper


+ (instancetype)sharedInstance {
    static UPStockSettingFMDBHelper *database = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        database = [[UPStockSettingFMDBHelper alloc] initWithName:@"database_stocksetting.db" version:UPDatabaseStockSettingVersion];
    });
    return database;
}

- (BOOL)databaseOnCreate:(UPTAFFMDatabase *)db {
    return [self setupTable:db];
}




+ (void)addFoorMarks:(NSArray<UPMarket2StockSettingCellDataSet *> *)markModel {
    
}

+ (NSArray<UPMarket2StockSettingCellDataSet *> *)getFootMarks {
    return nil;
}

+ (void)updateStockSettingInfos:(void (^)(NSString * _Nonnull, BOOL))handle {
}

+ (NSString *)getUid {
    return [UPUserManager uid]?:@"";
}


// MARK: - Private
- (BOOL)setupTable:(UPTAFFMDatabase *)db {
    BOOL result = YES;
    
    if (![db tableExists:kStockSettingDBName]) {
        NSString *createSQL = [NSString stringWithFormat:@"CREATE TABLE IF NOT EXISTS %@ (%@ INTEGER PRIMARY KEY AUTOINCREMENT, %@ INTEGER, %@ TEXT, %@ DOUBLE, %@ TEXT)",
                               kStockSettingDBName,
                               kColumnID,
                               kColumnSetCode,
                               kColumnCode,
                               kColumnCreateTime,
                               kColumnUID];
        
        result = [db executeUpdate:createSQL];
        
        if (!result) {
            NSLog(@"创建数据库失败--%@", createSQL);
        }
        
        if (result) {
            NSString *indexSQL = [NSString stringWithFormat:@"CREATE UNIQUE INDEX IF NOT EXISTS footMark_uid_setcode_code ON %@ (%@, %@, %@)",
                                  kStockSettingDBName,
                                  kColumnUID,
                                  kColumnCode,
                                  kColumnSetCode];
            
            result = [db executeUpdate:indexSQL];
            
            if (!result) {
                NSLog(@"创建索引失败--%@", indexSQL);
            }
        }
    }
    
    return result;
}
@end
