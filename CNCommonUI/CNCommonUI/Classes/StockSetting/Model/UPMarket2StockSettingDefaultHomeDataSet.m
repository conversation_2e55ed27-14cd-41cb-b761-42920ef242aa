//
//  UPMarket2StockSettingDefaultHomeDataSet.m
//  UPMarket2
//
//  Created by 方恒 on 2020/3/31.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDefaultHomeDataSet.h"

@implementation UPMarket2StockSettingDefaultHomeDataSet

- (NSString *)title {
    switch (_type) {
        case UPMarket2StockSettingOtherDefaultHomeStateSmart:
            return @"智能";
        case UPMarket2StockSettingOtherDefaultHomeStateHome:
            return @"首页";
        case UPMarket2StockSettingOtherDefaultHomeStateMarket:
            return @"解盘";
//        case UPMarket2StockSettingOtherDefaultHomeStateTrade:
//            return @"交易";
//        case UPMarket2StockSettingOtherDefaultHomeStatePersonal:
//            return @"我的";
//        case UPMarket2StockSettingOtherDefaultHomeStateFinance:
//            return @"理财";
            
        case UPMarket2StockSettingOtherDefaultHomeStateChoose:
            return @"选股";
        case UPMarket2StockSettingOtherDefaultHomeStateStudy:
            return @"学苑";
        case UPMarket2StockSettingOtherDefaultHomeStateOptional:
            return @"自选";

            
        default:
            return @"";
    }
}

- (NSString *)detailTitle {
    switch (_type) {
        case UPMarket2StockSettingOtherDefaultHomeStateSmart:
            return @"(每日9:00~12:00,13:00~16:00主页为解盘，其他时间为首页)";
        default:
            return @"";
    }
}

@end
