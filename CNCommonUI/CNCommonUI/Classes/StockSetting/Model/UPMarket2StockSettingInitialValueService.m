//
//  UPMarket2StockSettingInitialValueService.m
//  UPMarket2
//
//  Created by 方恒 on 2020/4/2.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingInitialValueService.h"
#import "UPMarket2StockSettingStateManager.h"

@implementation UPMarket2StockSettingInitialValueService

+ (void)load {
    if ([self isFirstLoading]) {
        // 分时
        [UPMarket2StockSettingStateManager updateJhjjState:(UPMarket2StockSettingMinuteJHJJStateSmartOn)];
        [UPMarket2StockSettingStateManager updateWdwzPosition:(UPMarket2StockSettingMinuteWDWZPositionRight)];
        [UPMarket2StockSettingStateManager updateMinuteMinorChartNum:1];
        // k线
        [UPMarket2StockSettingStateManager updateCfqState:(UPMarket2StockSettingKLineCFQStateBefore)];
        [UPMarket2StockSettingStateManager updateKLinePattern:(UPMarket2StockSettingKLinePatternHollow)];
        [UPMarket2StockSettingStateManager updateKLineMinorChartNum:1];
        [UPMarket2StockSettingStateManager updateShowGap:NO];
        [UPMarket2StockSettingStateManager updateShowGL:YES];

        [UPMarket2StockSettingStateManager updateShowTradingPoint:YES];
        // 其他
        [UPMarket2StockSettingStateManager updateShowCostLine:YES];
        [UPMarket2StockSettingStateManager updateShowFlashEffect:YES];
        [UPMarket2StockSettingStateManager updateFastTrade:YES];
        [UPMarket2StockSettingStateManager updateSkinTheme:(UPMarket2StockSettingOtherSkinThemeSmart)];
        [UPMarket2StockSettingStateManager updateDefaultHomeState:(UPMarket2StockSettingOtherDefaultHomeStateHome)];
        //历史分时
        [UPMarket2StockSettingStateManager updateHistoryMinuteIndexMode:UPMarketUIMinorIndexVOL];
    }
}

// 首次安装
+ (BOOL)isFirstLoading {
    BOOL _isFirstLoading = [[NSUserDefaults standardUserDefaults] boolForKey:@"UPMarket2StockSettingInitialValueServiceIsFirstLoadingKey"] == NO;
    if (_isFirstLoading) {
        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"UPMarket2StockSettingInitialValueServiceIsFirstLoadingKey"];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
    return _isFirstLoading;
}

@end
