//
//  UPMarket2StockSettingDetailDataBaseModel.m
//  UPMarket2
//
//  Created by 方恒 on 2020/4/7.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDetailDataBaseModel.h"

@implementation UPMarket2StockSettingDetailDataBaseModel

- (void)resetDefaultValue {
    // 子类重写
}

- (BOOL)isDefaultValue {
    return NO;
}

@end

@interface UPMarket2StockSettingDetailDataDynamicModel()

@property (nonatomic, strong) UPMarketIndexParam *indexParam;

@end

@implementation UPMarket2StockSettingDetailDataDynamicModel

+ (instancetype)modelWithIndexParam:(UPMarketIndexParam *)indexParam
{
    UPMarket2StockSettingDetailDataDynamicModel *dynamicModel = [UPMarket2StockSettingDetailDataDynamicModel new];
    dynamicModel.indexParam = indexParam;
    return dynamicModel;
}

- (UPMarketIndexParam *)generateIndexParamForSelf
{
    UPMarketIndexParam *indexParam = [[UPMarketIndexParam alloc] initWithIndexId:self.indexId];
    indexParam.params = [NSMutableArray array];
    for (UPMarket2StockSettingDetailDataSet *dataSetEnum in self.dataSets) {
        UPMarketIndexParamSingle *single = [[UPMarketIndexParamSingle alloc] init];
        single.cacheValue = @(dataSetEnum.sliderDataSet.value);
        single.minValue = @(dataSetEnum.sliderDataSet.minValue);
        single.maxValue = @(dataSetEnum.sliderDataSet.maxValue);
        single.defaultValue = @(dataSetEnum.sliderDataSet.defaultValue);
        if (IsValidateString(dataSetEnum.sliderDataSet.unit)) {
            single.unit = dataSetEnum.sliderDataSet.unit;
        }
        [indexParam.params addObject:single];
    }
    return indexParam;
}

- (void)resetDefaultValue
{
    for (UPMarket2StockSettingDetailDataSet *dataSetEnum in self.dataSets) {
        dataSetEnum.sliderDataSet.value = dataSetEnum.sliderDataSet.defaultValue;
        [[NSUserDefaults standardUserDefaults] setObject:@(dataSetEnum.sliderDataSet.defaultValue) forKey:kKeyForIndexTag(dataSetEnum.indexTag)];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
}

- (BOOL)isDefaultValue
{
    BOOL isDefaultValue = YES;
    
    for (UPMarket2StockSettingDetailDataSet *dataSetEnum in self.dataSets) {
        if (dataSetEnum.sliderDataSet.value != dataSetEnum.sliderDataSet.defaultValue) {
            return NO;
        }
    }
    return YES;
}

- (void)updateDataSet:(NSUInteger)indexTag forValue:(NSInteger)value
{
    for (UPMarket2StockSettingDetailDataSet *dataSetEnum in self.dataSets) {
        if (dataSetEnum.indexTag == indexTag) {
            dataSetEnum.sliderDataSet.value = value;
            [[NSUserDefaults standardUserDefaults] setObject:@(value) forKey:kKeyForIndexTag(indexTag)];
            [[NSUserDefaults standardUserDefaults] synchronize];
        }
    }
}

- (void)setIndexParam:(UPMarketIndexParam *)indexParam
{
    _indexParam = indexParam;
    
    NSMutableArray *tmp = [NSMutableArray array];
    NSInteger idx = 0;
    for (UPMarketIndexParamSingle *single in indexParam.params) {
        UPMarket2StockSettingDetailDataSet *dataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
        dataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
        dataSet.indexTag = indexParam.indexId * 100 + idx;
        
        NSNumber *cacheValue = [[NSUserDefaults standardUserDefaults] objectForKey:kKeyForIndexTag(dataSet.indexTag)];
        UPMarket2StockSettingDetailSliderDataSet *slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
        slider.title = single.name;
        slider.value = cacheValue ? cacheValue.integerValue : single.defaultValue.integerValue;
        slider.initialValue = single.defaultValue.integerValue;
        slider.defaultValue = single.defaultValue.integerValue;
        slider.minValue = single.minValue.integerValue;
        slider.maxValue = single.maxValue.integerValue;
        if (IsValidateString(single.unit)) {
            slider.unit = single.unit;
        }
        dataSet.sliderDataSet = slider;
        [tmp addObject:dataSet];
        idx++;
    }
    self.dataSets = tmp.copy;
}

NSString *kKeyForIndexTag(NSInteger indexTag){
    return [NSString stringWithFormat:@"MarketIndexCacheTag_%zd",indexTag];
}

@end

@implementation UPMarket2StockSettingDetailDataMACDModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.diff1 = self.diff1Default;
        self.diff2 = self.diff2Default;
        self.dea = self.deaDefault;
    }
    return self;
}

- (void)resetDefaultValue {
    self.diff1 = self.diff1Default;
    self.diff2 = self.diff2Default;
    self.dea = self.deaDefault;
}

- (BOOL)isDefaultValue {
    return (self.diff1 == self.diff1Default && self.diff2 == self.diff2Default && self.dea == self.deaDefault);
}

- (NSInteger)diff1Default {
    return 12;
}

- (NSInteger)diff2Default {
    return 26;
}

- (NSInteger)deaDefault {
    return 9;
}

- (NSInteger)diff1Max {
    return 40;
}

- (NSInteger)diff1Min {
    return 5;
}

- (NSInteger)diff2Max {
    return 100;
}

- (NSInteger)diff2Min {
    return 10;
}

- (NSInteger)deaMax {
    return 40;
}

- (NSInteger)deaMin {
    return 2;
}

@end

@implementation UPMarket2StockSettingDetailDataBOLLModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.diff1 = self.diff1Default;
//        self.diff2 = 10;
    }
    return self;
}

- (void)resetDefaultValue {
    self.diff1 = self.diff1Default;
//    self.diff2 = 10;
}

- (BOOL)isDefaultValue {
    return (self.diff1 == self.diff1Default);
}

- (NSInteger)diff1Default {
    return 20;
}

- (NSInteger)diff1Min {
    return 5;
}

- (NSInteger)diff1Max {
    return 100;
}

//- (NSInteger)diff2Min {
//    return 1;
//}
//
//- (NSInteger)diff2Max {
//    return 10;
//}

@end

@implementation UPMarket2StockSettingDetailDataVOLModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.ma1 = self.ma1Default;
        self.ma2 = self.ma2Default;
        self.ma3 = self.ma3Default;
    }
    return self;
}

- (void)resetDefaultValue {
    self.ma1 = self.ma1Default;
    self.ma2 = self.ma2Default;
    self.ma3 = self.ma3Default;
}

- (BOOL)isDefaultValue {
    return (self.ma1 == self.ma1Default && self.ma2 == self.ma2Default && self.ma3 == self.ma3Default);
}

- (NSInteger)ma1Default {
    return 5;
}

- (NSInteger)ma2Default {
    return 10;
}

- (NSInteger)ma3Default {
    return 20;
}

- (NSInteger)ma1Max {
    return 250;
}

- (NSInteger)ma1Min {
    return 1;
}

- (NSInteger)ma2Max {
    return 250;
}

- (NSInteger)ma2Min {
    return 1;
}

- (NSInteger)ma3Max {
    return 250;
}

- (NSInteger)ma3Min {
    return 1;
}

@end

@implementation UPMarket2StockSettingDetailDataKDJModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.k = self.kDefault;
        self.d = self.dDefault;
        self.j = self.jDefault;
    }
    return self;
}

- (void)resetDefaultValue {
    self.k = self.kDefault;
    self.d = self.dDefault;
    self.j = self.jDefault;
}

- (BOOL)isDefaultValue {
    return (self.k == self.kDefault && self.d == self.dDefault && self.j == self.jDefault);
}

- (NSInteger)kDefault {
    return 9;
}

- (NSInteger)dDefault {
    return 3;
}

- (NSInteger)jDefault {
    return 3;
}

- (NSInteger)kMax {
    return 100;
}

- (NSInteger)kMin {
    return 1;
}

- (NSInteger)dMax {
    return 40;
}

- (NSInteger)dMin {
    return 2;
}

- (NSInteger)jMax {
    return 40;
}

- (NSInteger)jMin {
    return 2;
}

@end

@implementation UPMarket2StockSettingDetailDataRSIModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.rsi1 = self.rsi1Default;
        self.rsi2 = self.rsi2Default;
        self.rsi3 = self.rsi3Default;
    }
    return self;
}

- (void)resetDefaultValue {
    self.rsi1 = self.rsi1Default;
    self.rsi2 = self.rsi2Default;
    self.rsi3 = self.rsi3Default;
}

- (BOOL)isDefaultValue {
    return (self.rsi1 == self.rsi1Default && self.rsi2 == self.rsi2Default && self.rsi3 == self.rsi3Default);
}

- (NSInteger)rsi1Default {
    return 6;
}

- (NSInteger)rsi2Default {
    return 12;
}

- (NSInteger)rsi3Default {
    return 24;
}

- (NSInteger)rsi1Max {
    return 100;
}

- (NSInteger)rsi1Min {
    return 2;
}

- (NSInteger)rsi2Max {
    return 100;
}

- (NSInteger)rsi2Min {
    return 2;
}

- (NSInteger)rsi3Max {
    return 100;
}

- (NSInteger)rsi3Min {
    return 2;
}

@end

@implementation UPMarket2StockSettingDetailDataBIASModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.bias1 = self.bias1Default;
        self.bias2 = self.bias2Default;
        self.bias3 = self.bias3Default;
    }
    return self;
}

- (void)resetDefaultValue {
    self.bias1 = self.bias1Default;
    self.bias2 = self.bias2Default;
    self.bias3 = self.bias3Default;
}

- (BOOL)isDefaultValue {
    return (self.bias1 == self.bias1Default && self.bias2 == self.bias2Default && self.bias3 == self.bias3Default);
}

- (NSInteger)bias1Default {
    return 6;
}

- (NSInteger)bias2Default {
    return 12;
}

- (NSInteger)bias3Default {
    return 24;
}

- (NSInteger)bias1Max {
    return 100;
}

- (NSInteger)bias1Min {
    return 1;
}

- (NSInteger)bias2Max {
    return 100;
}

- (NSInteger)bias2Min {
    return 1;
}

- (NSInteger)bias3Max {
    return 100;
}

- (NSInteger)bias3Min {
    return 1;
}

@end

@implementation UPMarket2StockSettingDetailDataVRModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.vr1 = self.vr1Default;
    }
    return self;
}

- (void)resetDefaultValue {
    self.vr1 = self.vr1Default;
}

- (BOOL)isDefaultValue {
    return (self.vr1 == self.vr1Default);
}

- (NSInteger)vr1Default {
    return 26;
}

- (NSInteger)vr1Max {
    return 300;
}

- (NSInteger)vr1Min {
    return 5;
}

@end

@implementation UPMarket2StockSettingDetailDataCRModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cr1 = self.cr1Default;
    }
    return self;
}

- (void)resetDefaultValue {
    self.cr1 = self.cr1Default;
}

- (BOOL)isDefaultValue {
    return (self.cr1 == self.cr1Default);
}

- (NSInteger)cr1Default {
    return 26;
}

- (NSInteger)cr1Max {
    return 500;
}

- (NSInteger)cr1Min {
    return 5;
}

@end

@implementation UPMarket2StockSettingDetailDataWRModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.wr1 = self.wr1Default;
    }
    return self;
}

- (void)resetDefaultValue {
    self.wr1 = self.wr1Default;
}

- (BOOL)isDefaultValue {
    return (self.wr1 == self.wr1Default);
}

- (NSInteger)wr1Default {
    return 10;
}

- (NSInteger)wr1Max {
    return 100;
}

- (NSInteger)wr1Min {
    return 2;
}

@end

@implementation UPMarket2StockSettingDetailDataDMAModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.ddd1 = self.ddd1Default;
        self.ddd2 = self.ddd2Default;
        self.ama1 = self.ama1Default;
    }
    return self;
}

- (void)resetDefaultValue {
    self.ddd1 = self.ddd1Default;
    self.ddd2 = self.ddd2Default;
    self.ama1 = self.ama1Default;
}

- (BOOL)isDefaultValue {
    return (self.ddd1 == self.ddd1Default && self.ddd2 == self.ddd2Default && self.ama1 == self.ama1Default);
}

- (NSInteger)ddd1Default {
    return 10;
}

- (NSInteger)ddd2Default {
    return 50;
}

- (NSInteger)ama1Default {
    return 10;
}

- (NSInteger)ddd1Max {
    return 500;
}

- (NSInteger)ddd1Min {
    return 2;
}

- (NSInteger)ddd2Max {
    return 500;
}

- (NSInteger)ddd2Min {
    return 10;
}

- (NSInteger)ama1Max {
    return 500;
}

- (NSInteger)ama1Min {
    return 1;
}

@end

@implementation UPMarket2StockSettingDetailDataOBVModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.obv1 = self.obv1Default;
    }
    return self;
}

- (void)resetDefaultValue {
    self.obv1 = self.obv1Default;
}

- (BOOL)isDefaultValue {
    return (self.obv1 == self.obv1Default);
}

- (NSInteger)obv1Default {
    return 30;
}

- (NSInteger)obv1Max {
    return 100;
}

- (NSInteger)obv1Min {
    return 2;
}

@end

@implementation UPMarket2StockSettingDetailDataCCIModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.cci = self.cciDefault;
    }
    return self;
}

- (void)resetDefaultValue {
    self.cci = self.cciDefault;
}

- (BOOL)isDefaultValue {
    return (self.cci == self.cciDefault);
}

- (NSInteger)cciDefault {
    return 14;
}

- (NSInteger)cciMax {
    return 99;
}

- (NSInteger)cciMin {
    return 2;
}

@end

@implementation UPMarket2StockSettingDetailDataMAModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self resetDefaultValue];
    }
    return self;
}

- (void)resetDefaultValue {
    self.ma1 = self.ma1Default;
    self.ma2 = self.ma2Default;
    self.ma3 = self.ma3Default;
    self.ma4 = self.ma4Default;
    self.ma5 = self.ma5Default;
    self.ma1On = YES;
    self.ma2On = YES;
    self.ma3On = YES;
    self.ma4On = NO;
    self.ma5On = NO;
}

- (BOOL)isDefaultValue {
    return (self.ma1 == self.ma1Default &&
            self.ma2 == self.ma2Default &&
            self.ma3 == self.ma3Default &&
            self.ma4 == self.ma4Default &&
            self.ma5 == self.ma5Default &&
            self.ma1IsOn == YES &&
            self.ma2IsOn == YES &&
            self.ma3IsOn == YES &&
            self.ma4IsOn == NO &&
            self.ma5IsOn == NO);
}

- (NSInteger)ma1Default {
    return 5;
}

- (NSInteger)ma2Default {
    return 10;
}

- (NSInteger)ma3Default {
    return 20;
}

- (NSInteger)ma4Default {
    return 40;
}

- (NSInteger)ma5Default {
    return 60;
}

- (NSInteger)maMax {
    return 250;
}

- (NSInteger)maMin {
    return 1;
}

@end
