//
//  UPMarket2StockSettingDetailStateManager.h
//  UPMarket2
//
//  Created by 方恒 on 2020/4/7.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UPMarketUISDK/UPMarketUIIndexPeriod.h>
#import "UPMarket2StockSettingCellDataSet.h"
#import "UPMarket2StockSettingDetailDataSet.h"
#import "UPMarket2StockSettingDetailDataBaseModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface UPMarket2StockSettingDetailStateManager : NSObject

+ (UPMarket2StockSettingDetailDataBaseModel *)modelWithType:(UPMarket2StockSettingType)type;

+ (UPMarket2StockSettingDetailDataDynamicModel *)dynamicModelForIndexId:(NSInteger)indexId;

+ (void)saveModel:(UPMarket2StockSettingDetailDataBaseModel *)baseModel type:(UPMarket2StockSettingType)type;

+ (void)resetModel:(UPMarket2StockSettingDetailDataBaseModel *)baseModel type:(UPMarket2StockSettingType)type;

+ (void)updateDataWithType:(UPMarket2StockSettingType)type
                     model:(UPMarket2StockSettingDetailDataBaseModel *)baseModel
                   dataSet:(UPMarket2StockSettingDetailDataSet *)dataSet
                     value:(NSInteger)value;

/// 开关类型的数据
+ (void)updateDataWithType:(UPMarket2StockSettingType)type model:(UPMarket2StockSettingDetailDataBaseModel *)baseModel dataSet:(UPMarket2StockSettingDetailDataSet *)dataSet enable:(BOOL)isEnable;

@end

NS_ASSUME_NONNULL_END
