//
//  UPMarket2StockSettingDetailManager.h
//  UPMarket2
//
//  Created by 方恒 on 2020/4/1.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarket2StockSettingCellDataSet.h"

NS_ASSUME_NONNULL_BEGIN

@class UPMarket2StockSettingDetailDataSet;

@interface UPMarket2StockSettingDetailManager : NSObject

/// 通过类型获取设置详情页数据源
+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)detailDatasWithType:(UPMarket2StockSettingType)type;

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)detailDatasWithType:(UPMarket2StockSettingType)type
                                                       indexConfigInfo:(MarketIndicatorSysIndexConfigInfo *) indexConfigInfo;

/// 通过类型获取设置详情页导航栏title
+ (NSString *)typeTitleWithType:(UPMarket2StockSettingType)type;
+ (NSString *)typeTitleWithType:(UPMarket2StockSettingType)type
                indexConfigInfo:(MarketIndicatorSysIndexConfigInfo *) indexConfigInfo;

/// 通过类型获取是否可恢复设置状态
+ (BOOL)resettableWithType:(UPMarket2StockSettingType)type;

@end

NS_ASSUME_NONNULL_END
