//
//  UPMarket2StockSettingDetailManager.m
//  UPMarket2
//
//  Created by 方恒 on 2020/4/1.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMarket2StockSettingDetailManager.h"
#import "UPMarket2StockSettingDetailDataSet.h"
#import "UPMarket2StockSettingDetailStateManager.h"
#import <UPMarketUISDK/UPMarketUIIndexUtil.h>
#import <UPMarketIndex/UPMarketIndex.h>

@implementation UPMarket2StockSettingDetailManager

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)detailDatasWithType:(UPMarket2StockSettingType)type {
    return [self detailDatasWithType:type indexConfigInfo:nil];
}


+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)dynamicIndexDataSetsForIndexId:(UPMarketIndexId)indexId {
    
    // 参数设置
    UPMarket2StockSettingDetailDataSet *dynamicDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    dynamicDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *section = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    section.title = @"参数设置";
    dynamicDataSet.sectionDataSet = section;
    
    UPMarket2StockSettingDetailDataDynamicModel *dynamicModel = [UPMarket2StockSettingDetailStateManager dynamicModelForIndexId:indexId];
    
    MarketIndicatorSysIndexConfigInfo *marketIndexInfo = [UPMarketIndexManager.share indexConfigInfoWithId:indexId];
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = marketIndexInfo.jce_indexDescInfo.jce_desc;
    jtmsDataSet.descDataSet = jtmsDesc;
    
    
    NSMutableArray *arr = @[dynamicDataSet].mutableCopy;
    
    [arr addObjectsFromArray:dynamicModel.dataSets];
    
    if (IsValidateString(marketIndexInfo.jce_indexDescInfo.jce_desc) && ![marketIndexInfo.jce_indexDescInfo.jce_desc isEqualToString:@" "]) {
        [arr addObjectsFromArray:@[zbsmDataSet, jtmsDataSet]];
    }
    
    return arr.copy;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)detailDatasWithType:(UPMarket2StockSettingType)type
                                                       indexConfigInfo:(MarketIndicatorSysIndexConfigInfo *) indexConfigInfo{
    
    if (indexConfigInfo != nil) {
        
        if ([UPMarketIndexManager.share dynamicParamIndexTypeFromPlatformIndexId:indexConfigInfo.jce_indexDescInfo.jce_formulaID] !=UPMarketIndexDynamicTypeNone) {
            return [self dynamicIndexDataSetsForIndexId:indexConfigInfo.jce_indexDescInfo.jce_formulaID];
        }else{
            UPMarket2StockSettingDetailDataSet *indexIntroDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
            indexIntroDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
            UPMarket2StockSettingDetailSectionDataSet *introSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
            introSection.title = @"指标说明";
            indexIntroDataSet.sectionDataSet = introSection;
            
            // 具体描述
            UPMarket2StockSettingDetailDataSet *introDetailDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
            introDetailDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
            UPMarket2StockSettingDetailDescDataSet *introDetail = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
            introDetail.desc = indexConfigInfo.jce_indexDescInfo.jce_desc;
            //        jtmsDesc.strongTitleArr = @[@"VOL：",@"基本用法："];
            introDetailDataSet.descDataSet = introDetail;
            
            NSArray *arr = @[indexIntroDataSet, introDetailDataSet];
            
            return arr;
        }
    }
    
    switch (type) {
        case UPMarket2StockSettingTypeMinuteJHJJ:
            return [self jhjjDataSets];
        case UPMarket2StockSettingTypeMinuteVOL:
            return [self volDataSets];
        case UPMarket2StockSettingTypeMinuteMACD:
            return [self macdDataSets];
        case UPMarket2StockSettingTypeMinuteLB:
            return [self lbDataSets];
        case UPMarket2StockSettingTypeMinuteDDX:
            return [self ddxDataSets];
        case UPMarket2StockSettingTypeMinuteDDY:
            return [self ddyDataSets];
        case UPMarket2StockSettingTypeMinuteDDZ:
            return [self ddzDataSets];
        case UPMarket2StockSettingTypeMinuteZJBY:
            return [self zjbyDataSets];
        case UPMarket2StockSettingTypeKLineXSQK:
            return [self kLineXsqkDataSets];
        case UPMarket2StockSettingTypeKLineXSMMD:
            return [self kLineMmdDataSets];
        case UPMarket2StockSettingTypeKLineJX:
            return [self kLineJxDataSets];
        case UPMarket2StockSettingTypeKLineBOLL:
            return [self kLineBOLLDataSets];
        case UPMarket2StockSettingTypeKLineVOL:
            return [self kLineVOLDataSets];
        case UPMarket2StockSettingTypeKLineMACD:
            return [self kLineMACDDataSets];
        case UPMarket2StockSettingTypeKLineKDJ:
            return [self kLineKDJDataSets];
        case UPMarket2StockSettingTypeKLineRSI:
            return [self kLineRSIDataSets];
        case UPMarket2StockSettingTypeKLineBIAS:
            return [self kLineBIASDataSets];
        case UPMarket2StockSettingTypeKLineVR:
            return [self kLineVRDataSets];
        case UPMarket2StockSettingTypeKLineCR:
            return [self kLineCRDataSets];
        case UPMarket2StockSettingTypeKLineWR:
            return [self kLineWRDataSets];
        case UPMarket2StockSettingTypeKLineDMA:
            return [self kLineDMADataSets];
        case UPMarket2StockSettingTypeKLineOBV:
            return [self kLineOBVDataSets];
        case UPMarket2StockSettingTypeKLineCCI:
            return [self kLineCCIDataSets];
        case UPMarket2StockSettingTypeKLineDDX:
            return [self kLineDDXDataSets];
        case UPMarket2StockSettingTypeKLineDDY:
            return [self kLineDDYDataSets];
        case UPMarket2StockSettingTypeKLineDDZ:
            return [self kLineDDZDataSets];
        case UPMarket2StockSettingTypeOtherCCCBX:
            return [self otherCCCBXDataSets];
        case UPMarket2StockSettingTypeKLineZLJC:
            return [self kLineZLJCDataSets];
        case UPMarket2StockSettingTypeKLineSXNZ:
            return [self kLineSXNZDataSets];
        case UPMarket2StockSettingTypeKLineQLZZ:
            return [self kLineQLZZDataSets];
        case UPMarket2StockSettingTypeKLineGZZZ:
            return [self kLineGZZZDataSets];
        case UPMarket2StockSettingTypeKLineQSQB:
            return [self kLineQSQBDataSets];
        case UPMarket2StockSettingTypeKLineDNEH:
            return [self kLineDNEHDataSets];
        case UPMarket2StockSettingTypeKLineSJNX:
            return [self kLineSJNXDataSets];
        case UPMarket2StockSettingTypeKLineHXFK:
            return [self kLineHXFKDataSets];
        case UPMarket2StockSettingTypeKLineLTGD:
            return [self kLineLTGDDataSets];
        case UPMarket2StockSettingTypeKLineZQXY:
            return [self kLineZQXYDataSets];
        default:
            return @[];
    }
    
}

+ (NSString *)typeTitleWithType:(UPMarket2StockSettingType)type {
    return [self typeTitleWithType:type indexConfigInfo:nil];
}

+ (NSString *)typeTitleWithType:(UPMarket2StockSettingType)type
                indexConfigInfo:(MarketIndicatorSysIndexConfigInfo *)indexConfigInfo{
    if (indexConfigInfo != nil) {
        
        
        return indexConfigInfo.jce_indexDescInfo.jce_name;
    }
    
    switch (type) {
        case UPMarket2StockSettingTypeMinuteJHJJ:
            return @"集合竞价";
        case UPMarket2StockSettingTypeMinuteWDWZ:
            return @"五档位置";
        case UPMarket2StockSettingTypeMinuteFTSL:
            return @"副图数量";
        case UPMarket2StockSettingTypeMinuteVOL:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexVOL];
        case UPMarket2StockSettingTypeMinuteMACD:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexMACD];
        case UPMarket2StockSettingTypeMinuteLB:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexLB];
        case UPMarket2StockSettingTypeMinuteDDX:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexDDX];
        case UPMarket2StockSettingTypeMinuteDDY:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexDDY];
        case UPMarket2StockSettingTypeMinuteDDZ:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexDDZ];
        case UPMarket2StockSettingTypeMinuteZJBY:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexZJBY];
        case UPMarket2StockSettingTypeKLineCFQ:
            return @"除复权";
        case UPMarket2StockSettingTypeKLineYS:
            return @"K线样式";
        case UPMarket2StockSettingTypeKLineFTSL:
            return @"副图数量";
        case UPMarket2StockSettingTypeKLineXSQK:
            return @"跳空缺口";
        case UPMarket2StockSettingTypeKLineXSMMD:
            return @"买卖点";
        case UPMarket2StockSettingTypeKLineJX:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMajorIndexMALine];
        case UPMarket2StockSettingTypeKLineBOLL:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMajorIndexBOLL];
        case UPMarket2StockSettingTypeKLineVOL:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexVOL];
        case UPMarket2StockSettingTypeKLineMACD:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexMACD];
        case UPMarket2StockSettingTypeKLineKDJ:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexKDJ];
        case UPMarket2StockSettingTypeKLineRSI:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexRSI];
        case UPMarket2StockSettingTypeKLineBIAS:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexBIAS];
        case UPMarket2StockSettingTypeKLineVR:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexVR];
        case UPMarket2StockSettingTypeKLineCR:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexCR];
        case UPMarket2StockSettingTypeKLineWR:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexWR];
        case UPMarket2StockSettingTypeKLineDMA:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexDMA];
        case UPMarket2StockSettingTypeKLineOBV:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexOBV];
        case UPMarket2StockSettingTypeKLineCCI:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexCCI];
        case UPMarket2StockSettingTypeKLineDDX:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexDDX];
        case UPMarket2StockSettingTypeKLineDDY:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexDDY];
        case UPMarket2StockSettingTypeKLineDDZ:
            return [UPMarketUIIndexUtil getIndexFullName:UPMarketUIMinorIndexDDZ];
        case UPMarket2StockSettingTypeKLineSJNX:
            return @"四季牛熊";
        case UPMarket2StockSettingTypeKLineHXFK:
            return @"核心风口";
        case UPMarket2StockSettingTypeKLineLTGD:
            return @"龙头高度";
        case UPMarket2StockSettingTypeKLineZQXY:
            return @"赚钱效应";
        case UPMarket2StockSettingTypeKLineZLJC:
            return @"主力进出";
        case UPMarket2StockSettingTypeKLineSXNZ:
            return @"三线扭转";
        case UPMarket2StockSettingTypeKLineQLZZ:
            return @"擒龙追踪";
        case UPMarket2StockSettingTypeKLineGZZZ:
            return @"共振追涨";
        case UPMarket2StockSettingTypeKLineQSQB:
            return @"强势起爆";
        case UPMarket2StockSettingTypeKLineDNEH:
            return @"动能二号";
        case UPMarket2StockSettingTypeOtherCCCBX:
            return @"持仓成本线";
        case UPMarket2StockSettingTypeOtherZDXS:
            return @"比上笔涨跌显示";
        case UPMarket2StockSettingTypeOtherSDXD:
            return @"闪电下单";
        case UPMarket2StockSettingTypeOtherZTFD:
            return @"行情大字版";
        case UPMarket2StockSettingTypeOtherPFZT:
            return @"皮肤主题";
        case UPMarket2StockSettingTypeOtherSYSZ:
            return @"默认首页设置";

        default:
            return @"";
    }
}

+ (BOOL)resettableWithType:(UPMarket2StockSettingType)type {
    switch (type) {
        case UPMarket2StockSettingTypeMinuteMACD:
        case UPMarket2StockSettingTypeKLineJX:
        case UPMarket2StockSettingTypeKLineBOLL:
        case UPMarket2StockSettingTypeKLineVOL:
        case UPMarket2StockSettingTypeKLineMACD:
        case UPMarket2StockSettingTypeKLineKDJ:
        case UPMarket2StockSettingTypeKLineRSI:
        case UPMarket2StockSettingTypeKLineBIAS:
        case UPMarket2StockSettingTypeKLineVR:
        case UPMarket2StockSettingTypeKLineCR:
        case UPMarket2StockSettingTypeKLineWR:
        case UPMarket2StockSettingTypeKLineDMA:
        case UPMarket2StockSettingTypeKLineOBV:
        case UPMarket2StockSettingTypeKLineCCI:
//        case UPMarket2StockSettingTypeKLineZLJC:
//        case UPMarket2StockSettingTypeKLineSXNZ:
//        case UPMarket2StockSettingTypeKLineQLZZ:
//        case UPMarket2StockSettingTypeKLineGZZZ:
//        case UPMarket2StockSettingTypeKLineQSQB:
//        case UPMarket2StockSettingTypeKLineDNEH:
            return YES;
        default:
            return NO;
    }
}

/// MARK: - Private Minute
+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)jhjjDataSets {
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.title = @"集合竞价";
    jtmsDesc.imageNamed = @"指标设置/指标设置-集合竞价";
    jtmsDesc.desc = @"集合竞价是指在股票每个交易日上午9:15至9:25，交易所对这段时间内接收的买卖申报实行一次性集中撮合的竞价方式。\n\n"
    "图示说明\n"
    "1.分时图折线为股票匹配价的走势\n"
    "2.成交量上部柱线是未匹配量，下部柱线为匹配量。红色代表未匹配量在买方一侧，绿色代表未匹配量在卖方一侧。\n\n"
    "交易原则\n"
    "1.有效价格范围内选取成交量最大的价位；\n"
    "2.高于成交价格的买进申报与低于成交价格的卖出申报全部成交；\n"
    "3.与成交价格相同的买方或卖方至少一方全部成交。两个以上价位符合上述条件的，上海证券交易所规定使未成交量最小的申报价格为成交价格。若仍有两个以上申报价格符合条件，取其中间价为成交价格。深圳证券交易所取距前收盘价最近的价位为成交价。集合竞价的所有交易以同一价格成交。集合竞价未成交的部分，自动进入连续竞价。\n\n"
    "申报规定\n"
    "每个交易日9:15至9:25(深证包括9:15至9:25和14:57至15:00)，证券交易所交易主机接受参与竞价交易的申报。\n\n"
    "撤单规定\n"
    "在9:20之前，委托未成交，委托人可以变更和撤销委托；9:20之后委托不可撤单；若集合竞价的委托未成交，则在开盘后可以撤单。\n\n"
    "模式说明\n"
    "1.智能开启\n"
    "在每个交易日盘前9:15-9:30自动开启集合竞价，9:30之后自动关闭。\n"
    "2.保持开启\n"
    "始终在分时图上显示集合竞价\n"
    "3.保持关闭\n"
    "始终不显示集合竞价";
    jtmsDesc.strongTitleArr = @[@"图示说明", @"交易原则", @"申报规定", @"撤单规定", @"模式说明"];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)volDataSets {
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"VOL：\n"
    "是指在个股即时走势图中股票的买卖成交量。\n\n"
    "基本用法：\n"
    "1.成交量大，代表交投热络，可界定为热门股。\n"
    "2.底部起涨点出现大成交量（成交手数），代表攻击量。\n"
    "3.头部地区出现大成交量（成交手数），代表出货量。\n"
    "4.观察成交金额的变化，比观察成交手数更具意义，因为成交手数并未反应股价的涨跌的后所应支出的实际金额。";
    jtmsDesc.strongTitleArr = @[@"VOL：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)macdDataSets {
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    // DIFF
    UPMarket2StockSettingDetailDataSet *diffDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    diffDataSet.cellStyle = UPMarket2StockSettingDetailStyleTip;
    UPMarket2StockSettingDetailTipDataSet *diffTip = [[UPMarket2StockSettingDetailTipDataSet alloc] init];
    diffTip.title = @"DIFF：收盘价短期水平与长期平滑移动平均值的差";
    diffDataSet.tipDataSet = diffTip;
    
    UPMarket2StockSettingDetailDataMACDModel *macd = (UPMarket2StockSettingDetailDataMACDModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeMinuteMACD)];
    // 短期
    UPMarket2StockSettingDetailDataSet *dqDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    dqDataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    dqDataSet.indexTag = UPMarket2StockSettingDetailDataMACDIndexTagDiff1;
    UPMarket2StockSettingDetailSliderDataSet *dqSlider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    dqSlider.title = @"短期";
    dqSlider.value = macd.diff1;
    dqSlider.initialValue = macd.diff1;
    dqSlider.defaultValue = macd.diff1Default;
    dqSlider.unit = @"分钟";
    dqSlider.minValue = macd.diff1Min;
    dqSlider.maxValue = macd.diff1Max;
    dqDataSet.sliderDataSet = dqSlider;
    
    // 长期
    UPMarket2StockSettingDetailDataSet *cqDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    cqDataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    cqDataSet.indexTag = UPMarket2StockSettingDetailDataMACDIndexTagDiff2;
    UPMarket2StockSettingDetailSliderDataSet *cqSlider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    cqSlider.title = @"长期";
    cqSlider.value = macd.diff2;
    cqSlider.initialValue = macd.diff2;
    cqSlider.defaultValue = macd.diff2Default;
    cqSlider.unit = @"分钟";
    cqSlider.minValue = macd.diff2Min;
    cqSlider.maxValue = macd.diff2Max;
    cqDataSet.sliderDataSet = cqSlider;
    
    // DEA
    UPMarket2StockSettingDetailDataSet *deaDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    deaDataSet.cellStyle = UPMarket2StockSettingDetailStyleTip;
    UPMarket2StockSettingDetailTipDataSet *deaTip = [[UPMarket2StockSettingDetailTipDataSet alloc] init];
    deaTip.title = @"DEA：DIFF的M分钟平滑移动平均值";
    deaDataSet.tipDataSet = deaTip;
    
    // M
    UPMarket2StockSettingDetailDataSet *mDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    mDataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    mDataSet.indexTag = UPMarket2StockSettingDetailDataMACDIndexTagDea;
    UPMarket2StockSettingDetailSliderDataSet *mSlider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    mSlider.title = @"M";
    mSlider.value = macd.dea;
    mSlider.initialValue = macd.dea;
    mSlider.defaultValue = macd.deaDefault;
    mSlider.unit = @"分钟";
    mSlider.minValue = macd.deaMin;
    mSlider.maxValue = macd.deaMax;
    mDataSet.sliderDataSet = mSlider;
    
    // MACD
    UPMarket2StockSettingDetailDataSet *macdDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    macdDataSet.cellStyle = UPMarket2StockSettingDetailStyleTip;
    UPMarket2StockSettingDetailTipDataSet *macdTip = [[UPMarket2StockSettingDetailTipDataSet alloc] init];
    macdTip.title = @"MACD：2倍的(DIFF-DEA)";
    macdDataSet.tipDataSet = macdTip;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"MACD：\n"
    "DIFF线是分钟收盘价短期、长期指数平滑移动平均线的差；DEA线是DIFF线的M分钟指数平滑移动平均线；MACD线是DIFF线与DEA线的差，彩色柱状线。\n\n"
    "基本用法：\n"
    "1.DIF、DEA均为正，DIF向上突破DEA，买入信号参考。\n"
    "2.DIF、DEA均为负，DIF向下跌破DEA，卖出信号参考。\n"
    "3.DIF线与K线发生背离，行情可能出现反转信号。\n"
    "4.DIF、DEA的值从正数变成负数，或者从负数变成正数并不是交易信号，因为它们落后于市场。";
    jtmsDesc.strongTitleArr = @[@"MACD：", @"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, diffDataSet, dqDataSet, cqDataSet, deaDataSet, mDataSet, macdDataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)lbDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"量比：\n"
    "是衡量相对成交量的指标。它是开市后每分钟的平均成交量与过去5个交易日每分钟平均成交量之比。其计算公式为：量比=现成交手数/[(过去5个交易日平均每分钟成交量)×当日累计开市时间(分)]。\n\n"
    "基本用法：\n"
    "1.若是突然出现放量，量比指标图会有一个向上突破，越陡说明放量越大（刚开市时可忽略不计）。若出现缩量，量比指标会向下走。\n"
    "2.当量比大于1时，说明当日每分钟的平均成交量大于过去5日的平均值，成交放大；交易比过去5日火爆。\n"
    "3.当量比小于1时，说明当日成交量小于过去5日的平均水平，成交萎缩。";
    jtmsDesc.strongTitleArr = @[@"量比：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)ddxDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"DDX：\n"
    "分时DDX（分时大单动向）指标是基于交易所Level-2的逐单数据，是一个短中线兼顾的技术指标。DDX红绿柱线表示当日大单买入净量占流通盘的百分比(估计值)，红柱表示大单买入量较大，绿柱表示大单卖出量较大、DDX累计是全天特、大单净流入的累计值。\n\n"
    "基本用法：\n"
    "1.如果红绿柱线为红色，表示时点的大单买入量较大，反之如果红绿柱线为绿色表示大单卖出较多。\n"
    "2.DDX累计线向上，说明当天主力资金持续流入。\n"
    "3.DDX累计线向下，说明当天主力资金持续流出。\n"
    "4.重点关注分时上的主力资金线支持向上运行且资金流向联系飘红的交易机会。";
    jtmsDesc.strongTitleArr = @[@"DDX：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)ddyDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"DDY：\n"
    "分时DDY（分时涨跌动因）指标是每分钟卖出单数和买入单数的差值占笔数化流通盘的比例。笔数化流通盘=流通盘/一段时间的平均每单成交手数。该指标判断主力对未来价格走势的态度，属于先行指标。\n\n"
    "基本用法：\n"
    "1.如果红绿柱线为红色表示时点单数差为正，大单买入较多，反之如果红绿柱线为绿色表示时点单数差为负，大单卖出较多。\n"
    "2.DDY累计为全天DDY的累计值，数值越大，反应散户卖出比例越高。\n"
    "3.分时涨跌动因具有极大的超前性，因为筹码的收集和发散都有一个过程。股价尽管还沿着原来的趋势运行，但筹码转移的方向已经逆转。\n"
    "4.DDY累计数值越高代表当日出货散户人数比例越大，这个值一方面可以和DDX相互验证，另一方面，可发现主力悄悄建仓的股票。";
    jtmsDesc.strongTitleArr = @[@"DDY：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)ddzDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"DDZ：\n"
    "分时DDZ（分时大单差分）是大单质量的评价指标，反映大资金的实力。有些多空争斗比较激烈的股票，买卖双方的买卖量都很大，DDZ的作用在于衡量买卖双方大单的力度，这种股票对于大盘股或机构分歧较大的股票比较有效，当然对于多方主力占绝对优势的股票更容易排行靠前。\n\n"
    "基本用法：\n"
    "1.红色波浪代表大资金的买入强度，波浪越高、持续时间越长表示买入强度越大。当波浪突然升高往往预示短线将快速上涨。\n"
    "2.绿色波浪代表大资金卖出强度，波浪越高、持续时间越长表示卖出强度越大。当波浪突然下降往往预示短线将快速下跌。\n"
    "3.该指标综合了DDX和DDY的优点，对大单的动作敏感性更强。";
    jtmsDesc.strongTitleArr = @[@"DDZ：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)zjbyDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"资金博弈：\n"
    "分时资金博弈，以L2逐单数据分析为基础，全面剖析不同规模资金的进出情况。资金按逐笔交易量规模分为超大、大单、中单和小单四挡，四类资金在同一个界面下进行展示。方便用户看出股票的主力类型及买卖方向。\n\n"
    "基本用法：\n"
    "1.当特大、大单资金线向上运行并与小单、中单资金线收窄，表示有主力资金在流入，可重点关注。\n"
    "2.当特大资金线或大单资金线上穿小单、中单资金线时，表示主力资金实现控盘，股价向上突破几率较大。\n"
    "3.当特大资金线或大单资金线下穿小单、中单资金线时，表示主力资金在流出，股价向下几率大。";
    jtmsDesc.strongTitleArr = @[@"资金博弈：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

#pragma mark - KLine

/// k线设置相关
+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineXsqkDataSets {
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"跳空缺口：\n"
    "是指相邻二根k线之间出现没有交易的空白价格区域，跳空缺口是一种强烈趋势信号。向上跳空，表示强烈的上涨趋势；向下跳空，表示强烈的下降趋势。\n\n"
    "基本用法：\n"
    "1、向下突破缺口的出现，说明市场已发生逆转，原来的升势结束，接下来是一轮跌势，下跌的空间一般会很大。因此见到向下突破缺口时应及时看空，尽量做到退出观望为宜；向下持续缺口的出现，说明市场做空力量仍很强，股价还将继续下跌。\n\n"
    "2、向上跳空缺口出现伴有较大的成交量时，成交量越大，说明日后股价上升的潜力越大。向上突破缺口形成后，如几天内不被封闭，说明多方占优势，股价将形成一路攀升的走势。在股价，上升时出现第二个缺口是向上持续缺口，股价仍然会沿着上涨势头持续下去。向上持续缺口的出现，表明多方力量十分强大。\n\n"
    "3.若是向上或向下跳空缺口当天就及时回补的现象对后市分析意义不大。";
    jtmsDesc.strongTitleArr = @[@"跳空缺口：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineMmdDataSets {
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.imageNamed = @"指标设置/指标设置-买卖点";
    jtmsDesc.desc = @"K线历史买卖点：\n"
    "将个股的历史操作记录显示在K线图上，B代表买入，S代表卖出，T代表有买有卖。\n\n"
    "1.若未登录则不显示，目前仅支持A股交易账户；\n"
    "2.历史成交只有买“B”时：\n"
    "   买入均价＜收盘价时：B在K线下方；\n"
    "   买入均价＞收盘价时：B在K线上方；\n"
    "   买入均价＝收盘价时：若股价上涨，B在K线上方，若股价下跌或不变，B在K线下方。\n"
    "3.历史成交只有卖“S”时：\n"
    "   卖出均价＜收盘价时：S在K线下方；\n"
    "   卖出均价＞收盘价时：S在K线上方；\n"
    "   卖出均价＝收盘价时：若股价上涨，S在K线上方，若股价下跌或不变，S在K线下方。\n"
    "4.历史成交有买也有卖“T”时：\n"
    "   买入均价＜卖出均价时， T显示在K线下方；\n"
    "   买入均价＞卖出均价时， T显示在K线上方；\n"
    "   买入均价＝卖出均价时， T显示在K线上方。\n"
    "5.当日成交：当日成交（B或S或T）均默认显示在K线上方，如上方显示不下，则显示在K线下方。";
    jtmsDesc.strongTitleArr = @[@"K线历史买卖点："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineJxDataSets {
    
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    UPMarket2StockSettingDetailDataMAModel *maModel = (UPMarket2StockSettingDetailDataMAModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineJX)];
    
    // 5日均线
    UPMarket2StockSettingDetailDataSet *jx5DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jx5DataSet.cellStyle = UPMarket2StockSettingDetailStyleSwitch;
    jx5DataSet.indexTag = UPMarket2StockSettingDetailDataMAIndexTagMa1;
    UPMarket2StockSettingDetailSwitchDataSet *jx5Switch = [[UPMarket2StockSettingDetailSwitchDataSet alloc] init];
    jx5Switch.value = maModel.ma1;
    jx5Switch.initialValue = maModel.ma1;
    jx5Switch.defaultValue = maModel.ma1Default;
    jx5Switch.unit = @"日均线";
    jx5Switch.on = maModel.ma1IsOn;
    jx5Switch.initialOn = maModel.ma1IsOn;
    jx5Switch.minValue = maModel.maMin;
    jx5Switch.maxValue = maModel.maMax;
    jx5DataSet.switchDataSet = jx5Switch;
    
    // 10日均线
    UPMarket2StockSettingDetailDataSet *jx10DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jx10DataSet.cellStyle = UPMarket2StockSettingDetailStyleSwitch;
    jx10DataSet.indexTag = UPMarket2StockSettingDetailDataMAIndexTagMa2;
    UPMarket2StockSettingDetailSwitchDataSet *jx10Switch = [[UPMarket2StockSettingDetailSwitchDataSet alloc] init];
    jx10Switch.value = maModel.ma2;
    jx10Switch.initialValue = maModel.ma2;
    jx10Switch.defaultValue = maModel.ma2Default;
    jx10Switch.unit = @"日均线";
    jx10Switch.on = maModel.ma2IsOn;
    jx10Switch.initialOn = maModel.ma2IsOn;
    jx10Switch.minValue = maModel.maMin;
    jx10Switch.maxValue = maModel.maMax;
    jx10DataSet.switchDataSet = jx10Switch;
    
    // 20日均线
    UPMarket2StockSettingDetailDataSet *jx20DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jx20DataSet.cellStyle = UPMarket2StockSettingDetailStyleSwitch;
    jx20DataSet.indexTag = UPMarket2StockSettingDetailDataMAIndexTagMa3;
    UPMarket2StockSettingDetailSwitchDataSet *jx20Switch = [[UPMarket2StockSettingDetailSwitchDataSet alloc] init];
    jx20Switch.value = maModel.ma3;
    jx20Switch.initialValue = maModel.ma3;
    jx20Switch.defaultValue = maModel.ma3Default;
    jx20Switch.unit = @"日均线";
    jx20Switch.on = maModel.ma3IsOn;
    jx20Switch.initialOn = maModel.ma3IsOn;
    jx20Switch.minValue = maModel.maMin;
    jx20Switch.maxValue = maModel.maMax;
    jx20DataSet.switchDataSet = jx20Switch;
    
    // 40日均线
    UPMarket2StockSettingDetailDataSet *jx40DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jx40DataSet.cellStyle = UPMarket2StockSettingDetailStyleSwitch;
    jx40DataSet.indexTag = UPMarket2StockSettingDetailDataMAIndexTagMa4;
    UPMarket2StockSettingDetailSwitchDataSet *jx40Switch = [[UPMarket2StockSettingDetailSwitchDataSet alloc] init];
    jx40Switch.value = maModel.ma4;
    jx40Switch.initialValue = maModel.ma4;
    jx40Switch.defaultValue = maModel.ma4Default;
    jx40Switch.unit = @"日均线";
    jx40Switch.on = maModel.ma4IsOn;
    jx40Switch.initialOn = maModel.ma4IsOn;
    jx40Switch.minValue = maModel.maMin;
    jx40Switch.maxValue = maModel.maMax;
    jx40DataSet.switchDataSet = jx40Switch;
    
    // 60日均线
    UPMarket2StockSettingDetailDataSet *jx60DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jx60DataSet.cellStyle = UPMarket2StockSettingDetailStyleSwitch;
    jx60DataSet.indexTag = UPMarket2StockSettingDetailDataMAIndexTagMa5;
    UPMarket2StockSettingDetailSwitchDataSet *jx60Switch = [[UPMarket2StockSettingDetailSwitchDataSet alloc] init];
    jx60Switch.value = maModel.ma5;
    jx60Switch.initialValue = maModel.ma5;
    jx60Switch.defaultValue = maModel.ma5Default;
    jx60Switch.unit = @"日均线";
    jx60Switch.on = maModel.ma5IsOn;
    jx60Switch.initialOn = maModel.ma5IsOn;
    jx60Switch.minValue = maModel.maMin;
    jx60Switch.maxValue = maModel.maMax;
    jx60DataSet.switchDataSet = jx60Switch;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"均线（MA）：\n"
    "移动平均线（Moving Average），简称MA，是用统计分析的方法，将一定时期内的证券价格（指数）加以平均，并把不同时间的平均值连接起来，形成一根MA，用以观察证券价格变动趋势的一种技术指标。\n\n"
    "基本用法：\n"
    "1.股价高于平均线，视为强势；股价低于平均线，视为弱势。\n"
    "2.平均线向上涨升，具有助涨力道；平均线向下跌降，具有助跌力道。\n"
    "3.两条以上平均线向上交叉时，买进。\n"
    "4.两条以上平均线向下交叉时，卖出。\n"
    "5.移动平均线的信号经常落后股价，若以EXPMA/VMA辅助，可以改善。";
    jtmsDesc.strongTitleArr = @[@"均线（MA）：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, jx5DataSet, jx10DataSet, jx20DataSet, jx40DataSet, jx60DataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineBOLLDataSets {
    
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    // DIFF
//    UPMarket2StockSettingDetailDataSet *diffDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
//    diffDataSet.cellStyle = UPMarket2StockSettingDetailStyleTip;
//    UPMarket2StockSettingDetailTipDataSet *diffTip = [[UPMarket2StockSettingDetailTipDataSet alloc] init];
//    diffTip.title = @"DIFF：收盘价短期、长期指数平滑移动平均值的差";
//    diffDataSet.tipDataSet = diffTip;
    
    UPMarket2StockSettingDetailDataBOLLModel *boll = (UPMarket2StockSettingDetailDataBOLLModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineBOLL)];
    
    // 标准
    UPMarket2StockSettingDetailDataSet *fbDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    fbDataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    fbDataSet.indexTag = UPMarket2StockSettingDetailDataBOLLIndexTagDiff1;
    UPMarket2StockSettingDetailSliderDataSet *fbSlider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    fbSlider.title = @"标准";
    fbSlider.value = boll.diff1;
    fbSlider.initialValue = boll.diff1;
    fbSlider.defaultValue = boll.diff1Default;
    fbSlider.unit = @"日";
    fbSlider.minValue = boll.diff1Min;
    fbSlider.maxValue = boll.diff1Max;
    fbDataSet.sliderDataSet = fbSlider;
    
    // 宽度
//    UPMarket2StockSettingDetailDataSet *kdDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
//    kdDataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
//    kdDataSet.indexTag = UPMarket2StockSettingDetailDataBOLLIndexTagDiff2;
//    UPMarket2StockSettingDetailSliderDataSet *kdSlider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
//    kdSlider.title = @"宽度";
//    kdSlider.value = boll.diff2;
//    kdSlider.unit = @"日";
//    kdSlider.minValue = boll.diff2Min;
//    kdSlider.maxValue = boll.diff2Max;
//    kdDataSet.sliderDataSet = kdSlider;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"布林线（BOLL）：\n"
    "又称为股价通道线，他表示的是股价围绕着一定的区间运行的轨迹。布林线由上轨、中轨、下轨三条线组成，股价总是围绕中轨附近上下波动。技术上用来做买点和卖点的判断有一定的参考价值。\n\n"
    "基本用法：\n"
    "1.布林线利用波带显示股价的安全高低价位。\n"
    "2.当易变性变小，而波带变窄时，激烈的价格波动有可能即将发生，表示变盘在即。\n"
    "3.高低点穿越上轨或下轨，立刻又回到波带内，会有短期回档或短期反弹发生。\n"
    "4.波带开始移动后，以此方式进入另一波带，这对于找出目标值很有帮助。";
    jtmsDesc.strongTitleArr = @[@"布林线（BOLL）：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, /*diffDataSet,*/ fbDataSet, /*kdDataSet,*/ zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineVOLDataSets {
    
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    UPMarket2StockSettingDetailDataVOLModel *vol = (UPMarket2StockSettingDetailDataVOLModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineVOL)];
    
    // 5日均线
    UPMarket2StockSettingDetailDataSet *jx5DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jx5DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    jx5DataSet.indexTag = UPMarket2StockSettingDetailDataVOLIndexTagMa1;
    UPMarket2StockSettingDetailSliderDataSet *jx5Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    jx5Slider.value = vol.ma1;
    jx5Slider.initialValue = vol.ma1;
    jx5Slider.defaultValue = vol.ma1Default;
    jx5Slider.unit = @"日均线";
    jx5Slider.minValue = vol.ma1Min;
    jx5Slider.maxValue = vol.ma1Max;
    jx5DataSet.sliderDataSet = jx5Slider;
    
    // 10日均线
    UPMarket2StockSettingDetailDataSet *jx10DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jx10DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    jx10DataSet.indexTag = UPMarket2StockSettingDetailDataVOLIndexTagMa2;
    UPMarket2StockSettingDetailSliderDataSet *jx10Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    jx10Slider.value = vol.ma2;
    jx10Slider.initialValue = vol.ma2;
    jx10Slider.defaultValue = vol.ma2Default;
    jx10Slider.unit = @"日均线";
    jx10Slider.minValue = vol.ma2Min;
    jx10Slider.maxValue = vol.ma2Max;
    jx10DataSet.sliderDataSet = jx10Slider;
    
    // 20日均线
    UPMarket2StockSettingDetailDataSet *jx20DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jx20DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    jx20DataSet.indexTag = UPMarket2StockSettingDetailDataVOLIndexTagMa3;
    UPMarket2StockSettingDetailSliderDataSet *jx20Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    jx20Slider.value = vol.ma3;
    jx20Slider.initialValue = vol.ma3;
    jx20Slider.defaultValue = vol.ma3Default;
    jx20Slider.unit = @"日均线";
    jx20Slider.minValue = vol.ma3Min;
    jx20Slider.maxValue = vol.ma3Max;
    jx20DataSet.sliderDataSet = jx20Slider;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"成交量（VOL）：\n"
    "是指某个交易品种或者大盘的成交总手数（对于股票来说，1手等于100股），用红绿柱体表示，颜色与相应的K线相同。\n\n"
    "量均线（VMA）：\n"
    "是价格平均线方法在成交量上的使用，可以同样平滑移动 。MA（5、10、20）分别表示5日、10日、20日市场平均成交情况。\n\n"
    "基本用法：\n"
    "1.成交量大，代表交投热络，可界定为热门股。\n"
    "2.底部起涨点出现大成交量（成交手数），代表攻击量。\n"
    "3.头部地区出现大成交量（成交手数），代表出货量。\n"
    "4.观察成交金额的变化，比观察成交手数更具意义，因为成交手数并未反应股价的涨跌的后所应支出的实际金额。";
    jtmsDesc.strongTitleArr = @[@"成交量（VOL）：", @"量均线（VMA）：", @"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, jx5DataSet, jx10DataSet, jx20DataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineMACDDataSets {
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    // DIFF
    UPMarket2StockSettingDetailDataSet *diffDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    diffDataSet.cellStyle = UPMarket2StockSettingDetailStyleTip;
    UPMarket2StockSettingDetailTipDataSet *diffTip = [[UPMarket2StockSettingDetailTipDataSet alloc] init];
    diffTip.title = @"DIFF：收盘价短期与长期平滑移动平均值的差";
    diffDataSet.tipDataSet = diffTip;
    
    UPMarket2StockSettingDetailDataMACDModel *macd = (UPMarket2StockSettingDetailDataMACDModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineMACD)];
    // 短期
    UPMarket2StockSettingDetailDataSet *dqDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    dqDataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    dqDataSet.indexTag = UPMarket2StockSettingDetailDataMACDIndexTagDiff1;
    UPMarket2StockSettingDetailSliderDataSet *dqSlider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    dqSlider.title = @"短期";
    dqSlider.value = macd.diff1;
    dqSlider.initialValue = macd.diff1;
    dqSlider.defaultValue = macd.diff1Default;
    dqSlider.unit = @"日";
    dqSlider.minValue = macd.diff1Min;
    dqSlider.maxValue = macd.diff1Max;
    dqDataSet.sliderDataSet = dqSlider;
    
    // 长期
    UPMarket2StockSettingDetailDataSet *cqDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    cqDataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    cqDataSet.indexTag = UPMarket2StockSettingDetailDataMACDIndexTagDiff2;
    UPMarket2StockSettingDetailSliderDataSet *cqSlider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    cqSlider.title = @"长期";
    cqSlider.value = macd.diff2;
    cqSlider.initialValue = macd.diff2;
    cqSlider.defaultValue = macd.diff2Default;
    cqSlider.unit = @"日";
    cqSlider.minValue = macd.diff2Min;
    cqSlider.maxValue = macd.diff2Max;
    cqDataSet.sliderDataSet = cqSlider;
    
    // DEA
    UPMarket2StockSettingDetailDataSet *deaDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    deaDataSet.cellStyle = UPMarket2StockSettingDetailStyleTip;
    UPMarket2StockSettingDetailTipDataSet *deaTip = [[UPMarket2StockSettingDetailTipDataSet alloc] init];
    deaTip.title = @"DEA：DIFF的M日平滑移动平均值";
    deaDataSet.tipDataSet = deaTip;
    
    // M
    UPMarket2StockSettingDetailDataSet *mDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    mDataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    mDataSet.indexTag = UPMarket2StockSettingDetailDataMACDIndexTagDea;
    UPMarket2StockSettingDetailSliderDataSet *mSlider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    mSlider.title = @"M";
    mSlider.value = macd.dea;
    mSlider.initialValue = macd.dea;
    mSlider.defaultValue = macd.deaDefault;
    mSlider.unit = @"日";
    mSlider.minValue = macd.deaMin;
    mSlider.maxValue = macd.deaMax;
    mDataSet.sliderDataSet = mSlider;
    
    // MACD
    UPMarket2StockSettingDetailDataSet *macdDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    macdDataSet.cellStyle = UPMarket2StockSettingDetailStyleTip;
    UPMarket2StockSettingDetailTipDataSet *macdTip = [[UPMarket2StockSettingDetailTipDataSet alloc] init];
    macdTip.title = @"MACD：2倍的(DIFF-DEA)";
    macdDataSet.tipDataSet = macdTip;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"MACD：\n"
    "DIFF线是收盘价短期、长期指数平滑移动平均线的差；DEA线是DIFF线的M日指数平滑移动平均线；MACD线是DIFF线与DEA线的差，彩色柱状线。\n\n"
    "基本用法：\n"
    "1.DIF、DEA均为正，DIF向上突破DEA，买入信号参考。\n"
    "2.DIF、DEA均为负，DIF向下跌破DEA，卖出信号参考。\n"
    "3.DIF线与K线发生背离，行情可能出现反转信号。\n"
    "4.DIF、DEA的值从正数变成负数，或者从负数变成正数并不是交易信号，因为它们落后于市场。";
    jtmsDesc.strongTitleArr = @[@"MACD：", @"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, diffDataSet, dqDataSet, cqDataSet, deaDataSet, mDataSet, macdDataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineKDJDataSets {
    
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    UPMarket2StockSettingDetailDataKDJModel *kdj = (UPMarket2StockSettingDetailDataKDJModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineKDJ)];
    // 5日
    UPMarket2StockSettingDetailDataSet *kdj5DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    kdj5DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    kdj5DataSet.indexTag = UPMarket2StockSettingDetailDataKDJIndexTagK;
    UPMarket2StockSettingDetailSliderDataSet *kdj5Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    kdj5Slider.value = kdj.k;
    kdj5Slider.initialValue = kdj.k;
    kdj5Slider.defaultValue = kdj.kDefault;
    kdj5Slider.unit = @"日";
    kdj5Slider.minValue = kdj.kMin;
    kdj5Slider.maxValue = kdj.kMax;
    kdj5DataSet.sliderDataSet = kdj5Slider;
    
    // 10日
    UPMarket2StockSettingDetailDataSet *kdj10DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    kdj10DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    kdj10DataSet.indexTag = UPMarket2StockSettingDetailDataKDJIndexTagD;
    UPMarket2StockSettingDetailSliderDataSet *kdj10Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    kdj10Slider.value = kdj.d;
    kdj10Slider.initialValue = kdj.d;
    kdj10Slider.defaultValue = kdj.dDefault;
    kdj10Slider.unit = @"日";
    kdj10Slider.minValue = kdj.dMin;
    kdj10Slider.maxValue = kdj.dMax;
    kdj10DataSet.sliderDataSet = kdj10Slider;
    
    // 20日
    UPMarket2StockSettingDetailDataSet *kdj20DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    kdj20DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    kdj20DataSet.indexTag = UPMarket2StockSettingDetailDataKDJIndexTagJ;
    UPMarket2StockSettingDetailSliderDataSet *kdj20Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    kdj20Slider.value = kdj.j;
    kdj20Slider.initialValue = kdj.j;
    kdj20Slider.defaultValue = kdj.jDefault;
    kdj20Slider.unit = @"日";
    kdj20Slider.minValue = kdj.jMin;
    kdj20Slider.maxValue = kdj.jMax;
    kdj20DataSet.sliderDataSet = kdj20Slider;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"KDJ：\n"
    "KDJ线称之为随机指标，K为快速指标，D为慢速指标。根据KDJ的取值，可将其划分为几个区域，即超买区、超卖区和徘徊区。按一般划分标准，K、D、J这三值在20以下为超卖区，是买入信号;K、D、J这三值在80以上为超买区，是卖出信号;K、D、J这三值在20-80之间为徘徊区，宜观望。\n\n"
    "基本用法：\n"
    "1. K值由右边向下交叉D值作卖,K值由右边向上交叉D值作买。\n"
    "2. 高档连续两次向下交叉确认跌势.低档两次向上交叉确认涨势。\n"
    "3. D值<20%超卖,D值>80%超买；J>100%超买,J<10%超卖。\n"
    "4. KD值于50%左右徘徊或交叉时,无意义。\n"
    "5. 投机性太强的个股不适用。\n"
    "6. 可观察KD值与股价之背离,以确认高低点。";
    jtmsDesc.strongTitleArr = @[@"KDJ：", @"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, kdj5DataSet, kdj10DataSet, kdj20DataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineRSIDataSets {
    
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    UPMarket2StockSettingDetailDataRSIModel *rsiModel = (UPMarket2StockSettingDetailDataRSIModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineRSI)];
    
    // 5日
    UPMarket2StockSettingDetailDataSet *rsi5DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    rsi5DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    rsi5DataSet.indexTag = UPMarket2StockSettingDetailDataRSIIndexTagRSI1;
    UPMarket2StockSettingDetailSliderDataSet *rsi5Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    rsi5Slider.value = rsiModel.rsi1;
    rsi5Slider.initialValue = rsiModel.rsi1;
    rsi5Slider.defaultValue = rsiModel.rsi1Default;
    rsi5Slider.unit = @"日";
    rsi5Slider.minValue = rsiModel.rsi1Min;
    rsi5Slider.maxValue = rsiModel.rsi1Max;
    rsi5DataSet.sliderDataSet = rsi5Slider;
    
    // 10日
    UPMarket2StockSettingDetailDataSet *rsi10DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    rsi10DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    rsi10DataSet.indexTag = UPMarket2StockSettingDetailDataRSIIndexTagRSI2;
    UPMarket2StockSettingDetailSliderDataSet *rsi10Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    rsi10Slider.value = rsiModel.rsi2;
    rsi10Slider.initialValue = rsiModel.rsi2;
    rsi10Slider.defaultValue = rsiModel.rsi2Default;
    rsi10Slider.unit = @"日";
    rsi10Slider.minValue = rsiModel.rsi2Min;
    rsi10Slider.maxValue = rsiModel.rsi2Max;
    rsi10DataSet.sliderDataSet = rsi10Slider;
    
    // 20日
    UPMarket2StockSettingDetailDataSet *rsi20DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    rsi20DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    rsi20DataSet.indexTag = UPMarket2StockSettingDetailDataRSIIndexTagRSI3;
    UPMarket2StockSettingDetailSliderDataSet *rsi20Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    rsi20Slider.value = rsiModel.rsi3;
    rsi20Slider.initialValue = rsiModel.rsi3;
    rsi20Slider.defaultValue = rsiModel.rsi3Default;
    rsi20Slider.unit = @"日";
    rsi20Slider.minValue = rsiModel.rsi3Min;
    rsi20Slider.maxValue = rsiModel.rsi3Max;
    rsi20DataSet.sliderDataSet = rsi20Slider;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"RSI：\n"
    "相对强弱指数RSI是通过比较一段时期内的平均收盘涨数和平均收盘跌数来分析市场买沽盘的意向和实力，从而作出未来市场的走势。\n\n"
    "基本用法：\n"
    "1.RSI 值于 0 一 100 之间呈正态分布。\n"
    "2.当 6 日 RSI 值在80%以上时，股市呈超买现象。\n"
    "3.若RSI曲线出现M头，为卖出时机，若出现W底为买进时机。\n"
    "4.当６日RSI值在20%以下时，股市呈超卖现象。\n"
    "5.RSI一般选用5天、10天、30天作为参考基期，基期越长越有趋势性，越短越有敏感性。";
    jtmsDesc.strongTitleArr = @[@"RSI：", @"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, rsi5DataSet, rsi10DataSet, rsi20DataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineBIASDataSets {
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    UPMarket2StockSettingDetailDataBIASModel *biasModel = (UPMarket2StockSettingDetailDataBIASModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineBIAS)];
    
    // 5日
    UPMarket2StockSettingDetailDataSet *bias5DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    bias5DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    bias5DataSet.indexTag = UPMarket2StockSettingDetailDataBIASIndexTagBIAS1;
    UPMarket2StockSettingDetailSliderDataSet *bias5Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    bias5Slider.value = biasModel.bias1;
    bias5Slider.initialValue = biasModel.bias1;
    bias5Slider.defaultValue = biasModel.bias1Default;
    bias5Slider.unit = @"日";
    bias5Slider.minValue = biasModel.bias1Min;
    bias5Slider.maxValue = biasModel.bias1Max;
    bias5DataSet.sliderDataSet = bias5Slider;
    
    // 10日
    UPMarket2StockSettingDetailDataSet *bias10DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    bias10DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    bias10DataSet.indexTag = UPMarket2StockSettingDetailDataBIASIndexTagBIAS2;
    UPMarket2StockSettingDetailSliderDataSet *bias10Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    bias10Slider.value = biasModel.bias2;
    bias10Slider.initialValue = biasModel.bias2;
    bias10Slider.defaultValue = biasModel.bias2Default;
    bias10Slider.unit = @"日";
    bias10Slider.minValue = biasModel.bias2Min;
    bias10Slider.maxValue = biasModel.bias2Max;
    bias10DataSet.sliderDataSet = bias10Slider;
    
    // 20日
    UPMarket2StockSettingDetailDataSet *bias20DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    bias20DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    bias20DataSet.indexTag = UPMarket2StockSettingDetailDataBIASIndexTagBIAS3;
    UPMarket2StockSettingDetailSliderDataSet *bias20Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    bias20Slider.value = biasModel.bias3;
    bias20Slider.initialValue = biasModel.bias3;
    bias20Slider.defaultValue = biasModel.bias3Default;
    bias20Slider.unit = @"日";
    bias20Slider.minValue = biasModel.bias3Min;
    bias20Slider.maxValue = biasModel.bias3Max;
    bias20DataSet.sliderDataSet = bias20Slider;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"BIAS：\n"
    "乖离率又称偏离率，简称Y值，是通过计算市场指数或收盘价与某条移动平均线之间的差距百分比，以反映一定时期内价格与其MA偏离程度的指标，从而得出价格在剧烈波动时因偏离移动平均趋势而造成回档或反弹的可能性，以及价格在正常波动范围内移动而形成继续原有势的可信度。\n\n"
    "基本用法：\n"
    "1.当平均线从下降逐渐转为盘局或上升，而价格从平均线下方突破平均线，为买进信号。\n"
    "2.当价格虽跌破平均线，但又立刻回升到平均线上，此时平均线仍然保持上升势态，还为买进信号。\n"
    "3.当价格趋势线走在平均线上，价格下跌并未跌破平均线并且立刻反转上升，亦是买进信号。\n"
    "4.当价格突然暴跌，跌破平均线，且远离平均线，则有可能反弹上升，亦为买进信号。\n"
    "5.当平均线从上升逐渐转为盘局或下跌，而价格向下跌破平均线，为卖出信号。\n"
    "6.当价格虽然向上突破平均线，但又立刻回跌至平均线以下，此时平均线仍然保持持续下跌势态，还为卖出信号。\n"
    "7.当价格趋势线走在平均线下，价格上升却并未突破平均线且立刻反转下跌，亦是卖出信号。\n"
    "8.当价格突然暴涨，突破平均线，且远离平均线，则有可能反弹回跌，亦为卖出信号。";
    jtmsDesc.strongTitleArr = @[@"BIAS：", @"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, bias5DataSet, bias10DataSet, bias20DataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineVRDataSets {
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    UPMarket2StockSettingDetailDataVRModel *vrModel = (UPMarket2StockSettingDetailDataVRModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineVR)];
    // 26日
    UPMarket2StockSettingDetailDataSet *vr26DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    vr26DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    vr26DataSet.indexTag = UPMarket2StockSettingDetailDataVRIndexTagVR1;
    UPMarket2StockSettingDetailSliderDataSet *vr26Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    vr26Slider.value = vrModel.vr1;
    vr26Slider.initialValue = vrModel.vr1;
    vr26Slider.defaultValue = vrModel.vr1Default;
    vr26Slider.unit = @"日";
    vr26Slider.minValue = vrModel.vr1Min;
    vr26Slider.maxValue = vrModel.vr1Max;
    vr26DataSet.sliderDataSet = vr26Slider;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"VR：\n"
    "称为成交量比率，是一项通过分析股价上升日成交额（或成交量，下同）与股价下降日成交额比值，从而掌握市场买卖气势的中期技术指标。主要用于个股分析，其理论基础是“量价同步”及“量须先于价”，以成交量的变化确认低价和高价，从而确定买卖时法。\n\n"
    "基本用法：\n"
    "1.VR>450，市场成交过热，可反向卖出。\n"
    "2.VR<40，市场成交低迷，可反向买进。\n"
    "3.VR 由低档直接上升至250，股价仍为遭受阻力，此为大行情的前兆。\n"
    "4.VR 除了与PSY为同指标群外，尚须与BR、AR、CR同时搭配研判。";
    
    jtmsDesc.strongTitleArr = @[@"VR：", @"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, vr26DataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineCRDataSets {
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    UPMarket2StockSettingDetailDataCRModel *crModel = (UPMarket2StockSettingDetailDataCRModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineCR)];
    
    // 26日
    UPMarket2StockSettingDetailDataSet *cr26DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    cr26DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    cr26DataSet.indexTag = UPMarket2StockSettingDetailDataCRIndexTagCR1;
    UPMarket2StockSettingDetailSliderDataSet *cr26Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    cr26Slider.value = crModel.cr1;
    cr26Slider.initialValue = crModel.cr1;
    cr26Slider.defaultValue = crModel.cr1Default;
    cr26Slider.unit = @"日";
    cr26Slider.minValue = crModel.cr1Min;
    cr26Slider.maxValue = crModel.cr1Max;
    cr26DataSet.sliderDataSet = cr26Slider;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"CR：\n"
    "又称中间意愿指标、价格动量指标，它和AR、BR指标有很多相似之处，但更有自己独特的研判功能，是分析股市多空双方力量对比、把握买卖股票时机的一种中长期技术分析工具。\n\n"
    "基本用法：\n"
    "1.CR>400时，其10日平均线向下滑落，视为卖出信号；CR<40买进。\n"
    "2.CR 由高点下滑至其四条平均线下方时，股价容易形成短期底部。\n"
    "3.CR 由下往上连续突破其四条平均线时，为强势买进点。\n"
    "4.CR 除了预测价格的外，最大的作用在于预测时间；\n"
    "5.BR、AR、CR、VR 四者合为一组指标群，须综合搭配使用。";
    jtmsDesc.strongTitleArr = @[@"CR：", @"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, cr26DataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineWRDataSets {
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    UPMarket2StockSettingDetailDataWRModel *wrModel = (UPMarket2StockSettingDetailDataWRModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineWR)];
    
    // 26日
    UPMarket2StockSettingDetailDataSet *wr26DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    wr26DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    wr26DataSet.indexTag = UPMarket2StockSettingDetailDataWRIndexTagWR1;
    UPMarket2StockSettingDetailSliderDataSet *wr26Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    wr26Slider.value = wrModel.wr1;
    wr26Slider.initialValue = wrModel.wr1;
    wr26Slider.defaultValue = wrModel.wr1Default;
    wr26Slider.unit = @"日";
    wr26Slider.minValue = wrModel.wr1Min;
    wr26Slider.maxValue = wrModel.wr1Max;
    wr26DataSet.sliderDataSet = wr26Slider;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"WR：\n"
    "称为威廉指标，是属于摆动类指标。它是指当天的收盘价在过去一段日子的全部价格范围内所处的相对位置，是一种兼具超买超卖和强弱分界的技术指标。威廉指标由两条威廉曲线组成，它们分别是短期威廉曲线和中期威廉曲线。\n\n"
    "基本用法：\n"
    "1.本指标以50为中轴线，高于50视为股价转强；低于50视为股价转弱。\n"
    "2.本指标高于20后再度向下跌破20，卖出参考；低于80后再度向上突破80，买进参考。\n"
    "3.WR连续触底3 - 4次，股价向下反转机率大；连续触顶3 - 4次，股价向上反转机率大。";
    jtmsDesc.strongTitleArr = @[@"WR：", @"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, wr26DataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineDMADataSets {
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    // DDD
    UPMarket2StockSettingDetailDataSet *dddDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    dddDataSet.cellStyle = UPMarket2StockSettingDetailStyleTip;
    UPMarket2StockSettingDetailTipDataSet *dddTip = [[UPMarket2StockSettingDetailTipDataSet alloc] init];
    dddTip.title = @"DDD：短期均价与长期均价的差";
    dddDataSet.tipDataSet = dddTip;
    
    UPMarket2StockSettingDetailDataDMAModel *dmaModel = (UPMarket2StockSettingDetailDataDMAModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineDMA)];
    
    // 短期
    UPMarket2StockSettingDetailDataSet *dqDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    dqDataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    dqDataSet.indexTag = UPMarket2StockSettingDetailDataDMAIndexTagDDD1;
    UPMarket2StockSettingDetailSliderDataSet *dqSlider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    dqSlider.title = @"短期";
    dqSlider.value = dmaModel.ddd1;
    dqSlider.initialValue = dmaModel.ddd1;
    dqSlider.defaultValue = dmaModel.ddd1Default;
    dqSlider.unit = @"日";
    dqSlider.minValue = dmaModel.ddd1Min;
    dqSlider.maxValue = dmaModel.ddd1Max;
    dqDataSet.sliderDataSet = dqSlider;
    
    // 长期
    UPMarket2StockSettingDetailDataSet *cqDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    cqDataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    cqDataSet.indexTag = UPMarket2StockSettingDetailDataDMAIndexTagDDD2;
    UPMarket2StockSettingDetailSliderDataSet *cqSlider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    cqSlider.title = @"长期";
    cqSlider.value = dmaModel.ddd2;
    cqSlider.initialValue = dmaModel.ddd2;
    cqSlider.defaultValue = dmaModel.ddd2Default;
    cqSlider.unit = @"日";
    cqSlider.minValue = dmaModel.ddd2Min;
    cqSlider.maxValue = dmaModel.ddd2Max;
    cqDataSet.sliderDataSet = cqSlider;
    
    // AMA
    UPMarket2StockSettingDetailDataSet *amaDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    amaDataSet.cellStyle = UPMarket2StockSettingDetailStyleTip;
    UPMarket2StockSettingDetailTipDataSet *amaTip = [[UPMarket2StockSettingDetailTipDataSet alloc] init];
    amaTip.title = @"AMA：DDD的M日均值";
    amaDataSet.tipDataSet = amaTip;
    
    // M
    UPMarket2StockSettingDetailDataSet *mDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    mDataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    mDataSet.indexTag = UPMarket2StockSettingDetailDataDMAIndexTagAMA1;
    UPMarket2StockSettingDetailSliderDataSet *mSlider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    mSlider.title = @"M";
    mSlider.value = dmaModel.ama1;
    mSlider.initialValue = dmaModel.ama1;
    mSlider.defaultValue = dmaModel.ama1Default;
    mSlider.unit = @"日";
    mSlider.minValue = dmaModel.ama1Min;
    mSlider.maxValue = dmaModel.ama1Max;
    mDataSet.sliderDataSet = mSlider;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"DMA：\n"
    "又称平行线差指标，是目前股市分析技术指标中的一种中短期指标，它常用于大盘指数和个股的研判。它主要通过计算两条基准周期不同的移动平均线的差值，来判断当前买入卖出的能量的大小和未来价格走势的趋势。\n\n"
    "基本用法：\n"
    "1.DMA 向上交叉其平均线时，买进。\n"
    "2.DMA 向下交叉其平均线时，卖出。\n"
    "3.DMA 的交叉信号比MACD、TRIX 略快。\n"
    "4.DMA 与股价产生背离时的交叉信号，可信度较高。\n"
    "5.DMA、MACD、TRIX 三者构成一组指标群，互相验证。";
    jtmsDesc.strongTitleArr = @[@"DMA：", @"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, dddDataSet, dqDataSet, cqDataSet, amaDataSet, mDataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineOBVDataSets {
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    UPMarket2StockSettingDetailDataOBVModel *obvModel = (UPMarket2StockSettingDetailDataOBVModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineOBV)];
    
    // 26日
    UPMarket2StockSettingDetailDataSet *obv26DataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    obv26DataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    obv26DataSet.indexTag = UPMarket2StockSettingDetailDataOBVIndexTagOBV1;
    UPMarket2StockSettingDetailSliderDataSet *obv26Slider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    obv26Slider.value = obvModel.obv1;
    obv26Slider.initialValue = obvModel.obv1;
    obv26Slider.defaultValue = obvModel.obv1Default;
    obv26Slider.unit = @"日";
    obv26Slider.minValue = obvModel.obv1Min;
    obv26Slider.maxValue = obvModel.obv1Max;
    obv26DataSet.sliderDataSet = obv26Slider;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"OBV：\n"
    "称为平衡成交量，是一项通过统计成交量变动的趋势来推测股价趋势的指标。其主要理论基础是市场价格的变化必须有成交量的配合，股价的波动与成交量的扩大或萎缩有密切的关连。\n\n"
    "基本用法：\n"
    "1.股价一顶比一顶高，而OBV 一顶比一顶低，暗示头部即将形成。\n"
    "2.股价一底比一底低，而OBV 一底比一底高，暗示底部即将形成。\n"
    "3.OBV 突破其Ｎ字形波动的高点次数达5 次时，为短线卖点。\n"
    "4.OBV 跌破其Ｎ字形波动的低点次数达5 次时，为短线买点。\n"
    "5.OBV 与ADVOL、PVT、WAD、ADL同属一组指标群，使用时应综合研判。";
    jtmsDesc.strongTitleArr = @[@"OBV：", @"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, obv26DataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineCCIDataSets {
    // 参数设置
    UPMarket2StockSettingDetailDataSet *csszDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    csszDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *csszSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    csszSection.title = @"参数设置";
    csszDataSet.sectionDataSet = csszSection;
    
    UPMarket2StockSettingDetailDataCCIModel *cciModel = (UPMarket2StockSettingDetailDataCCIModel *)[UPMarket2StockSettingDetailStateManager modelWithType:(UPMarket2StockSettingTypeKLineCCI)];
    
    // 26日
    UPMarket2StockSettingDetailDataSet *cciDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    cciDataSet.cellStyle = UPMarket2StockSettingDetailStyleSlider;
    cciDataSet.indexTag = UPMarket2StockSettingDetailDataCCIIndexTagCCI;
    UPMarket2StockSettingDetailSliderDataSet *cciSlider = [[UPMarket2StockSettingDetailSliderDataSet alloc] init];
    cciSlider.title = @"N:";
    cciSlider.value = cciModel.cci;
    cciSlider.initialValue = cciModel.cci;
    cciSlider.defaultValue = cciModel.cciDefault;
    cciSlider.unit = nil;
    cciSlider.minValue = cciModel.cciMin;
    cciSlider.maxValue = cciModel.cciMax;
    cciDataSet.sliderDataSet = cciSlider;
    
    // 指标说明
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"CCI：\n" @"顺势指标又叫CCI指标，专门测量股价是否已超出常态分布范围。属于超买超卖类指标中较特殊的一种，波动于正无穷大和负无穷大之间。CCI指标是根据统计学原理，引进价格与固定期间的股价平均区间的偏离程度的概念，强调股价平均绝对偏差在股市技术分析中的重要性，是一种比较独特的技术指标。\n\n"
    "基本用法：\n"
    "1.CCI处于±100之间时视为常态行情；CCI处于±100之外时视为强势行情。\n"
    "2.CCI正值时视为多头市场；负值时视为空头市场。\n"
    "3.CCI>100时，参考买入；CCI<100时，参考卖出。\n"
    "4.CCI<100时，参考减仓；CCI>100时，参考补仓.";
    jtmsDesc.strongTitleArr = @[@"CCI：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[csszDataSet, cciDataSet, zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineDDXDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"DDX：\n"
    "DDX大单动向基于交易所Level-2的逐单数据，是一个短中线兼顾的技术指标。DDX红绿柱线表示当日大单买入净量占流通盘的百分比(估计值)，红柱表示大单买入量较大，绿柱表示大单卖出量较大。DDX1是大单买入净量60日（参数P1）平滑累加值占流通盘比例，DDX2和DDX3是其5日（参数P2）和10日（参数P3）移动平均线。\n\n"
    "基本用法：\n"
    "1.如果当日红绿柱线为红色表示当日大单买入量较大，反之如果当日红绿柱线为绿色表示大单卖出较多。\n"
    "2.三线持续向上主力买入积极，股价有持续的上涨动力。\n"
    "3.三线持续向下表示主力持续卖出。";
    jtmsDesc.strongTitleArr = @[@"DDX：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineDDYDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"DDY：\n"
    "DDY涨跌动因指标是每日卖出单数和买入单数的差值占笔数化流通盘的比例。排名靠前的股票代表当日出货散户人数比例最大的股票，这个值一方面可以和DDX相互验证，另一方面可以发现主力悄悄建仓的股票。\n\n"
    "基本用法：\n"
    "1.如果当日红绿柱线为红色表示当日单数差为正，大单买入较多，反之如果当日红绿柱线为绿色表示当日单数差为负，大单卖出较多。\n"
    "2.三线持续向上则表示筹码在持续向少数人转移，有主力资金收集，股价有持续的。\n"
    "3.股价上涨三线却向下，表明是游资短线和散户行情，一般不具备长期的上涨动力。\n"
    "4.涨跌动因指标所要在一个较长的周期内观察，如果一段时间三线持续向上，那么每次股价回调就是买入良机。相反如果上涨时三线持续向下，那么短线超买就是减仓良机。\n"
    "5.该指标具有极大的超前性，因为筹码的收集和发散都有一个过程。股价尽管还沿着原来的趋势运行，但筹码转移的方向已经逆转。";
    jtmsDesc.strongTitleArr = @[@"DDY：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineDDZDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"DDZ：\n"
    "DDZ大单差分是大单质量的评价指标，反映大资金的实力。有些多空争斗比较激烈的股票，买卖双方的买卖量都很大，DDZ的作用在于衡量买卖双方大单的力度，这种股票对于大盘股或机构分歧较大的股票比较有效，当然对于多方主力占绝对优势的股票更容易排行靠前。\n\n"
    "基本用法：\n"
    "1.红柱表示了大资金买入强度，柱体越高、连续天数越多表示买入强度越大。当柱线突然升高或连续出现红柱往往预示短线将快速上涨。\n"
    "2.绿柱表示了大资金卖出强度，柱体越高、连续天数越多表示卖出强度越大。当柱线突然下降或连续出现绿柱往往预示短线将快速下跌。\n"
    "3.该指标综合了DDX和DDY的优点，对大单的动作敏感性更强。";
    jtmsDesc.strongTitleArr = @[@"DDZ：",@"基本用法："];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)otherCCCBXDataSets {
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.imageNamed = @"指标设置/指标设置-持仓成本线";
    jtmsDesc.desc = @"将您的交易持仓个股的成本以线的形式标注在分时图和K线图上，随着您的持仓成本更新。若您的持仓成本为0、负数或超过K线图显示区域则不显示。\n\n"
    "成本线为您当前交易所在账户的持仓信息，成本线数据仅供参考，更新可能会有延迟，具体以当前所在的交易账号持仓中的数据为准。";
    jtmsDesc.strongTitleArr = @[];
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[jtmsDataSet];
    
    return arr;
}


+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineZLJCDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"1、红色彩带表示个股主升浪趋势。\n"
    "2、绿色细线表示个股下跌趋势。\n"
    "3、红色实心K线代表持股状态。\n"
    "4、青色实心K线代表观望状态。\n";
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineSXNZDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"1、当三线向上扭转合一的时候，预示着一波上升行情的启动。\n"
    "2、当三线向下扭转分散的时候，预示着一波下跌行情开启。\n";
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineQLZZDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"1、细线代表主力攻击线，粗线代表主力控盘线。\n"
    "2、红细线表示多头趋势，主力向上攻击。\n"
    "3、紫色K线表示连续涨停。\n"
    "4、火箭图标表示龙头启动信号。\n"
    "5、星星图标表示龙头主升信号。\n"
    "6、钻石图标表示龙头二波信号。\n"
    "7、旗子图标表示趋势龙头信号。\n"
    "8、鹰眼图标表示波段模型信号。";
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineGZZZDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"1、零轴位置的红色条形图表示中线多头趋势。\n"
    "2、零轴位置的绿色条形图表示中线空头线趋势。\n"
    "3、黄线在零轴上方运行时表示短线趋势走强。\n"
    "4、黄线在零轴下方运行时表示短线趋势走弱。\n"
    "5、紫色柱表示短线趋势和中线趋势共振走强。";
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineQSQBDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"1、我们经常听到身边的炒股高手说强者恒强，告诫我们，做股票就要做强势股。那么如何衡量一只个股是否强势呢？投资大师欧耐尔给了一个全市场打分系统，提出了强度这个词，强度在85以上的个股，才值得我们关注。强势起爆基于强度这个概念而研发，当一只个股领先大盘，同时趋势强度不断升高才可能是短线牛股。\n"
    "2、黄色线是个股强势趋势线，K线高于黄色线表示个股趋势走强。\n"
    "3、红色网状线是大盘走势，K线高于红色网状线表示个股中期跑赢大市。\n"
    "4、紫色K线表示个股同时高于黄色线和红色网状线，股价处于强势起爆阶段。\n"
    "5、红色K线表示个股高于红色网状线低于黄色线，股价处于强势蓄势阶段。\n"
    "6、灰色K线表示个股处于弱势震荡阶段。";
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineDNEHDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"1、红色柱状表示资金正在主导价格上涨。核心：资金做多、价格上涨，持有。策略：买进或持有。\n"
    "2、黄色柱状表示获利资金主导价格。核心：如短期涨幅过大、获利盘过多，则短期价格出现回落或震荡的概率增加。策略：减仓或观望。\n"
    "3、绿色柱状表示资金主导价格下跌。核心：资金做空，价格下跌，远离。策略：离场或观望。\n"
    "4、蓝色柱状表示空头回补资金主导价格。核心：如短期跌幅过大、空头获利盘过多，则短期价格出现反弹或震荡的概率增加。策略：空头平仓或观望。";
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineSJNXDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"1. 四种K线颜色分别代表大盘春夏秋冬四种牛熊区域；\n"
    "2. 红色表示春天，波段上涨区域；\n"
    "3. 黄色表示夏天，横盘震荡区域；\n"
    "4. 绿色表示秋天，弱势下跌区域；\n"
    "5. 蓝色表示冬天，超跌反弹区域。";
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineHXFKDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"1.该指标监控市场每日核心风口的数量；\n"
    "2.柱状体越高则核心风口数量越多。";
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineLTGDDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"1.该指标监控市场每日最长连板个股的连板数；\n"
    "2.柱状体越高则连板数越高。";
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

+ (NSArray<UPMarket2StockSettingDetailDataSet *> *)kLineZQXYDataSets {
    
    UPMarket2StockSettingDetailDataSet *zbsmDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    zbsmDataSet.cellStyle = UPMarket2StockSettingDetailStyleSection;
    UPMarket2StockSettingDetailSectionDataSet *zbsmSection = [[UPMarket2StockSettingDetailSectionDataSet alloc] init];
    zbsmSection.title = @"指标说明";
    zbsmDataSet.sectionDataSet = zbsmSection;
    
    // 具体描述
    UPMarket2StockSettingDetailDataSet *jtmsDataSet = [[UPMarket2StockSettingDetailDataSet alloc] init];
    jtmsDataSet.cellStyle = UPMarket2StockSettingDetailStyleDesc;
    UPMarket2StockSettingDetailDescDataSet *jtmsDesc = [[UPMarket2StockSettingDetailDescDataSet alloc] init];
    jtmsDesc.desc = @"1.统计上一个交易日涨幅>=5%的股票在今天的平均涨幅；\n"
    "2.剔除停牌超过20个自然日的股票；\n"
    "3.红柱表示上涨，绿柱表示下跌；\n"
    "4.黄色线表示5日赚钱效应平均值；\n"
    "5.紫色线表示20日赚钱效应平均值。";
    jtmsDataSet.descDataSet = jtmsDesc;
    
    NSArray *arr = @[zbsmDataSet, jtmsDataSet];
    
    return arr;
}

@end
