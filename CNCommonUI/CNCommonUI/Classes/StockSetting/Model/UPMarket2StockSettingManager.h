//
//  UPMarket2StockSettingManager.h
//  UPMarket2
//
//  Created by 方恒 on 2020/4/2.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarket2StockSettingCellDataSet.h"
#import "UPMarket2StockIndexCache.h"

NS_ASSUME_NONNULL_BEGIN

static NSString * const kUPMarket2StockSettingSectionTitleKey = @"kUPMarket2StockSettingSectionTitleKey";
static NSString * const kUPMarket2StockSettingRowsKey = @"kUPMarket2StockSettingRowsKey";

static NSString *const kDapanIndexOrderKey = @"kDapanIndexOrderKey";
static NSString *const kMajorIndexOrderKey = @"kMajorIndexOrderKey";
static NSString *const kMinorIndexOrderKey = @"kMinorIndexOrderKey";
static NSString *const kMinuteMinorIndexOrderKey = @"kMinuteMinorIndexOrderKey";
static NSString *const kMinuteMajorIndexOrderKey = @"kMinuteMajorIndexOrderKey";

@interface UPMarket2StockSettingManager : NSObject

@property (nonatomic, strong) UPMarket2StockIndexCache *indexCache;

+ (instancetype)sharedInstance;

- (void)setupIndexMap;

+ (UPMarket2StockSettingAlertModel *)alertModelWithType:(UPMarket2StockSettingType)type;

/// 获取设置-分时数据源
- (NSArray *)minuteDataSetArrStockHq:(UPHqStockHq *)stockHq;;

/// 获取设置-k线数据源
- (NSArray *)kLineDataSetArrWithStockHq:(UPHqStockHq *)stockHq;

/// 获取设置-其他数据源
+ (NSArray *)otherDataSetArr;

//cache目前有三个维度 第一个是CacheType 第二是分时和k线 第三是主副图
//初始化设置
///   - key: 对应的UPMarket2StockIndexCache 的key
- (void)removeOrderListForKey:(NSString *)key;


/// 更新指标排序方法
/// - Parameters:
///   - orderArray: 最新的排序过后的数组
///   - key: 对应的UPMarket2StockIndexCache 的key
- (void)updateOrderList:(NSArray<NSNumber *> *)orderArray
                 ForKey:(NSString *)key;


/// 获得当前缓存排序后数组方法
/// - Parameters:
///   - dataArray: 从网络和本地读取完的所有数据index数组 乱序的
///   - key: UPMarket2StockIndexCache 的key
///   - contaninsHiddenIndex: 是否包含隐藏的 的key
///   return 返回排序后的index数组
- (NSArray<NSNumber *> *)sortArrayByLocalSettingWithArray:(NSArray<NSNumber *> *)dataArray
                                                   ForKey:(NSString *)key
                                     contaninsHiddenIndex:(BOOL)contaninsHiddenIndex;


/// 设置里面隐藏或展示指标 默认都展示
/// - Parameters:
///   - hidden: 是否隐藏
///   - indexId: 对应指标id
- (void)handleHidden:(BOOL)hidden forIndexId:(NSInteger)indexId;

/// 获取当前状态
/// - Parameter indexId: 指标id
- (BOOL)isHiddenForIndexId:(NSInteger)indexId;

/// 获取设置-图标设置
+ (NSArray *)tuBiaoDataSetArr;

/// 获取默认首页设置-数据源
+ (NSArray *)defaultHomeDataSetArr;

// 获取设置
+ (NSArray <UPMarket2StockSettingCellDataSet *> *)getStockSetting;

// 批量添加股票设置
+ (void)addStockSettings:(NSArray <UPMarket2StockSettingCellDataSet *> *)markModel;

// 删除设置
+ (void)cleanStockSettings:(void (^)(NSString *msg, BOOL success))handle;

@end

NS_ASSUME_NONNULL_END
