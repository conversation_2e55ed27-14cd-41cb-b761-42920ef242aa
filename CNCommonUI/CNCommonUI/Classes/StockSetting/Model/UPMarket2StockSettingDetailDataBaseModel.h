//
//  UPMarket2StockSettingDetailDataBaseModel.h
//  UPMarket2
//
//  Created by 方恒 on 2020/4/7.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UPMarketIndex/UPMarketIndexParam.h>
#import "UPMarket2StockSettingDetailDataSet.h"

NS_ASSUME_NONNULL_BEGIN

@interface UPMarket2StockSettingDetailDataBaseModel : NSObject

- (void)resetDefaultValue;

// 判断是否是默认值
- (BOOL)isDefaultValue;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataMACDIndexTag) {
    UPMarket2StockSettingDetailDataMACDIndexTagDiff1,
    UPMarket2StockSettingDetailDataMACDIndexTagDiff2,
    UPMarket2StockSettingDetailDataMACDIndexTagDea,
};

//指标平台动态化
@interface UPMarket2StockSettingDetailDataDynamicModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, strong) NSArray<UPMarket2StockSettingDetailDataSet *> *dataSets;

@property (nonatomic, assign) NSInteger indexId;

+ (instancetype)modelWithIndexParam:(UPMarketIndexParam *)indexParam;

- (UPMarketIndexParam *)generateIndexParamForSelf;
- (void)updateDataSet:(NSUInteger)indexTag forValue:(NSInteger)value;

@end

/// MACD
@interface UPMarket2StockSettingDetailDataMACDModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger diff1;
@property (nonatomic, assign) NSInteger diff2;
@property (nonatomic, assign) NSInteger dea;

@property (nonatomic, assign, readonly) NSInteger diff1Default;
@property (nonatomic, assign, readonly) NSInteger diff2Default;
@property (nonatomic, assign, readonly) NSInteger deaDefault;

@property (nonatomic, assign, readonly) NSInteger diff1Max;
@property (nonatomic, assign, readonly) NSInteger diff1Min;

@property (nonatomic, assign, readonly) NSInteger diff2Max;
@property (nonatomic, assign, readonly) NSInteger diff2Min;

@property (nonatomic, assign, readonly) NSInteger deaMax;
@property (nonatomic, assign, readonly) NSInteger deaMin;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataBOLLIndexTag) {
    UPMarket2StockSettingDetailDataBOLLIndexTagDiff1 = 10,
//    UPMarket2StockSettingDetailDataBOLLIndexTagDiff2,
};

@interface UPMarket2StockSettingDetailDataBOLLModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger diff1; // 标准差
//@property (nonatomic, assign) NSInteger diff2; // 宽度

@property (nonatomic, assign, readonly) NSInteger diff1Default;

@property (nonatomic, assign, readonly) NSInteger diff1Max;
@property (nonatomic, assign, readonly) NSInteger diff1Min;

//@property (nonatomic, assign, readonly) NSInteger diff2Max;
//@property (nonatomic, assign, readonly) NSInteger diff2Min;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataVOLIndexTag) {
    UPMarket2StockSettingDetailDataVOLIndexTagMa1 = 20,
    UPMarket2StockSettingDetailDataVOLIndexTagMa2,
    UPMarket2StockSettingDetailDataVOLIndexTagMa3,
};

@interface UPMarket2StockSettingDetailDataVOLModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger ma1;
@property (nonatomic, assign) NSInteger ma2;
@property (nonatomic, assign) NSInteger ma3;

@property (nonatomic, assign, readonly) NSInteger ma1Default;
@property (nonatomic, assign, readonly) NSInteger ma2Default;
@property (nonatomic, assign, readonly) NSInteger ma3Default;

@property (nonatomic, assign, readonly) NSInteger ma1Max;
@property (nonatomic, assign, readonly) NSInteger ma1Min;

@property (nonatomic, assign, readonly) NSInteger ma2Max;
@property (nonatomic, assign, readonly) NSInteger ma2Min;

@property (nonatomic, assign, readonly) NSInteger ma3Max;
@property (nonatomic, assign, readonly) NSInteger ma3Min;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataKDJIndexTag) {
    UPMarket2StockSettingDetailDataKDJIndexTagK = 30,
    UPMarket2StockSettingDetailDataKDJIndexTagD,
    UPMarket2StockSettingDetailDataKDJIndexTagJ,
};

@interface UPMarket2StockSettingDetailDataKDJModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger k;
@property (nonatomic, assign) NSInteger d;
@property (nonatomic, assign) NSInteger j;

@property (nonatomic, assign, readonly) NSInteger kDefault;
@property (nonatomic, assign, readonly) NSInteger dDefault;
@property (nonatomic, assign, readonly) NSInteger jDefault;

@property (nonatomic, assign, readonly) NSInteger kMax;
@property (nonatomic, assign, readonly) NSInteger kMin;

@property (nonatomic, assign, readonly) NSInteger dMax;
@property (nonatomic, assign, readonly) NSInteger dMin;

@property (nonatomic, assign, readonly) NSInteger jMax;
@property (nonatomic, assign, readonly) NSInteger jMin;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataRSIIndexTag) {
    UPMarket2StockSettingDetailDataRSIIndexTagRSI1 = 40,
    UPMarket2StockSettingDetailDataRSIIndexTagRSI2,
    UPMarket2StockSettingDetailDataRSIIndexTagRSI3
};

@interface UPMarket2StockSettingDetailDataRSIModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger rsi1;
@property (nonatomic, assign) NSInteger rsi2;
@property (nonatomic, assign) NSInteger rsi3;

@property (nonatomic, assign, readonly) NSInteger rsi1Default;
@property (nonatomic, assign, readonly) NSInteger rsi2Default;
@property (nonatomic, assign, readonly) NSInteger rsi3Default;

@property (nonatomic, assign, readonly) NSInteger rsi1Max;
@property (nonatomic, assign, readonly) NSInteger rsi1Min;

@property (nonatomic, assign, readonly) NSInteger rsi2Max;
@property (nonatomic, assign, readonly) NSInteger rsi2Min;

@property (nonatomic, assign, readonly) NSInteger rsi3Max;
@property (nonatomic, assign, readonly) NSInteger rsi3Min;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataBIASIndexTag) {
    UPMarket2StockSettingDetailDataBIASIndexTagBIAS1 = 50,
    UPMarket2StockSettingDetailDataBIASIndexTagBIAS2,
    UPMarket2StockSettingDetailDataBIASIndexTagBIAS3,
};

@interface UPMarket2StockSettingDetailDataBIASModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger bias1;
@property (nonatomic, assign) NSInteger bias2;
@property (nonatomic, assign) NSInteger bias3;

@property (nonatomic, assign, readonly) NSInteger bias1Default;
@property (nonatomic, assign, readonly) NSInteger bias2Default;
@property (nonatomic, assign, readonly) NSInteger bias3Default;

@property (nonatomic, assign, readonly) NSInteger bias1Max;
@property (nonatomic, assign, readonly) NSInteger bias1Min;

@property (nonatomic, assign, readonly) NSInteger bias2Max;
@property (nonatomic, assign, readonly) NSInteger bias2Min;

@property (nonatomic, assign, readonly) NSInteger bias3Max;
@property (nonatomic, assign, readonly) NSInteger bias3Min;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataVRIndexTag) {
    UPMarket2StockSettingDetailDataVRIndexTagVR1 = 60,
};

@interface UPMarket2StockSettingDetailDataVRModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger vr1;

@property (nonatomic, assign, readonly) NSInteger vr1Default;

@property (nonatomic, assign, readonly) NSInteger vr1Max;
@property (nonatomic, assign, readonly) NSInteger vr1Min;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataCRIndexTag) {
    UPMarket2StockSettingDetailDataCRIndexTagCR1 = 70,
};

@interface UPMarket2StockSettingDetailDataCRModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger cr1;

@property (nonatomic, assign, readonly) NSInteger cr1Default;

@property (nonatomic, assign, readonly) NSInteger cr1Max;
@property (nonatomic, assign, readonly) NSInteger cr1Min;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataWRIndexTag) {
    UPMarket2StockSettingDetailDataWRIndexTagWR1 = 80,
};

@interface UPMarket2StockSettingDetailDataWRModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger wr1;

@property (nonatomic, assign, readonly) NSInteger wr1Default;

@property (nonatomic, assign, readonly) NSInteger wr1Max;
@property (nonatomic, assign, readonly) NSInteger wr1Min;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataDMAIndexTag) {
    UPMarket2StockSettingDetailDataDMAIndexTagDDD1 = 90,
    UPMarket2StockSettingDetailDataDMAIndexTagDDD2,
    UPMarket2StockSettingDetailDataDMAIndexTagAMA1,
};

@interface UPMarket2StockSettingDetailDataDMAModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger ddd1;
@property (nonatomic, assign) NSInteger ddd2;
@property (nonatomic, assign) NSInteger ama1;

@property (nonatomic, assign, readonly) NSInteger ddd1Default;
@property (nonatomic, assign, readonly) NSInteger ddd2Default;
@property (nonatomic, assign, readonly) NSInteger ama1Default;

@property (nonatomic, assign, readonly) NSInteger ddd1Max;
@property (nonatomic, assign, readonly) NSInteger ddd1Min;

@property (nonatomic, assign, readonly) NSInteger ddd2Max;
@property (nonatomic, assign, readonly) NSInteger ddd2Min;

@property (nonatomic, assign, readonly) NSInteger ama1Max;
@property (nonatomic, assign, readonly) NSInteger ama1Min;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataOBVIndexTag) {
    UPMarket2StockSettingDetailDataOBVIndexTagOBV1 = 100,
};

@interface UPMarket2StockSettingDetailDataOBVModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger obv1;

@property (nonatomic, assign, readonly) NSInteger obv1Default;
@property (nonatomic, assign, readonly) NSInteger obv1Max;
@property (nonatomic, assign, readonly) NSInteger obv1Min;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataCCIIndexTag) {
    UPMarket2StockSettingDetailDataCCIIndexTagCCI = 120,
};

@interface UPMarket2StockSettingDetailDataCCIModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger cci;

@property (nonatomic, assign, readonly) NSInteger cciDefault;
@property (nonatomic, assign, readonly) NSInteger cciMax;
@property (nonatomic, assign, readonly) NSInteger cciMin;

@end

typedef NS_ENUM(NSUInteger, UPMarket2StockSettingDetailDataMAIndexTag) {
    UPMarket2StockSettingDetailDataMAIndexTagMa1 = 110,
    UPMarket2StockSettingDetailDataMAIndexTagMa2,
    UPMarket2StockSettingDetailDataMAIndexTagMa3,
    UPMarket2StockSettingDetailDataMAIndexTagMa4,
    UPMarket2StockSettingDetailDataMAIndexTagMa5,
};

/// 均线
@interface UPMarket2StockSettingDetailDataMAModel : UPMarket2StockSettingDetailDataBaseModel

@property (nonatomic, assign) NSInteger ma1;
@property (nonatomic, assign) NSInteger ma2;
@property (nonatomic, assign) NSInteger ma3;
@property (nonatomic, assign) NSInteger ma4;
@property (nonatomic, assign) NSInteger ma5;

@property (nonatomic, assign, readonly) NSInteger ma1Default;
@property (nonatomic, assign, readonly) NSInteger ma2Default;
@property (nonatomic, assign, readonly) NSInteger ma3Default;
@property (nonatomic, assign, readonly) NSInteger ma4Default;
@property (nonatomic, assign, readonly) NSInteger ma5Default;

@property (nonatomic, assign, getter=ma1IsOn) BOOL ma1On;
@property (nonatomic, assign, getter=ma2IsOn) BOOL ma2On;
@property (nonatomic, assign, getter=ma3IsOn) BOOL ma3On;
@property (nonatomic, assign, getter=ma4IsOn) BOOL ma4On;
@property (nonatomic, assign, getter=ma5IsOn) BOOL ma5On;

@property (nonatomic, assign, readonly) NSInteger maMax;
@property (nonatomic, assign, readonly) NSInteger maMin;

@end


NS_ASSUME_NONNULL_END
