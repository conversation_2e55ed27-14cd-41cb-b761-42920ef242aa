// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/HotData.jce'
// **********************************************************************

#include "HotData.h"

@implementation DGRecordStockCountReq

@synthesize jce_code = J_PROP_NM(o,0,code);

- (id)init
{
    if (self = [super init]) {
        J_PROP(code) = DefaultJceString;
    }
    return self;
}

@end

@implementation DGRecordStockCountRsp

@synthesize jce_result = J_PROP_NM(o,0,result);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation DGQClientInfo

@synthesize jce_vGuid = J_PROP_NM(o,0,vGuid);
@synthesize jce_sXua = J_PROP_NM(o,1,sXua);

- (id)init
{
    if (self = [super init]) {
        J_PROP(sXua) = DefaultJceString;
    }
    return self;
}

@end

@implementation DGFuzzySearchReq

@synthesize jce_stClient = J_PROP_NM(o,0,stClient);
@synthesize jce_condition = J_PROP_NM(o,1,condition);
@synthesize jce_sceneType = J_PROP_NM(o,2,sceneType);
@synthesize jce_date = J_PROP_NM(o,3,date);
@synthesize jce_offset = J_PROP_NM(o,4,offset);
@synthesize jce_limit = J_PROP_NM(o,5,limit);

- (id)init
{
    if (self = [super init]) {
        J_PROP(condition) = DefaultJceString;
        J_PROP(limit) = -1;
    }
    return self;
}

@end

@implementation DGFColumnDescription

@synthesize jce_name = J_PROP_NM(o,1,name);
@synthesize jce_fieldType = J_PROP_NM(o,2,fieldType);
@synthesize jce_fieldIndex = J_PROP_NM(o,3,fieldIndex);

- (id)init
{
    if (self = [super init]) {
        J_PROP(name) = DefaultJceString;
    }
    return self;
}

@end

@implementation DGFRowData

@synthesize jce_market = J_PROP_NM(r,1,market);
@synthesize jce_code = J_PROP_NM(r,2,code);
@synthesize jce_vStrColumn = J_PROP_EX(o,3,vStrColumn,VONSString);
@synthesize jce_vDblColumn = J_PROP_EX(o,4,vDblColumn,VONSNumber);
@synthesize jce_vIntColumn = J_PROP_EX(o,5,vIntColumn,VONSNumber);
@synthesize jce_vLongColumn = J_PROP_EX(o,6,vLongColumn,VONSNumber);

- (id)init
{
    if (self = [super init]) {
        J_PROP(code) = DefaultJceString;
    }
    return self;
}

@end

@implementation DGFuzzySearchRsp

@synthesize jce_tradeDate = J_PROP_NM(o,0,tradeDate);
@synthesize jce_schemaInfo = J_PROP_EX(o,1,schemaInfo,VODGFColumnDescription);
@synthesize jce_vStock = J_PROP_EX(o,2,vStock,VODGFRowData);
@synthesize jce_totalNum = J_PROP_NM(o,3,totalNum);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation DGSearchReq

@synthesize jce_options = J_PROP_NM(o,0,options);
@synthesize jce_tradeDate = J_PROP_NM(o,1,tradeDate);
@synthesize jce_fields = J_PROP_NM(o,2,fields);
@synthesize jce_offset = J_PROP_NM(o,3,offset);
@synthesize jce_limit = J_PROP_NM(o,4,limit);
@synthesize jce_sortField = J_PROP_NM(o,5,sortField);
@synthesize jce_sortDesc = J_PROP_NM(o,6,sortDesc);
@synthesize jce_hqStockList = J_PROP_EX(o,7,hqStockList,VONSString);
@synthesize jce_stClient = J_PROP_NM(o,8,stClient);
@synthesize jce_collection = J_PROP_NM(o,9,collection);
@synthesize jce_codes = J_PROP_NM(o,10,codes);
@synthesize jce_fuzzyOptions = J_PROP_NM(o,11,fuzzyOptions);

- (id)init
{
    if (self = [super init]) {
        J_PROP(options) = DefaultJceString;
        J_PROP(fields) = DefaultJceString;
        J_PROP(limit) = -1;
        J_PROP(sortField) = DefaultJceString;
        J_PROP(sortDesc) = YES;
        J_PROP(collection) = DefaultJceString;
        J_PROP(codes) = DefaultJceString;
        J_PROP(fuzzyOptions) = DefaultJceString;
    }
    return self;
}

@end

@implementation DGFPoolData

@synthesize jce_tradeDate = J_PROP_NM(o,0,tradeDate);
@synthesize jce_schemaInfo = J_PROP_EX(o,1,schemaInfo,VODGFColumnDescription);
@synthesize jce_vStock = J_PROP_EX(o,2,vStock,VODGFRowData);
@synthesize jce_totalNum = J_PROP_NM(o,3,totalNum);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end

@implementation DGCompleteFieldsReq

@synthesize jce_tradeDate = J_PROP_NM(o,0,tradeDate);
@synthesize jce_fPoolData = J_PROP_NM(o,1,fPoolData);
@synthesize jce_baseFields = J_PROP_EX(o,2,baseFields,VONSString);
@synthesize jce_options = J_PROP_NM(o,3,options);

- (id)init
{
    if (self = [super init]) {
        J_PROP(options) = DefaultJceString;
    }
    return self;
}

@end



