// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Documents/code/dongniu-ios/Tools/PoolStat.jce'
// **********************************************************************

#ifndef MarketIndicatorSysPoolStatAgent_h
#define MarketIndicatorSysPoolStatAgent_h

#import "PoolStat.h"

#import <UPTAF/TAFRequest.h>

// MARK: MarketIndicatorSysPoolStatGetLatestPool
@interface MarketIndicatorSysPoolStatGetLatestPoolRequest : UPTAFRequest
@property (nonatomic, strong) MarketIndicatorSysGetLatestPoolReq* stReq;
@end

@interface MarketIndicatorSysPoolStatGetLatestPoolResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) MarketIndicatorSysGetLatestPoolRsp* stRsp;
@end

// MARK: MarketIndicatorSysPoolStatGetPoolStat
@interface MarketIndicatorSysPoolStatGetPoolStatRequest : UPTAFRequest
@property (nonatomic, strong) MarketIndicatorSysGetPoolStatReq* stReq;
@end

@interface MarketIndicatorSysPoolStatGetPoolStatResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) MarketIndicatorSysGetPoolStatRsp* stRsp;
@end

// MARK: MarketIndicatorSysPoolStatRecentRankingList
@interface MarketIndicatorSysPoolStatRecentRankingListRequest : UPTAFRequest
@property (nonatomic, strong) MarketIndicatorSysRankingListReq* stReq;
@end

@interface MarketIndicatorSysPoolStatRecentRankingListResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) MarketIndicatorSysRankingListRsp* stRsp;
@end

// MARK: MarketIndicatorSysPoolStatBaiduSearch
@interface MarketIndicatorSysPoolStatBaiduSearchRequest : UPTAFRequest
@property (nonatomic, strong) MarketIndicatorSysBaiduSearchReq* stReq;
@end

@interface MarketIndicatorSysPoolStatBaiduSearchResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) MarketIndicatorSysBaiduSearchRsp* stRsp;
@end

// MARK: MarketIndicatorSysPoolStatPoolTrace
@interface MarketIndicatorSysPoolStatPoolTraceRequest : UPTAFRequest
@property (nonatomic, strong) MarketIndicatorSysPoolTraceReq* stReq;
@end

@interface MarketIndicatorSysPoolStatPoolTraceResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) MarketIndicatorSysPoolTraceRsp* stRsp;
@end

// MARK: MarketIndicatorSysPoolStatGetHotStock
@interface MarketIndicatorSysPoolStatGetHotStockRequest : UPTAFRequest
@property (nonatomic, strong) MarketIndicatorSysHotStockReq* stReq;
@end

@interface MarketIndicatorSysPoolStatGetHotStockResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) MarketIndicatorSysHotStockRsp* stRsp;
@end

// MARK: MarketIndicatorSysPoolStatAgent
@interface MarketIndicatorSysPoolStatAgent : NSObject
@property (nonatomic, copy) NSString * servantName;

- (instancetype)initWithServantName:(NSString *)servantName;

- (MarketIndicatorSysPoolStatGetLatestPoolRequest*)newGetLatestPoolRequest:(MarketIndicatorSysGetLatestPoolReq*)stReq ;
- (MarketIndicatorSysPoolStatGetPoolStatRequest*)newGetPoolStatRequest:(MarketIndicatorSysGetPoolStatReq*)stReq ;
- (MarketIndicatorSysPoolStatRecentRankingListRequest*)newRecentRankingListRequest:(MarketIndicatorSysRankingListReq*)stReq ;
- (MarketIndicatorSysPoolStatBaiduSearchRequest*)newBaiduSearchRequest:(MarketIndicatorSysBaiduSearchReq*)stReq ;
- (MarketIndicatorSysPoolStatPoolTraceRequest*)newPoolTraceRequest:(MarketIndicatorSysPoolTraceReq*)stReq ;
- (MarketIndicatorSysPoolStatGetHotStockRequest*)newGetHotStockRequest:(MarketIndicatorSysHotStockReq*)stReq ;
@end

#endif
