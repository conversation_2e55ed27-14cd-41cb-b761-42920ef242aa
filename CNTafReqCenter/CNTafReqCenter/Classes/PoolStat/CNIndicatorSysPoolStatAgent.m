//
//  CNIndicatorSysPoolStatAgent.m
//  CNTafReqCenter
//
//  Created by 彭继宗 on 2024/4/19.
//

#import "CNIndicatorSysPoolStatAgent.h"

@implementation CNIndicatorSysPoolStatAgent

+ (void)requestHotStocksDataForReq:(MarketIndicatorSysHotStockReq *)req result:(void (^)(MarketIndicatorSysHotStockRsp * _Nonnull))result
{
    WeakSelf(weakSelf);
    MarketIndicatorSysPoolStatAgent *agent = [[MarketIndicatorSysPoolStatAgent alloc] initWithServantName:@"pool_stat"];
   
    [[agent newGetHotStockRequest:req] enqueue:^(UPTAFResponse *response) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (response.isSuccessful) {
                MarketIndicatorSysPoolStatGetHotStockResponse *rsp = response.result;
                !result?:result(rsp.stRsp);
            }else{
                !result?:result(nil);
            }
        });
    }];
}

@end
