module IndicatorSys
{
    enum Pool_ID_Type
    {
        PT_FORMULA = 0,     //公式类型
        PT_UPS = 1,         //优品UPS 接口
        PT_FUPAN_HIS = 2,   //FuPan历史因子
    };


    enum PoolSortType
    {
        PST_BY_MAX_CHG_RATE = 0,    //涨跌幅排序
        PST_BY_CODE = 1,            //按照代码排序
        PST_BY_DATE = 2           //按照日期排序，仅recentRankingList接口支持
    };

    struct PoolIDInfo
    {
        1 optional int type = 0;    //类型Pool_ID_Type，默认是公式类型的ID,
        2 optional int id;          //股池ID，当type==PT_FORMULA和PT_FUPAN_HIS时使用
        3 optional string upsname;  //ups的名称，当type==PT_UPS时使用
    };

    struct RangeStatInfo
    {
        1 optional double beginPrice;            //入选日的收盘价，若是起始日为上市首日，则取起始日开盘价
        2 optional double highPrice;            //区间最高价
        3 optional double lastPrice;            //区间最新价
        4 optional float  chgRate;              //最高涨幅比例，已经乘了100%
    };

    //历史股池的股票统计数据
    struct StockStat
    {
        1 optional short market;
        2 optional string code;
        3 optional string name;
        4 optional string industryCode;     //所属行业代码
        5 optional string industryName;     //所属行业名称
        6 optional map<int, RangeStatInfo> mapStatInfo;     //涨跌幅统计数据，key是3、5、10等统计周期
        7 optional map<string, string>     mapExtendStr;     //扩展的字符串字段
    };

    //当日股票统计数据
    struct LastestStockStat
    {
        1 optional short market;
        2 optional string code;
        3 optional string name;
        4 optional string industryCode;     //所属行业代码
        5 optional string industryName;     //所属行业名称
        6 optional double price;            //最新价
        7 optional float  chgRatio;         //最新涨幅比例，已经乘了100%
    };

    //股池统计
    struct IndexPoolStat
    {
        1 optional int choosenDate;
        2 optional vector<StockStat> vStockStat;
    };

    //股池缓存结构
    struct MapPoolStat
    {
        //key是日期
        1 optional map<int, IndexPoolStat> mapData;
    };

    //榜单详情
    struct RankingListItem
    {
        1 optional int choosenDate;
        2 optional StockStat stockStat;
    };

    //今日股池请求参数
    struct GetLatestPoolReq
    {
        1 optional PoolIDInfo poolId;
        2 optional int maxNum = 10; //最大条数
    };

    //今日股池行情数据
    struct GetLatestPoolRsp
    {
        1 optional int tradeDate;                       //交易日
        2 optional vector<LastestStockStat> vStock;     //股池详情
    };

    //股池统计请求参数
    struct GetPoolStatReq
    {
        1 optional PoolIDInfo poolId;
        //最后交易日期，填0表示之前的历史日期都允许，这里最早的数据是T-1日， 如填写20230320，则只会返回<=20230320的数据
        2 optional int lastestDate = 0;
        3 optional int wantDates = 1;         //返回条数，从lastestDate朝前数wantDates个交易日的池子
        4 optional int maxNumPerDay = 10;     //每个交易日最大条数

        //统计周期(单位为日），如果为空，会默认返回3日最高涨幅，目前默认只允许3、5、10日，其他时间需要增加配置
        5 optional vector<int>  vStatDays;
    };

    //股池统计回包
    struct GetPoolStatRsp
    {
        1 optional vector<IndexPoolStat> vPoolStat;
    };


    //优秀案例请求参数
    struct RankingListReq
    {
        1 optional PoolIDInfo poolId;
        2 optional int minDaysBefore = 1;       //最少多少日之前的股池，必须大于0
        3 optional int maxDaysBefore = 20;      //最多多少日之前的股池，默认是只要20个交易日之内的股池
        4 optional short wantNum = 10;          //返回条数
        5 optional int sortPeriod = 5;          //排序的周期，默认是5日，支持3日、5日、10日
        6 optional bool removeDuplicate=false;  //如果某只股票出现多次，是否排除？
    };

    //优秀案例回包接口
    struct RankingListRsp
    {
        1 optional vector<RankingListItem> vRankingItem;
    };

    // 猜你想知道请求参数
    struct BaiduSearchReq
    {
        0 require string keyword;
    };

    // 猜你想知道回包
    struct BaiduSearchRsp
    {
        0 require  vector<string> keywords;
    };

    struct StockInfo
    {
        0 optional short shtMarket;             //市场
        1 optional string sCode;                //股票代码
    };

    struct PoolTraceReq
    {
        0 require vector<StockInfo> vStock;
        1 require int tradeDate;   // 交易日期，0为最新日期
    };

    struct PoolTraceItem
    {
        0 optional short shtMarket;             // 市场
        1 optional string sCode;                // 股票代码
        2 optional string sName;                // 股票名称
        3 optional int tradeDate;   // 入选日期
        4 optional int poolId;   // 入选股池id
        5 optional string poolName;   // 入选股池
        6 optional double maxIncreaseRatio20;   // 20日最高涨幅(最高价涨幅)
        7 optional double maxIncreaseRatio5;   // 5日最高涨幅(最高价涨幅)
    };

    struct PoolTraceRsp
    {
        0 optional int tradeDate = 0;   // 交易日期
        1 optional vector<PoolTraceItem> items;   // 入选股池列表
    };

    struct HotStockInfo
    {
        0 optional short  market;
        1 optional string code;
        2 optional string name;        //股票名称
        3 optional int    choosenDate; //入选日期
        4 optional string poolName;    //入选股池
        5 optional string  poolId;      //股池ID
        6 optional double maxRise;     //入选后最高涨幅
    };

    struct HotStockReq
    {
        0 optional short wantNum = 10;          //返回条数
    };

    struct HotStockRsp
    {
        0 optional vector<HotStockInfo> vStock;
    };

    interface PoolStat
    {
        //今日股池
        int getLatestPool(GetLatestPoolReq stReq, out GetLatestPoolRsp stRsp);

        //获取股池统计
        int getPoolStat(GetPoolStatReq stReq, out GetPoolStatRsp stRsp);

        //历史战绩接口
        int recentRankingList(RankingListReq stReq, out RankingListRsp stRsp);

        // 猜你想知道（百度搜索关键词）
        int baiduSearch(BaiduSearchReq stReq, out BaiduSearchRsp stRsp);

        // 入池跟踪
        int poolTrace(PoolTraceReq stReq, out PoolTraceRsp stRsp);

        //获取首页热股推荐
        int getHotStock(HotStockReq stReq, out HotStockRsp stRsp );
    };
};

