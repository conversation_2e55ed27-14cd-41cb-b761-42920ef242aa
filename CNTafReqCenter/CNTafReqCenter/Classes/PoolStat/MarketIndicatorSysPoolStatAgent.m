// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Documents/code/dongniu-ios/Tools/PoolStat.jce'
// **********************************************************************

#import "MarketIndicatorSysPoolStatAgent.h"

// MARK: MarketIndicatorSysPoolStatGetLatestPool
@implementation MarketIndicatorSysPoolStatGetLatestPoolRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"getLatestPool";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    MarketIndicatorSysPoolStatGetLatestPoolResponse * response = [[MarketIndicatorSysPoolStatGetLatestPoolResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.stRsp = [uniPacket get:@"stRsp" forClass: MarketIndicatorSysGetLatestPoolRsp.class];
    return response;
}
@end

@implementation MarketIndicatorSysPoolStatGetLatestPoolResponse 
@end

// MARK: MarketIndicatorSysPoolStatGetPoolStat
@implementation MarketIndicatorSysPoolStatGetPoolStatRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"getPoolStat";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    MarketIndicatorSysPoolStatGetPoolStatResponse * response = [[MarketIndicatorSysPoolStatGetPoolStatResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.stRsp = [uniPacket get:@"stRsp" forClass: MarketIndicatorSysGetPoolStatRsp.class];
    return response;
}
@end

@implementation MarketIndicatorSysPoolStatGetPoolStatResponse 
@end

// MARK: MarketIndicatorSysPoolStatRecentRankingList
@implementation MarketIndicatorSysPoolStatRecentRankingListRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"recentRankingList";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    MarketIndicatorSysPoolStatRecentRankingListResponse * response = [[MarketIndicatorSysPoolStatRecentRankingListResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.stRsp = [uniPacket get:@"stRsp" forClass: MarketIndicatorSysRankingListRsp.class];
    return response;
}
@end

@implementation MarketIndicatorSysPoolStatRecentRankingListResponse 
@end

// MARK: MarketIndicatorSysPoolStatBaiduSearch
@implementation MarketIndicatorSysPoolStatBaiduSearchRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"baiduSearch";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    MarketIndicatorSysPoolStatBaiduSearchResponse * response = [[MarketIndicatorSysPoolStatBaiduSearchResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.stRsp = [uniPacket get:@"stRsp" forClass: MarketIndicatorSysBaiduSearchRsp.class];
    return response;
}
@end

@implementation MarketIndicatorSysPoolStatBaiduSearchResponse 
@end

// MARK: MarketIndicatorSysPoolStatPoolTrace
@implementation MarketIndicatorSysPoolStatPoolTraceRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"poolTrace";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    MarketIndicatorSysPoolStatPoolTraceResponse * response = [[MarketIndicatorSysPoolStatPoolTraceResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.stRsp = [uniPacket get:@"stRsp" forClass: MarketIndicatorSysPoolTraceRsp.class];
    return response;
}
@end

@implementation MarketIndicatorSysPoolStatPoolTraceResponse 
@end

// MARK: MarketIndicatorSysPoolStatGetHotStock
@implementation MarketIndicatorSysPoolStatGetHotStockRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"getHotStock";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    MarketIndicatorSysPoolStatGetHotStockResponse * response = [[MarketIndicatorSysPoolStatGetHotStockResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.stRsp = [uniPacket get:@"stRsp" forClass: MarketIndicatorSysHotStockRsp.class];
    return response;
}
@end

@implementation MarketIndicatorSysPoolStatGetHotStockResponse 
@end

// MARK: MarketIndicatorSysPoolStatAgent
@implementation MarketIndicatorSysPoolStatAgent
- (instancetype)initWithServantName:(NSString *)servantName {
    self = [super init];
    if (self) {
        self.servantName = servantName;
    }
    return self;
}

- (MarketIndicatorSysPoolStatGetLatestPoolRequest*)newGetLatestPoolRequest:(MarketIndicatorSysGetLatestPoolReq*)stReq {
    MarketIndicatorSysPoolStatGetLatestPoolRequest * request = [[MarketIndicatorSysPoolStatGetLatestPoolRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
- (MarketIndicatorSysPoolStatGetPoolStatRequest*)newGetPoolStatRequest:(MarketIndicatorSysGetPoolStatReq*)stReq {
    MarketIndicatorSysPoolStatGetPoolStatRequest * request = [[MarketIndicatorSysPoolStatGetPoolStatRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
- (MarketIndicatorSysPoolStatRecentRankingListRequest*)newRecentRankingListRequest:(MarketIndicatorSysRankingListReq*)stReq {
    MarketIndicatorSysPoolStatRecentRankingListRequest * request = [[MarketIndicatorSysPoolStatRecentRankingListRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
- (MarketIndicatorSysPoolStatBaiduSearchRequest*)newBaiduSearchRequest:(MarketIndicatorSysBaiduSearchReq*)stReq {
    MarketIndicatorSysPoolStatBaiduSearchRequest * request = [[MarketIndicatorSysPoolStatBaiduSearchRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
- (MarketIndicatorSysPoolStatPoolTraceRequest*)newPoolTraceRequest:(MarketIndicatorSysPoolTraceReq*)stReq {
    MarketIndicatorSysPoolStatPoolTraceRequest * request = [[MarketIndicatorSysPoolStatPoolTraceRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
- (MarketIndicatorSysPoolStatGetHotStockRequest*)newGetHotStockRequest:(MarketIndicatorSysHotStockReq*)stReq {
    MarketIndicatorSysPoolStatGetHotStockRequest * request = [[MarketIndicatorSysPoolStatGetHotStockRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
@end
