// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/SuperStore.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>
@interface QtAstQBlockStockItem : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_market;
@property (nonatomic, strong) NSString* jce_code;
@property (nonatomic, strong) NSString* jce_name;
@property (nonatomic, assign) JceDouble jce_price;
@property (nonatomic, assign) JceDouble jce_chgrate;
@property (nonatomic, assign) JceInt64 jce_volume;
@property (nonatomic, assign) JceDouble jce_amount;
@property (nonatomic, assign) JceInt32 jce_dayzt30;
@property (nonatomic, assign) JceInt32 jce_leadup30;
@property (nonatomic, strong) NSString* jce_boardperiod;
@property (nonatomic, assign) JceDouble jce_mainnetbuy;
@property (nonatomic, assign) JceDouble jce_mainratio;
@property (nonatomic, assign) JceInt32 jce_szzl;
@property (nonatomic, strong) NSString* jce_ztreason;
@property (nonatomic, assign) JceInt32 jce_firstzttime;
@property (nonatomic, assign) JceInt32 jce_lastzttime;
@property (nonatomic, assign) JceInt32 jce_dragonheadidx;
@end

@interface QtAstQBlockStockList : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSString*, QtAstQBlockStockItem*>* jce_mapStock;
@end

@interface QtAstQPoolItem : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_market;
@property (nonatomic, strong) NSString* jce_code;
@property (nonatomic, strong) NSDictionary<NSString*, NSNumber*>* jce_mapDblValue;
@property (nonatomic, strong) NSDictionary<NSString*, NSString*>* jce_mapStrValue;
@property (nonatomic, strong) NSDictionary<NSString*, NSNumber*>* jce_mapLongValue;
@end

@interface QtAstQDailyPoolData : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_tradeDate;
@property (nonatomic, strong) NSArray<QtAstQPoolItem*>* jce_vStock;
@end



