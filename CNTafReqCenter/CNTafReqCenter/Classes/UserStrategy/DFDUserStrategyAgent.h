// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/UserStrategy.jce'
// **********************************************************************

#ifndef DFDUserStrategyAgent_h
#define DFDUserStrategyAgent_h

#import "UserStrategy.h"

#import <UPTAF/TAFRequest.h>

// MARK: DFDUserStrategyAddCombination
@interface DFDUserStrategyAddCombinationRequest : UPTAFRequest
@property (nonatomic, strong) DFDCombinationReq* stReq;
@end

@interface DFDUserStrategyAddCombinationResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) DFDCombinationRsp* stRsp;
@end

// MARK: DFDUserStrategyListCombination
@interface DFDUserStrategyListCombinationRequest : UPTAFRequest
@property (nonatomic, strong) DFDCombinationReq* stReq;
@end

@interface DFDUserStrategyListCombinationResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) DFDListCombinationRsp* stRsp;
@end

// MARK: DFDUserStrategyEditCombination
@interface DFDUserStrategyEditCombinationRequest : UPTAFRequest
@property (nonatomic, strong) DFDCombinationReq* stReq;
@end

@interface DFDUserStrategyEditCombinationResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@end

// MARK: DFDUserStrategyDeleteCombination
@interface DFDUserStrategyDeleteCombinationRequest : UPTAFRequest
@property (nonatomic, strong) DFDCombinationReq* stReq;
@end

@interface DFDUserStrategyDeleteCombinationResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@end

// MARK: DFDUserStrategyAgent
@interface DFDUserStrategyAgent : NSObject
@property (nonatomic, copy) NSString * servantName;

- (instancetype)initWithServantName:(NSString *)servantName;

- (DFDUserStrategyAddCombinationRequest*)newAddCombinationRequest:(DFDCombinationReq*)stReq ;
- (DFDUserStrategyListCombinationRequest*)newListCombinationRequest:(DFDCombinationReq*)stReq ;
- (DFDUserStrategyEditCombinationRequest*)newEditCombinationRequest:(DFDCombinationReq*)stReq ;
- (DFDUserStrategyDeleteCombinationRequest*)newDeleteCombinationRequest:(DFDCombinationReq*)stReq ;
@end

#endif
