// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/UserStrategy.jce'
// **********************************************************************

#include "UserStrategy.h"

@implementation DFDCombinationReq

@synthesize jce_id = J_PROP_NM(o,0,id);
@synthesize jce_name = J_PROP_NM(o,1,name);
@synthesize jce_content = J_PROP_NM(o,2,content);
@synthesize jce_userId = J_PROP_NM(o,3,userId);
@synthesize jce_condition = J_PROP_NM(o,4,condition);
@synthesize jce_dateType = J_PROP_NM(o,5,dateType);
@synthesize jce_scence = J_PROP_NM(r,6,scence);
@synthesize jce_factorIds = J_PROP_NM(o,7,factorIds);

- (id)init
{
    if (self = [super init]) {
        J_PROP(name) = DefaultJceString;
        J_PROP(content) = DefaultJceString;
        J_PROP(userId) = DefaultJceString;
        J_PROP(condition) = DefaultJceString;
        J_PROP(scence) = DefaultJceString;
        J_PROP(factorIds) = DefaultJceString;
    }
    return self;
}

@end

@implementation DFDCombinationRsp

@synthesize jce_id = J_PROP_NM(o,0,id);
@synthesize jce_name = J_PROP_NM(o,1,name);
@synthesize jce_content = J_PROP_NM(o,2,content);
@synthesize jce_userId = J_PROP_NM(o,3,userId);
@synthesize jce_condition = J_PROP_NM(o,4,condition);
@synthesize jce_dateType = J_PROP_NM(o,5,dateType);
@synthesize jce_factorIds = J_PROP_NM(o,6,factorIds);
@synthesize jce_factorList = J_PROP_EX(o,7,factorList,VONSString);

- (id)init
{
    if (self = [super init]) {
        J_PROP(name) = DefaultJceString;
        J_PROP(content) = DefaultJceString;
        J_PROP(userId) = DefaultJceString;
        J_PROP(condition) = DefaultJceString;
        J_PROP(factorIds) = DefaultJceString;
    }
    return self;
}

@end

@implementation DFDListCombinationRsp

@synthesize jce_list = J_PROP_EX(o,0,list,VODFDCombinationRsp);

- (id)init
{
    if (self = [super init]) {
    }
    return self;
}

@end



