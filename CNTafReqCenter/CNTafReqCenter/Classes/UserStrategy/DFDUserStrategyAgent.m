// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/UserStrategy.jce'
// **********************************************************************

#import "DFDUserStrategyAgent.h"

// MARK: DFDUserStrategyAddCombination
@implementation DFDUserStrategyAddCombinationRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"addCombination";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    DFDUserStrategyAddCombinationResponse * response = [[DFDUserStrategyAddCombinationResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.stRsp = [uniPacket get:@"stRsp" forClass: DFDCombinationRsp.class];
    return response;
}
@end

@implementation DFDUserStrategyAddCombinationResponse 
@end

// MARK: DFDUserStrategyListCombination
@implementation DFDUserStrategyListCombinationRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"listCombination";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    DFDUserStrategyListCombinationResponse * response = [[DFDUserStrategyListCombinationResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.stRsp = [uniPacket get:@"stRsp" forClass: DFDListCombinationRsp.class];
    return response;
}
@end

@implementation DFDUserStrategyListCombinationResponse 
@end

// MARK: DFDUserStrategyEditCombination
@implementation DFDUserStrategyEditCombinationRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"editCombination";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    DFDUserStrategyEditCombinationResponse * response = [[DFDUserStrategyEditCombinationResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    return response;
}
@end

@implementation DFDUserStrategyEditCombinationResponse 
@end

// MARK: DFDUserStrategyDeleteCombination
@implementation DFDUserStrategyDeleteCombinationRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"deleteCombination";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    DFDUserStrategyDeleteCombinationResponse * response = [[DFDUserStrategyDeleteCombinationResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    return response;
}
@end

@implementation DFDUserStrategyDeleteCombinationResponse 
@end

// MARK: DFDUserStrategyAgent
@implementation DFDUserStrategyAgent
- (instancetype)initWithServantName:(NSString *)servantName {
    self = [super init];
    if (self) {
        self.servantName = servantName;
    }
    return self;
}

- (DFDUserStrategyAddCombinationRequest*)newAddCombinationRequest:(DFDCombinationReq*)stReq {
    DFDUserStrategyAddCombinationRequest * request = [[DFDUserStrategyAddCombinationRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
- (DFDUserStrategyListCombinationRequest*)newListCombinationRequest:(DFDCombinationReq*)stReq {
    DFDUserStrategyListCombinationRequest * request = [[DFDUserStrategyListCombinationRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
- (DFDUserStrategyEditCombinationRequest*)newEditCombinationRequest:(DFDCombinationReq*)stReq {
    DFDUserStrategyEditCombinationRequest * request = [[DFDUserStrategyEditCombinationRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
- (DFDUserStrategyDeleteCombinationRequest*)newDeleteCombinationRequest:(DFDCombinationReq*)stReq {
    DFDUserStrategyDeleteCombinationRequest * request = [[DFDUserStrategyDeleteCombinationRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
@end
