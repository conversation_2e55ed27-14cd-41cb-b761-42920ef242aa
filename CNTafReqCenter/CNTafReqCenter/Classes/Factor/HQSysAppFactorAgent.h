// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/Factor.jce'
// **********************************************************************

#ifndef HQSysAppFactorAgent_h
#define HQSysAppFactorAgent_h

#import "Factor.h"

#import <UPTAF/TAFRequest.h>

// MARK: HQSysAppFactorGetStockFactor
@interface HQSysAppFactorGetStockFactorRequest : UPTAFRequest
@property (nonatomic, strong) HQSysAppHStockFactorReq* stReq;
@end

@interface HQSysAppFactorGetStockFactorResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) HQSysAppHStockFactorRsp* stRsp;
@end

// MARK: HQSysAppFactorAgent
@interface HQSysAppFactorAgent : NSObject
@property (nonatomic, copy) NSString * servantName;

- (instancetype)initWithServantName:(NSString *)servantName;

- (HQSysAppFactorGetStockFactorRequest*)newGetStockFactorRequest:(HQSysAppHStockFactorReq*)stReq ;
@end

#endif
