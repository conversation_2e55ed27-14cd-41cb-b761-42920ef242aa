// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `/Users/<USER>/Desktop/UPJCETool/Factor.jce'
// **********************************************************************

#import "HQSysAppFactorAgent.h"

// MARK: HQSysAppFactorGetStockFactor
@implementation HQSysAppFactorGetStockFactorRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"getStockFactor";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"stReq" value:self.stReq];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    HQSysAppFactorGetStockFactorResponse * response = [[HQSysAppFactorGetStockFactorResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.stRsp = [uniPacket get:@"stRsp" forClass: HQSysAppHStockFactorRsp.class];
    return response;
}
@end

@implementation HQSysAppFactorGetStockFactorResponse 
@end

// MARK: HQSysAppFactorAgent
@implementation HQSysAppFactorAgent
- (instancetype)initWithServantName:(NSString *)servantName {
    self = [super init];
    if (self) {
        self.servantName = servantName;
    }
    return self;
}

- (HQSysAppFactorGetStockFactorRequest*)newGetStockFactorRequest:(HQSysAppHStockFactorReq*)stReq {
    HQSysAppFactorGetStockFactorRequest * request = [[HQSysAppFactorGetStockFactorRequest alloc] init];
    request.servantName = self.servantName;
    request.stReq = stReq;
    return request;
}
@end
