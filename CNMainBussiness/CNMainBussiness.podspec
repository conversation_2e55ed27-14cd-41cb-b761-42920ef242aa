
Pod::Spec.new do |s|
  s.name             = 'CNMainBussiness'
  s.version          = '0.1.0'
  s.summary          = 'A short description of Service.'

  s.description      = <<-DESC
TODO: Add long description of the pod here.
                       DESC

  s.homepage         = 'CNMainBussiness'
  # s.screenshots     = 'www.example.com/screenshots_1', 'www.example.com/screenshots_2'
  s.license          = { :type => 'MIT', :file => 'LICENSE' }
  s.author           = { 'j' => '<EMAIL>' }
  s.source           = { :git => 'https://CNMainBussiness.git', :tag => s.version.to_s }
  # s.social_media_url = 'https://twitter.com/<TWITTER_USERNAME>'

  s.ios.deployment_target = '11.0'

  s.source_files = 'CNMainBussiness/Classes/**/*.{h,m}'
  
  s.dependency 'UPCommon'
  s.dependency 'JXCategoryView'
  

  s.prefix_header_file = 'CNMainBussiness/CNMainBussiness-pch.pch'
  
  s.ios.pod_target_xcconfig = {
    'GCC_PREPROCESSOR_DEFINITIONS' => 'UPR_MODULE=CNMainBussiness',
    'FRAMEWORK_SEARCH_PATHS' => '$(PROJECT_DIR)/../ThirdFrameworks/** $(PROJECT_DIR)/../DependencyFrameworks/**',
    'HEADER_SEARCH_PATHS' => '$(PROJECT_DIR)/../ThirdFrameworks/** $(PROJECT_DIR)/../DependencyFrameworks/**',
    'DEFINES_MODULE' => 'NO',
    'CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES' => 'YES',
  }
  s.resources = 'CNMainBussiness/Resources/*.{bundle}'
  
  s.resource_bundles = {
    'CNMainBussiness' => [
    'CNMainBussiness/Resources/*.{xcassets}',
    ]
  }
  
end
