//
//  UPNotificationService.m
//  UPStockMain
//
//  Created by x<PERSON><PERSON> on 2018/10/24.
//  Copyright © 2018 UpChina. All rights reserved.
//

#import "UPNotificationService.h"
#import <UserNotifications/UserNotifications.h>
#import <UserNotifications/UNUserNotificationCenter.h>
#import <UPTAFPush/TAFPushManager.h>

@interface UPNotificationService()

@property (nonatomic, strong) UPTAFPushNotifyMsg *originMessage;
@property (nonatomic, strong) UNMutableNotificationContent *content;

@end

@implementation UPNotificationService

+ (void)scheduleLocalNotification:(UPTAFPushNotifyMsg *)message sound:(UNNotificationSound *)sound; {
    UPNotificationService *service = [[UPNotificationService alloc] initWithMessage:message sound:sound];
    [service scheduleNotification];
}

- (instancetype)initWithMessage:(UPTAFPushNotifyMsg *)message sound:(UNNotificationSound *)sound {
    if (self = [super init]) {
        _originMessage = message;

        _content = [[UNMutableNotificationContent alloc] init];
        _content.badge = @1;
        _content.title = message.title ? : @"";
        _content.body = message.digest ? : @"";

        if (sound) {
            _content.sound = sound;
        } else {
            _content.sound = nil;
        }
        
        NSMutableDictionary *extraInfo = [[NSMutableDictionary alloc] init];
        extraInfo[@"MCMsgID"] = @(message.msgId);
        extraInfo[@"msgid"] = @(message.pushId);
        extraInfo[@"title"] = message.title;
        extraInfo[@"desc"] = message.digest;
        extraInfo[@"data"] = message.rawExt;
        extraInfo[@"taf"] = @"push";
        
        NSMutableDictionary *userInfo = [[NSMutableDictionary alloc] init];
        userInfo[@"extra"] = extraInfo;

        _content.userInfo = userInfo;
    }

    return self;
}

- (void)scheduleNotification {
    NSString *imageURL = self.originMessage.picUrl;
    NSLog(@"NotificationService handleAttachment: %@ ", imageURL);

    // 如果有图片 需要先下载图片
    if (IsValidateString(imageURL) && ([imageURL hasPrefix:@"http://"] || [imageURL hasPrefix:@"https://"])) {
        NSURLSession * session = [NSURLSession sessionWithConfiguration:[NSURLSessionConfiguration ephemeralSessionConfiguration]];
        NSURLSessionDownloadTask * task = [session downloadTaskWithURL:[NSURL URLWithString:imageURL]
                                                     completionHandler:^(NSURL *temporaryFileLocation,
                                                                         NSURLResponse *response,
                                                                         NSError *error) {
                                                         
                                                         if (!error) {
                                                             [self handleAttachment:temporaryFileLocation
                                                                           MIMEType:response.MIMEType];
                                                         } else {
                                                             NSLog(@"NotificationService downloadTask error: %@", error);
                                                             
                                                             [self handleAttachment:nil MIMEType:nil];
                                                         }

                                                         [self addNotification];
                                                     }];

        [task resume];
    } else {
        [self addNotification];
    }
}

- (void)addNotification {
    UNTimeIntervalNotificationTrigger *trigger = [UNTimeIntervalNotificationTrigger triggerWithTimeInterval:0.1 repeats:NO];
    NSString *msgId = [NSString stringWithFormat:@"%lld", self.originMessage.msgId];
    UNNotificationRequest *request = [UNNotificationRequest requestWithIdentifier:msgId content:self.content trigger:trigger];
    
    UNUserNotificationCenter *center = [UNUserNotificationCenter currentNotificationCenter];
    [center addNotificationRequest:request withCompletionHandler:^(NSError * _Nullable error) {
        if (error) {
            UPTAFLog(@"UPNotificationService", @"UPLocalNotification: %@", error.description);
        }
    }];
}

- (void)handleAttachment:(NSURL *)fileURL MIMEType:(NSString *)type {
    NSLog(@"NotificationService handleAttachment: %@ %@", fileURL, type);
    
    if (fileURL) {
        NSString * imageExt = [self fileExtensionFromMIMEType:type];
        NSData * imageData = nil;
        
        if(!imageExt) {
            // 不知道图片是什么格式, 所以都转换成PNG算了
            UIImage * uiImage = [UIImage imageWithData:[NSData dataWithContentsOfURL:fileURL]];
            
            if(uiImage) {
                imageData = UIImagePNGRepresentation(uiImage);
                imageExt = @"png";
            }
        } else {
            imageData = [NSData dataWithContentsOfURL:fileURL];
        }
        
        NSString * imageLocalPath = nil;
        
        if(imageData) {
            NSString * cachePath = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES).firstObject;
            NSString * imageName = [NSString stringWithFormat:@"NotificationServiceImage_%@.%@", fileURL.lastPathComponent, imageExt];
            NSString * imagePath = [cachePath stringByAppendingPathComponent:imageName];
            [imageData writeToFile:imagePath atomically:NO];
            
            imageLocalPath = imagePath;
        }
        
        if(imageLocalPath && imageLocalPath.length > 0) {
            NSError * attachmentError = nil;
            UNNotificationAttachment * attachment = [UNNotificationAttachment attachmentWithIdentifier:@"" URL:[NSURL fileURLWithPath:imageLocalPath] options:nil error:&attachmentError];
            if (!attachmentError) {
                self.content.attachments = [NSArray arrayWithObject:attachment];
            } else {
                NSLog(@"NotificationService handleAttachment error: %@", attachmentError);
            }
        }
    }
}

-(NSString *)fileExtensionFromMIMEType:(NSString *)type {
    if([type isEqualToString:@"image/jpeg"]) {
        return @"jpg";
    } else if([type isEqualToString:@"image/png"]) {
        return @"png";
    } else if([type isEqualToString:@"image/gif"]) {
        return @"gif";
    } else {
        return nil;
    }
}

@end
