//
//  UPThirdService.h
//  UPStockMain
//
//  Created by sammy<PERSON> on 2020/3/3.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 用于封装一些三方的服务工具, 例如Bugly, FLEX等等
// 这些工具在发布的时候都会去掉, 所以单独封装在这里, 以便打包时动态替换
@interface UPThirdService : NSObject

+(void)initCrashHanlder:(BOOL)fullInit;

+(void)showAppDebugTool;

@end

NS_ASSUME_NONNULL_END
