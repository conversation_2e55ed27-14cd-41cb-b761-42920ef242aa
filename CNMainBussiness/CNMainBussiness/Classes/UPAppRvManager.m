//
//  UPAppRvLogic.m
//  UPStockMain
//
//  Created by 彭继宗 on 2023/5/24.
//  Copyright © 2023 UpChina. All rights reserved.
//

#import "UPAppRvManager.h"
#import <UPServiceSDK/UPConfigManager.h>

@interface UPAppRvManager ()<UPConfigDelegate>

@property (nonatomic, assign) BOOL isAppConfigSuccess;

@end

@implementation UPAppRvManager

+ (instancetype)sharedManager {
    static id instance;
    
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    
    return instance;
}

- (void)tryRequestForAppInitConfig
{
    if (!self.isAppConfigSuccess) {
        [self forceRequestForAppInitConfig];
    }
}

- (void)forceRequestForAppInitConfig
{
    [UPConfigManager getRawConfig:kAPPInitConfig uid:[UPUserManager uid] delegate:self];
}


/// MARK: - UPConfigDelegate
-(void)upConfigResponse:(UPConfigError)error type:(UPConfigType)type result:(nullable id)result {
    UPTAFLog(@"Review",@"UPAppRvManager - upConfigResponse error: %lu, type: %lu", (unsigned long)error, (unsigned long)type);

    BOOL isAudit = YES;
    /***
      配置示例:
      {"L2StrictMode":"1","UniversalLinks":{"wechat":"https://hazq.upoem1.com/uphazq/","qq":"https://hazq.upoem1.com/qq_conn/101853528/"}}
     */
    if (error == UPConfigErrorNone && type == UPConfigTypeRawConfig) {
        if ([result isKindOfClass:[UPConfigRawConfigInfo class]]) {
            UPConfigRawConfigInfo *info = (UPConfigRawConfigInfo *)result;
            NSString *content = @"";
            UPTAFLog(@"Review",@"UPAppRvManager - upConfigResponse content: %@", info.content);

            if (info && info.content && info.content.length) {
                content = info.content;
                
                UPTAFJSONObject *jsonObj = [UPTAFJSONObject jsonObjectWithString:content];
                if ([jsonObj stringForKey:@"ReviewVersion"]) {
                    NSString *version = [jsonObj stringForKey:@"ReviewVersion"];
                    
                    if (![version isEqualToString:[UPDeviceInfoManager appShortVersion]]) {
                        isAudit = NO;
                    }
                }
                
            }
            self.isAppConfigSuccess = YES;
            [UPAppConfig setReviewVersion:isAudit];
        }
    }
    else
    {
        UPTAFLog(@"Review",@"UPAppRvManager - error: %lu", (unsigned long)error);
        self.isAppConfigSuccess = NO;
    }


//    [UPOpenManager setUID:[UPUserManager uid]];
}
@end
