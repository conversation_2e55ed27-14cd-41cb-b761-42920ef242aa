// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `HANewStockServer.jce'
// **********************************************************************

#ifndef HAZQHANewStockServerAgent_h
#define HAZQHANewStockServerAgent_h

#import "HANewStockServer.h"

#import <UPTAF/TAFRequest.h>

// MARK: HAZQHANewStockServerGetNewStock
@interface HAZQHANewStockServerGetNewStockRequest : UPTAFRequest
@property (nonatomic, strong) HAZQNewStockReq* req;
@end

@interface HAZQHANewStockServerGetNewStockResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) HAZQNewStockRsp* rsp;
@end

// MARK: HAZQHANewStockServerGetWinLots
@interface HAZQHANewStockServerGetWinLotsRequest : UPTAFRequest
@property (nonatomic, strong) HAZQWinLotsReq* req;
@end

@interface HAZQHANewStockServerGetWinLotsResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) HAZQWinLotsRsp* rsp;
@end

// MARK: HAZQHANewStockServerAgent
@interface HAZQHANewStockServerAgent : NSObject
@property (nonatomic, copy) NSString * servantName;

- (instancetype)initWithServantName:(NSString *)servantName;

- (HAZQHANewStockServerGetNewStockRequest*)newGetNewStockRequest:(HAZQNewStockReq*)req ;
- (HAZQHANewStockServerGetWinLotsRequest*)newGetWinLotsRequest:(HAZQWinLotsReq*)req ;
@end

#endif
