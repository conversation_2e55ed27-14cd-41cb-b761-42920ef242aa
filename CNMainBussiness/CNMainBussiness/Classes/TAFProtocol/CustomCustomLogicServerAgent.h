// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `CustomLogic.jce'
// **********************************************************************

#ifndef CustomCustomLogicServerAgent_h
#define CustomCustomLogicServerAgent_h

#import "CustomLogic.h"

#import <UPTAF/TAFRequest.h>

// MARK: CustomCustomLogicServerGetTotalWidget
@interface CustomCustomLogicServerGetTotalWidgetRequest : UPTAFRequest
@property (nonatomic, strong) CustomTotalWidgetReq* req;
@end

@interface CustomCustomLogicServerGetTotalWidgetResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) CustomTotalWidgetRsp* rsp;
@end

// MARK: CustomCustomLogicServerGetCustomWidget
@interface CustomCustomLogicServerGetCustomWidgetRequest : UPTAFRequest
@property (nonatomic, strong) CustomCustomWidgetReq* req;
@end

@interface CustomCustomLogicServerGetCustomWidgetResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) CustomCustomWidgetRsp* rsp;
@end

// MARK: CustomCustomLogicServerSetCustomWidget
@interface CustomCustomLogicServerSetCustomWidgetRequest : UPTAFRequest
@property (nonatomic, strong) CustomCustomWidgetReq* req;
@end

@interface CustomCustomLogicServerSetCustomWidgetResponse : NSObject
@property (nonatomic, assign) JceInt32 _ret;
@property (nonatomic, strong) CustomCustomWidgetRsp* rsp;
@end

// MARK: CustomCustomLogicServerAgent
@interface CustomCustomLogicServerAgent : NSObject
@property (nonatomic, copy) NSString * servantName;

- (instancetype)initWithServantName:(NSString *)servantName;

- (CustomCustomLogicServerGetTotalWidgetRequest*)newGetTotalWidgetRequest:(CustomTotalWidgetReq*)req ;
- (CustomCustomLogicServerGetCustomWidgetRequest*)newGetCustomWidgetRequest:(CustomCustomWidgetReq*)req ;
- (CustomCustomLogicServerSetCustomWidgetRequest*)newSetCustomWidgetRequest:(CustomCustomWidgetReq*)req ;
@end

#endif
