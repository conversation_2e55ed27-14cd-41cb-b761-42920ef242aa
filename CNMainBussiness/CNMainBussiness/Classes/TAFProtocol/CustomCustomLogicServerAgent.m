// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `CustomLogic.jce'
// **********************************************************************

#import "CustomCustomLogicServerAgent.h"

// MARK: CustomCustomLogicServerGetTotalWidget
@implementation CustomCustomLogicServerGetTotalWidgetRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"getTotalWidget";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"req" value:self.req];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    CustomCustomLogicServerGetTotalWidgetResponse * response = [[CustomCustomLogicServerGetTotalWidgetResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.rsp = [uniPacket get:@"rsp" forClass: CustomTotalWidgetRsp.class];
    return response;
}
@end

@implementation CustomCustomLogicServerGetTotalWidgetResponse 
@end

// MARK: CustomCustomLogicServerGetCustomWidget
@implementation CustomCustomLogicServerGetCustomWidgetRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"getCustomWidget";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"req" value:self.req];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    CustomCustomLogicServerGetCustomWidgetResponse * response = [[CustomCustomLogicServerGetCustomWidgetResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.rsp = [uniPacket get:@"rsp" forClass: CustomCustomWidgetRsp.class];
    return response;
}
@end

@implementation CustomCustomLogicServerGetCustomWidgetResponse 
@end

// MARK: CustomCustomLogicServerSetCustomWidget
@implementation CustomCustomLogicServerSetCustomWidgetRequest
- (instancetype)init {
    self = [super init];
    if (self) {
        self.funcName = @"setCustomWidget";
    }
    return self;
}

- (void)buildRequest:(UPTAFUniPacket *)uniPacket {
    [uniPacket put:@"req" value:self.req];
}

- (id)parseResponse:(UPTAFUniPacket *)uniPacket {
    CustomCustomLogicServerSetCustomWidgetResponse * response = [[CustomCustomLogicServerSetCustomWidgetResponse alloc] init];
    response._ret = [[uniPacket get:@"" forClass: NSNumber.class] intValue];
    response.rsp = [uniPacket get:@"rsp" forClass: CustomCustomWidgetRsp.class];
    return response;
}
@end

@implementation CustomCustomLogicServerSetCustomWidgetResponse 
@end

// MARK: CustomCustomLogicServerAgent
@implementation CustomCustomLogicServerAgent
- (instancetype)initWithServantName:(NSString *)servantName {
    self = [super init];
    if (self) {
        self.servantName = servantName;
    }
    return self;
}

- (CustomCustomLogicServerGetTotalWidgetRequest*)newGetTotalWidgetRequest:(CustomTotalWidgetReq*)req {
    CustomCustomLogicServerGetTotalWidgetRequest * request = [[CustomCustomLogicServerGetTotalWidgetRequest alloc] init];
    request.servantName = self.servantName;
    request.req = req;
    return request;
}
- (CustomCustomLogicServerGetCustomWidgetRequest*)newGetCustomWidgetRequest:(CustomCustomWidgetReq*)req {
    CustomCustomLogicServerGetCustomWidgetRequest * request = [[CustomCustomLogicServerGetCustomWidgetRequest alloc] init];
    request.servantName = self.servantName;
    request.req = req;
    return request;
}
- (CustomCustomLogicServerSetCustomWidgetRequest*)newSetCustomWidgetRequest:(CustomCustomWidgetReq*)req {
    CustomCustomLogicServerSetCustomWidgetRequest * request = [[CustomCustomLogicServerSetCustomWidgetRequest alloc] init];
    request.servantName = self.servantName;
    request.req = req;
    return request;
}
@end
