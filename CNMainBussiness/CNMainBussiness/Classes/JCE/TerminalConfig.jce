module CRM
{
    //按顺序下发APP首页的ICON
    struct IconInfo
    {
        0 optional  string              sLinkUrl;            //ICON链接
        1 optional  string              sImgUrl;             //图片链接
        2 optional  string              sName;               //ICON名称
        3 optional  string              sMarkUrl;           //角标图片地址
        4 optional  string              sDescription;       // 给优投顾使用的字段，对图标的描述
    };

    struct AppIndexIconRsp
    {
        0 require   int             	 iRet;
        1 require   string              sMsg;
        2 optional  vector<IconInfo>    stIconInfo;         //ICON信息
    };
    
    //我的特权ICON下发
    struct ModuleICON
    {
        0 optional string                  sImgUrl;                     //图片链接
        1 optional string                  sModuleName;                 //ICON名称
        2 optional string                  sLinkUrl;                    //ICON链接网址
        3 optional string                  sModuleId;                   //权限ICON的权限ID
        4 optional int                     iOperationFlag;              //是否是运营位，1为运营，0为正常权限
    };

    struct SendModuleICONReq
    {
        0 optional  int                    iFlag;                  //0为未登录用户、1为登录用户
        1 optional int                      iType;//图标类型 0：优品股票通   1：优投顾app  2：优投教app
    };

    struct GetModuleICONReq
    {
        0 optional  int  iFlag;      // 0 为未登录用户， 1为登录用户
        1 optional  int  iType;      // app 类型： 6： 优品股票通  1：优投顾app   2：优投教app
        2 optional  string sXua;     // app 的 xua
    };

    struct SendModuleICONRsp
    {
        0 require   int                     iRet;                  //状态码
        1 require   string                  sMsg;
        2 optional  vector<ModuleICON>      stModuleICONs;         //ICON列表
    };

    struct getAppIconMoreReq
    {
        0 optional int  iType; //图标类型 0：优品股票通   1：优投顾app  2：优投教app
        1 optional string sXua; // app的xua
    };

    interface TerminalConfig
    {
        //按顺序下发APP首页的ICON
        int getAppIndexIcon(out AppIndexIconRsp rsp);

        //按顺序下发APP首页的ICON 个 新接口
        int getAppIconMore(getAppIconMoreReq req, out AppIndexIconRsp rsp);

        //我的特权ICON下发
        int sendModuleICON(SendModuleICONReq req, out SendModuleICONRsp rsp);

        // 新版本我的特权icon下发
        // 版本在 ios6.0.3|android6.0.6 之后的app 调用
        int getModuleICON(GetModuleICONReq req, out SendModuleICONRsp rsp);
    };
};
