module PickStock
{
    enum CONDITION_RET
    {
        CONDITION_OK = 0,
        CONDITION_INCOMING_ERR = -1,        //数据传入错误
        CONDITION_FCT_NONE = -2,            //因子类型是NONE
        CONDITION_FCT_NO_DATA = -3,            //因子匹配不到股票
        CONDITION_INTERSECTION_NO_DATA = -4,   //结果交集为空
        CONDITION_FCT_NOT_SUPPORT = -5,        //不支持的因子
        CONDITION_UNKNOWN = -99,            //未知错误  
    };
   
    enum E_CONDITION_TABLE
    {
        QUANT_NONE = 0,

        QUANT_DAY = 1,                   //查询quant_fct_value_row_d表
        QUANT_SEASON_FRESH = 2,          //查询quant_fct_value_row_q_fresh表
        QUANT_CALC_DAY = 3,              //查询quant_stk_calc_d表
		QUANT_QUARTER  = 4,              //查询quant_fct_value_row_q表
		DB_FAC_SIGNAL = 5,               //查询db_facsignal表
    };
    
    enum E_CONDITION_MODEL
	{
    	MODEL_NUM0 = 0,
		MODEL_NUM1 = 1,
		MODEL_NUM2 = 2,
		MODEL_NUM3 = 3,
		MODEL_NUM4 = 4,
		MODEL_NUM5 = 5,
		MODEL_NUM6 = 6,
		MODEL_NUM7 = 7,
		MODEL_NUM8 = 8,
	};
    
    enum E_VALUE_PRECISION  //精度
	{
    	PRECISION_0D2 = 0, //保留2位小数
		PRECISION_INT = 1, //整数
	};
    
    enum E_SORT_TYPE
	{
    	SORT_TYPE_ASC = 0, // 升序
		SORT_TYPE_DESC = 1, // 降序
	};
    
    enum E_CONDITION_FACTOR
    {
        FACTOR_NONE = 0,

        //因子库待开发：规则是枚举或者数据区间
        FACTOR_MACD = 1,                 //MACD:E_FACTOR_MACD：MACD金叉、MACD顶背离、MACD底背离、MACD买入信号、MACD红二波
        FACTOR_KDJ = 2,                  //KDJ:E_FACTOR_KDJ: KDJ金叉、KDJ超卖、KDJ顶背离、KDJ底背离、买入信号
        FACTOR_BOLL = 3,                 //BOLL:E_FACTOR_BOLL: 突破下轨，突破中轨、突破上轨
        FACTOR_RSI = 4,                  //RSI:E_FACTOR_RSI: RSI金叉、RSI买入信号、RSI超卖、RSI底背离
        FACTOR_KXT = 5,                  //K线形态:E_FACTOR_KXT: MACD金叉、底部红三兵、深跌回弹、阶段新高、多方炮、量能巨变、盘整突破、小步上扬

        FACTOR_ZZCBCL = 6,               //总资产报酬率7041000067--季度
        FACTOR_ZCCJLL = 7,               //总资产净利率7041000066--季度
        FACTOR_MGJYXJL = 8,              //每股经营现金流7045000054--季度
        FACTOR_MGSY = 9,                 //每股收益7045000057--季度
        FACTOR_MGJZC = 10,               //每股净资产7045000058--季度
        FACTOR_MGWFPLR = 11,             //每股未分配利润7045000059--季度
        FACTOR_MGZBGJ = 12,              //每股资本公积7045000060--季度
        FACTOR_MGGL = 13,                //每股股利7045000061--季度
        FACTOR_JZCSYL = 14,              //净资产收益率7045000062--季度

        //因子库已有数据：规则是数据区间
        FACTOR_PB = 15,                   //市净率7041000002--日
        FACTOR_PS = 16,                   //市销率7041000003--日
        FACTOR_PCF = 17,                  //市现率7041000004--日
        FACTOR_LTSZ = 18,                 //流通市值7045000007--日
        FACTOR_ZGB = 19,                  //总股本7045000035--季度
        FACTOR_GDHS = 20,                 //股东户数7042000005--季度
        FACTOR_HJCG = 21,                 //户均持股数7042000006--季度
        FACTOR_MGSYZZL = 22,              //每股收益增长率7046000001--季度
        FACTOR_XSMLL = 23,                //销售毛利率7041000015--季度
        FACTOR_YYZSR = 24,                //营业总收入7045000032--季度
        FACTOR_YYZSRZZL = 25,             //营业总收入增长率7046000009--季度
        FACTOR_JLR = 26,                  //净利润7045000014--季度
        FACTOR_JLRZZL = 27,               //净利润增长率7046000011--季度
        FACTOR_ZCFZL = 28,                //资产负债率7041000012--季度

        //行情数据(市场+行情趋势+基本面部分)：规则是枚举或者数据区间
        FACTOR_MARKET = 29,               //市场：深市A股、创业板、中小板、沪市A股
        FACTOR_B2S = 30,                  //行业板块、概念板块的成分股--传入对应板块的code，例如：建材板块：880016
        FACTOR_PE = 31,                   //(市盈率7041000001--日)在行情数据类获取
        FACTOR_ZSZ = 32,                  //(总市值7045000026--日)在行情数据类获取
        FACTOR_CHG = 33,                  //涨跌幅
        FACTOR_HSL = 34,                  //换手率
        FACTOR_ZHENFU = 35,               //振幅
        FACTOR_AMOUNT = 36,               //成交额
        FACTOR_VOLUME = 37,               //成交量
        FACTOR_PRICE = 38,                //股价
        FACTOR_LIANGBI = 39,              //量比
        FACTOR_WEIBI = 40,                //委比

        //龙虎榜数据：规则是数据区间
        FACTOR_LHB_JGJMR = 41,            //机构净买入（所有买入营业部机构专用的机构净额相加）
        FACTOR_LHB_JGJMC = 42,            //机构净卖出：机构净额之和小于0（所有卖出营业部机构专用的机构净额相加）
        FACTOR_LHB_YYBJMR = 43,           //营业部净买入：营业部净买入净额之和排序（从大到小）取前十
        FACTOR_LHB_YYBJMC = 44,           //营业部净卖出：营业部净卖出净额之和排序（从大到小）取前十
        FACTOR_LHB_MJGMR = 45,            //多家机构买入：买入营业部中机构专用户数大于等于3的股票
        FACTOR_LHB_SBN = 46,              //近一周龙虎榜上榜次数大于等于2：某只股票连续五个交易日进入龙虎榜
        
        // : 规则是1(true)
        FACTOR_UPDOWN_SIGNAL = 47,        //搓揉线
        FACTOR_BRKTHROUGH_SIGNAL = 48,    //三档突破
        FACTOR_FIVESTAR_SIGNAL = 49,      //五星信号
        FACTOR_DROPBOUNCE_SIGNAL = 50,    //超跌反弹因子
		
		//技术因子
		FACTOR_MA = 51,                   //均线：均线多头排列、均线粘合、5日线上穿10日线、20日线支撑 
		FACTOR_CCI = 52,                  //CCI：CCI买入信号、CCI超卖、CCI底背离
		FACTOR_BIAS = 53,                 //BIAS：BIAS金叉、BIAS买入信号、BIAS超卖
		FACTOR_WR = 54,                   //WR：WR买入信号、WR金叉
		FACTOR_MTM = 55,                  //MTM：MTM金叉、MTM二次金叉
		
		//技术因子
		FACTOR_DKX = 56,                  //多空线：DKX大于MADKX的百分比
		FACTOR_DYZCMX = 57,               //多赢增仓模型：龙线绘图>X
		
		//选股范围 - 剔除因子
		FACTOR_EXCEPT_ST = 60,            //排除-S、ST、*ST、SST、S*ST的股票
		FACTOR_EXCEPT_NEW = 61,           //排除-【新股】板块中的股票
		FACTOR_EXCEPT_LOSS = 62,          //排除净利润小于0的股票
		
		//特色指标 - 日更
		FACTOR_DXLS = 70,                 //短线猎杀
		FACTOR_SSQL = 71,                 //顺势擒龙
		FACTOR_YXQN = 72,                 //一线擒牛
		FACTOR_MACD_DZQGZ = 73,           //MACD多周期共振
		FACTOR_CPLS = 74,                 //操盘论势
		FACTOR_HCNLBY = 75,               //活筹能量博弈
		FACTOR_ZJXB = 76,                 //资金谐波
		FACTOR_GSBD = 77,                 //高手波段
		FACTOR_HJZ = 78,                  //黄金柱
		FACTOR_ZJBY = 79,                 //资金博弈
		
		//特色指标-实时
		FACTOR_ZTJB = 80,                 //涨停尖兵 - 蓄能
		FACTOR_YZCJ = 81,                 //游资出击
		FACTOR_YZCP = 82,                 //有庄操盘
		FACTOR_QZCP = 83,                 //强庄操盘
		FACTOR_LZCJ = 84,                 //猎庄出击
		
		//主力行为
		FACTOR_JJZC = 85,                 //基金重仓
		FACTOR_JJZC2 = 86,                //基金增仓
		FACTOR_QSZC = 87,                 //券商重仓
		FACTOR_QSZC2 = 88,                //券商增仓
		FACTOR_XTZC = 89,                 //信托重仓
		FACTOR_XTZC2 = 90,                //信托增仓
		FACTOR_SBZC = 91,                 //社保重仓
		FACTOR_SBZC2 = 92,                //社保增仓
		FACTOR_QFII_ZC = 93,              //QFII重仓
		FACTOR_QFII_ZC2 = 94,             //QFII增仓
		FACTOR_BXZC = 95,                 //保险重仓
		FACTOR_BXZC2 = 96,                //保险增仓
		
		FACTOR_ZXLHB = 97,                //最新龙虎榜
		FACTOR_RZRQYEZJ = 98,             //融资融券余额增加
		FACTOR_SDLTGDCGBL = 99,           //十大流通股东持股比例
		
		// 财务数据
		FACTOR_GYL = 100,                 //高盈利
		FACTOR_XJN = 101,                 //现金牛
		FACTOR_GCZ = 102,                 //高成长
		FACTOR_GYY = 103,                 //高运营
		
		//行情相关因子
		FACTOR_CLSXD = 104,               //创历史新低
		FACTOR_CLSXG = 105,               //创历史新高
		FACTOR_PFPJ = 106,                //破发破净
		FACTOR_ZTG = 107,                 //涨停股
		FACTOR_QSG = 108,                 //强势股
		
		//数据中心-接口
		FACTOR_YJYZ = 109,                //业绩预增
		FACTOR_DZJYYJJY = 110,            //大宗交易溢价交易
		FACTOR_GGZC = 111,                //高管增持
		FACTOR_RZFPLZD7 = 112,            //日涨幅偏离值达7%
		FACTOR_RDFPLZD7 = 113,            //日跌幅偏离值达7%
		FACTOR_RHSLD20 = 114,             //日换手率达20%
		FACTOR_RZFZD15 = 115,             //日振幅值达15%
		FACTOR_3DAYS_ZFPLZLJ20 = 116,     //连续3个交易日，涨幅偏离值累计达20%
		FACTOR_3DAYS_DFPLZLJ20 = 117,     //连续3个交易日，跌幅偏离值累计达20%
		FACTOR_3DAYS_RJHSL_DAY5_30 = 118, //连续3个交易日，日均换手率为前五日平均值30倍
		FACTOR_1WEEK_LXSB = 119,          //连续上榜
		FACTOR_JGCY = 120,                //机构参与
		FACTOR_JGGZZDDGG = 121,           //机构关注最多的个股
		FACTOR_JGSCGZDGG = 122,           //机构首次关注的个股
		FACTOR_PJYCTGDGG = 123,           //评级预测调高的个股
		FACTOR_YLYCTGDGG = 124,           //盈利预测调高的个股
		FACTOR_MBZFZDDGG = 125,           //目标涨幅最大的个股
		
		FACTOR_MAX_NUM = 126, // 用来计数
    };

    enum E_FACTOR_MACD
    {
        MACD_JC = 1,          //MACD金叉
        MACD_TBL = 2,         //MACD顶背离
        MACD_DBL = 3,         //MACD底背离
        MACD_BUY = 4,         //MACD买入信号
        MACD_HEB = 5,         //MACD红二波
    };

    enum E_FACTOR_KDJ
    {
        KDJ_JC = 1,           //KDJ金叉
        KDJ_SUPER_SELL = 2,   //KDJ超卖
        KDJ_TBL = 3,          //KDJ顶背离
        KDJ_DBL = 4,          //KDJ底背离
        KDJ_BUY = 5,          //KDJ买入信号
    };

    enum E_FACTOR_BOLL
    {
        BOLL_BREAK_XG = 1,    //突破下轨
        BOLL_BREAK_ZG = 2,    //突破中轨
        BOLL_BREAK_SG = 3,    //突破上轨
    };

    enum E_FACTOR_RSI
    {
        RSI_JC = 1,           //RSI金叉
        RSI_BUY = 2,          //RSI买入信号
        RSI_SUPER_SELL = 3,   //RSI超卖
        RSI_DBL = 4,          //RSI底背离
    };

    enum E_FACTOR_KXT
    {
        KXT_MACD_JC = 1,      //MACD金叉
        KXT_DHSB = 2,         //底部红三兵
        KXT_SDHT = 3,         //深跌回弹
        KXT_JDXG = 4,         //阶段新高
        KXT_DFP = 5,          //多方炮
        KXT_LNJB = 6,         //量能巨变
        KXT_PZTP = 7,         //盘整突破
        KXT_XBSY = 8,         //小步上扬
    };
    
    enum E_FACTOR_ZZCBCL
	{
    	ZZCBCL_LT0 = 1,
		ZZCBCL_GT5 = 2,
		ZZCBCL_GT15 = 3,
		ZZCBCL_GT20 = 4,
		ZZCBCL_GT30 = 5,
	};
    
    enum E_FACTOR_ZCCJLL
	{
    	ZCCJLL_LT0 = 1,
		ZCCJLL_GT5 = 2,
		ZCCJLL_GT15 = 3,
		ZCCJLL_GT20 = 4,
		ZCCJLL_GT30 = 5,
	};
    
    enum E_FACTOR_MGJYXJL
	{
    	MGJYXJL_LT0 = 1,
		MGJYXJL_BW0_0D3 = 2,
		MGJYXJL_BW0D3_0D5 = 3,
		MGJYXJL_GT0D5 = 4,
	};
    
    enum E_FACTOR_MGSY
	{
    	MGSY_LT0 = 1,
		MGSY_GT0 = 2,
		MGSY_GT0D3 = 3,
		MGSY_GT0D5 = 4,
	};
    
    enum E_FACTOR_MGJZC
	{
    	MGJZC_LT0 = 1,
		MGJZC_GT0 = 2,
		MGJZC_GT1 = 3,
		MGJZC_GT3 = 4,
		MGJZC_GT5 = 5,
	};
    
    enum E_FACTOR_MGWFPLR
	{
    	MGWFPLR_LT0 = 1,
		MGWFPLR_BW0_1 = 2,
		MGWFPLR_BW1_3 = 3,
		MGWFPLR_GT3 = 4,
	};
    
    enum E_FACTOR_MGZBGJ
	{
    	MGZBGJ_LT1 = 1,
		MGZBGJ_BW1_2 = 2,
		MGZBGJ_BW2_5 = 3,
		MGZBGJ_GT5 = 4,
	};
    
    enum E_FACTOR_MGGL
	{
    	MGGL_LT0D2 = 1,
		MGGL_BW0D2_0D5 = 2,
		MGGL_GT0D5 = 3,
	};
    
    enum E_FACTOR_JZCSYL
	{
    	JZCSYL_GT0 = 1,
		JZCSYL_BW0_5 = 2,
		JZCSYL_BW5_15 = 3,
		JZCSYL_GT15 = 4,
		JZCSYL_GT20 = 5,
		JZCSYL_GT30 = 6,
	};
    
    enum E_FACTOR_PB
	{
    	PB_BW0_1 = 1,
		PB_BW1_2 = 2,
		PB_BW2_5 = 3,
		PB_GT5 = 4,
	};
    
    enum E_FACTOR_PS
	{
    	PS_BW0_1 = 1,
		PS_BW1_2 = 2,
		PS_BW2_5 = 3,
		PS_GT5 = 4,
	};
    
    enum E_FACTOR_PCF
	{
    	PCF_LT0 = 1,
		PCF_GT0 = 2,
		PCF_GT10 = 3,
		PCF_GT50 = 4,
		PCF_GT100 = 5,
	};
    
    enum E_FACTOR_LTSZ
	{
    	LTSZ_LT20E = 1,
		LTSZ_BW20E_50E = 2,
		LTSZ_BW50E_100E = 3,
		LTSZ_GT100E = 4,
	};
    
    enum E_FACTOR_ZGB
	{
    	ZGB_LT2E = 1,
		ZGB_BW2E_5E = 2,
		ZGB_BW5E_10E = 3,
		ZGB_GT10E = 4,
	};
    
    enum E_FACTOR_GDHS
	{
    	GDHS_LT5K = 1,
		GDHS_BW5K_10K = 2,
		GDHS_BW10K_50K = 3,
		GDHS_GT50K = 4,
	};
    
    enum E_FACTOR_HJCG
	{
    	HJCG_LT5K = 1,
		HJCG_BW5K_10K = 2,
		HJCG_BW10K_50K = 3,
		HJCG_GT50K = 4,
	};
    
    enum E_FACTOR_MGSYZZL
	{
    	MGSYZZL_LT0 = 1,
		MGSYZZL_BW0_100 = 2,
		MGSYZZL_BW100_500 = 3,
		MGSYZZL_GT500 = 4,
	};

    enum E_FACTOR_XSMLL
	{
    	XSMLL_LT20 = 1,
		XSMLL_BW20_50 = 2,
		XSMLL_BW50_70 = 3,
		XSMLL_GT70 = 4,
	};
    
    enum E_FACTOR_YYZSR
	{
    	YYZSR_LT10E = 1,
		YYZSR_GT100E = 2,
		YYZSR_GT1000E = 3,
	};
    
    enum E_FACTOR_YYZSRZZL
	{
    	YYZSRZZL_GT5 = 1,
		YYZSRZZL_GT10 = 2,
		YYZSRZZL_GT30 = 3,
		YYZSRZZL_GT50 = 4,
		YYZSRZZL_GT100 = 5,
	};
    
    enum E_FACTOR_JLR
	{
    	JLR_GT0 = 1,
		JLR_BW0_1E = 2,
		JLR_BW1E_10E = 3,
		JLR_GT10E = 4,
	};
    
    enum E_FACTOR_JLRZZL
	{
    	JLRZZL_GT5 = 1,
		JLRZZL_GT10 = 2,
		JLRZZL_GT30 = 3,
		JLRZZL_GT50 = 4,
		JLRZZL_GT100 = 5,
	};
    
    enum E_FACTOR_ZCFZL
	{
    	ZCFZL_LT10 = 1,
		ZCFZL_LT30 = 2,
		ZCFZL_LT50 = 3,
		ZCFZL_LT100 = 4,
	};
    
    enum E_FACTOR_MARKET
    {
        MARKET_SHA = 1,      //沪市A股
        MARKET_SZA = 2,      //深市A股
        MARKET_CY = 3,       //创业板
        MARKET_SM = 4,       //中小板
    };
    
    enum E_FACTOR_PE
	{
    	PE_GT0 = 1,
		PE_BW0_25 = 2,
		PE_BW25_50 = 3,
		PE_GT50 = 4,
	};
    
    enum E_FACTOR_ZSZ
	{
    	ZSZ_LT20E = 1,
		ZSZ_BW20E_50E = 2,
		ZSZ_BW50E_100E = 3,
		ZSZ_GT100E = 4,
	};
    
    enum E_FACTOR_CHG
	{
    	CHG_LTN5 = 1,
		CHG_BWN5_0 = 2,
		CHG_BW0_5 = 3,
		CHG_GT5 = 4,
	};
    
    enum E_FACTOR_HSL
	{
    	HSL_LT1 = 1,
		HSL_BW1_3 = 2,
		HSL_BW3_5 = 3,
		HSL_GT5 = 4,
	};
    
    enum E_FACTOR_ZHENFU
	{
    	ZHENFU_LT5 = 1,
		ZHENFU_BW5_10 = 2,
		ZHENFU_BW10_15 = 3,
		ZHENFU_GT15 = 4,
	};
    
    enum E_FACTOR_AMOUNT
	{
    	AMOUNT_LT1E = 1,
		AMOUNT_BW1E_5E = 2,
		AMOUNT_BW5E_10E = 3,
		AMOUNT_GT10E = 4,
	};
    
    enum E_FACTOR_VOLUME
	{
    	VOLUME_LT3K = 1,
		VOLUME_BW3K_5K = 2,
		VOLUME_BW5K_10K = 3,
		VOLUME_GT10K = 4,
	};
    
    enum E_FACTOR_PRICE
	{
    	PRICE_LT10 = 1,
		PRICE_BW10_50 = 2,
		PRICE_BW50_100 = 3,
		PRICE_GT100 = 4,
	};
    
    enum E_FACTOR_LIANGBI
	{
    	LIANGBI_LT1 = 1,
		LIANGBI_BW1_3 = 2,
		LIANGBI_BW3_5 = 3,
		LIANGBI_GT5 = 4,
	};
    
    enum E_FACTOR_WEIBI
	{
    	WEIBI_LT0 = 1,
		WEIBI_BW0_30 = 2,
		WEIBI_BW30_50 = 3,
		WEIBI_GT50 = 4,
	};
    
    enum E_FACTOR_LHB_JGJMR
	{
    	LHB_JGJMR1 = 1,
	};
    
    enum E_FACTOR_LHB_JGJMC
	{
    	LHB_JGJMC1 = 1,
	};
    
    enum E_FACTOR_LHB_YYBJMR
	{
    	LHB_YYBJMR1 = 1,
	};
    
    enum E_FACTOR_LHB_YYBJMC
	{
    	LHB_YYBJMC1 = 1,
	};
    
    enum E_FACTOR_LHB_MJGMR
	{
    	LHB_MJGMR1 = 1,
	};
    
    enum E_FACTOR_LHB_SBN
	{
    	LHB_SBN1 = 1,
	};
    
    enum E_FACTOR_UPDOWN_SIGNAL
	{
    	UPDOWN_SIGNAL1 = 1,
	};
    
    enum E_FACTOR_BRKTHROUGH_SIGNAL
	{
    	BRKTHROUGH_SIGNAL1 = 1,
	};
    
    enum E_FACTOR_FIVESTAR_SIGNAL
	{
    	FIVESTAR_SIGNAL1 = 1,
	};
	
    enum E_FACTOR_DROPBOUNCE_SIGNAL
	{
    	DROPBOUNCE_SIGNAL1 = 1,
	};
    
    enum E_FACTOR_MA
	{
    	MA_DTPL = 1,  // 多头排列
		MA_ZH = 2,  // 均线粘合
		MA_5RX_SC_10RX = 3,  // 5日线上穿10日线
		MA_20RXZC = 4,  // 20日线支撑
	};
    
    enum E_FACTOR_CCI
	{
    	CCI_MRXH = 1,  // CCI买入信号
		CCI_CM = 2,  // CCI超卖
		CCI_DBL = 3,  // CCI底背离
	};
    
    enum E_FACTOR_BIAS
	{
    	BIAS_JC = 1,  // BIAS金叉
		BIAS_MRXH = 2,  // BIAS买入信号
		BIAS_CM = 3,  // BIAS超卖
	};
    
    enum E_FACTOR_WR
	{
    	WR_MRXH = 1,  // WR买入信号
		WR_JC = 2,  // WR金叉
	};
    
    enum E_FACTOR_MTM
	{
    	MTM_JC = 1,  // MTM金叉
		MTM_MR = 2,  // MTM买入
		MTM_ECJC = 3,  // MTM二次金叉
	};
    
    enum E_FACTOR_DKX
	{
    	DKX_DKX_GT_MADKX = 1,  // DKX大于MADKX的百分比
	};
    
    enum E_FACTOR_DYZCMX
	{
    	DYZCMX_LXHT = 1,  // 龙线绘图>X
	};
    
    enum E_FACTOR_EXCEPT_ST
	{
    	EXCEPT_ST1 = 1,
	};
    
    enum E_FACTOR_EXCEPT_NEW
	{
    	EXCEPT_NEW1 = 1,
	};
    
    enum E_FACTOR_EXCEPT_LOSS
	{
    	EXCEPT_LOSS1 = 1,
	};
    
    enum E_FACTOR_DXLS
	{
    	DXLS1 = 1,
	};
    
    enum E_FACTOR_SSQL
	{
    	SSQL1 = 1,
	};
    
    enum E_FACTOR_YXQN
	{
    	YXQN1 = 1,
	};
    
    enum E_FACTOR_MACD_DZQGZ
	{
    	MACD_DZQGZ1 = 1,
	};
    
    enum E_FACTOR_CPLS
	{
    	CPLS1 = 1,
	};
    
    enum E_FACTOR_HCNLBY
	{
    	HCNLBY1 = 1,
	};
    
    enum E_FACTOR_ZJXB
	{
    	ZJXB1 = 1,
	};
    
    enum E_FACTOR_GSBD
	{
    	GSBD1 = 1,
	};
    
    enum E_FACTOR_HJZ
	{
    	HJZ1 = 1,
	};
    
    enum E_FACTOR_ZJBY
	{
    	ZJBY1 = 1,
	};
    
    enum E_FACTOR_ZTJB
	{
    	ZTJB1 = 1,
	};
    
    enum E_FACTOR_YZCJ
	{
    	YZCJ1 = 1,
	};
    
    enum E_FACTOR_YZCP
	{
    	YZCP1 = 1,
	};
    
    enum E_FACTOR_QZCP
	{
    	QZCP1 = 1,
	};
    
    enum E_FACTOR_LZCJ
	{
    	LZCJ1 = 1,
	};
    
    
    enum E_FACTOR_JJZC
	{
    	JJZC1 = 1,
	};
    
    enum E_FACTOR_JJZC2
	{
    	JJZC2_1 = 1,
	};
    
    enum E_FACTOR_QSZC
	{
    	QSZC1 = 1,
	};
    
    enum E_FACTOR_QSZC2
	{
    	QSZC2_1 = 1,
	};
    
    enum E_FACTOR_XTZC
	{
    	XTZC = 1,
	};
    
    enum E_FACTOR_XTZC2
	{
    	XTZC2_1 = 1,
	};
    
    enum E_FACTOR_SBZC
	{
    	SBZC1 = 1,
	};
    
    enum E_FACTOR_SBZC2
	{
    	SBZC2_1 = 1,
	};
    
    enum E_FACTOR_QFII_ZC
	{
    	QFII_ZC1 = 1,
	};
    
    enum E_FACTOR_QFII_ZC2
	{
    	QFII_ZC2_1 = 1,
	};
    
    enum E_FACTOR_BXZC
	{
    	BXZC1 = 1,
	};
    
    enum E_FACTOR_BXZC2
	{
    	BXZC2_1 = 1,
	};
    
    enum E_FACTOR_ZXLHB
	{
    	ZXLBH1 = 1,
	};
    
    enum E_FACTOR_RZRQYEZJ
	{
    	RZRQYEZJ1 = 1,
	};
    
    enum E_FACTOR_SDLTGDCGBL
	{
    	SDLTGDCGBL_GT5 = 1,
		SDLTGDCGBL_GT10 = 2,
		SDLTGDCGBL_GT15 = 3,
		SDLTGDCGBL_GT20 = 4,
		SDLTGDCGBL_GT30 = 5,
	};
    
    enum E_FACTOR_GYL
	{
    	GYL1 = 1,
	};
    
    enum E_FACTOR_XJN
	{
    	XJN1 = 1,
	};
    
    enum E_FACTOR_GCZ
	{
    	GCZ1 = 1,
	};
    
    enum E_FACTOR_GYY
	{
    	GYY1 = 1,
	};
    
    enum E_FACTOR_CLSXD
	{
    	CLSXD_DAY1 = 1,
		CLSXD_DAY3 = 2,
		CLSXD_DAY5 = 3,
		CLSXD_DAY10 = 4,
	};
    
    enum E_FACTOR_CLSXG
	{
    	CLXSG_DAY1 = 1,
		CLXSG_DAY3 = 2,
		CLXSG_DAY5 = 3,
		CLXSG_DAY10 = 4,
	};
    
    enum E_FACTOR_PFPJ
	{
    	PFPJ_PF = 1, //破发
		PFPF_PJ = 2, //破净
	};
    
    enum E_FACTOR_ZTG
	{
    	ZTG_KPZT = 1, //开盘涨停
		ZTG_2DAY5 = 2, //5个交易i日有2+次涨停
		ZTG_LXZT = 3, //连续涨停(大于等于2)
	};
    
    enum E_FACTOR_QSG
	{
    	QSG_LXSZ3 = 1, //连续上涨天数大于3
		QSG_3DAY10 = 2, //3个交易日涨幅大于10%
	};
    
    enum E_FACTOR_YJYZ
	{
    	YJYZ1 = 1, // 业绩大幅上升
		YJYZ2 = 3, // 上升小于50%
		YJYZ5 = 5, // 预盈
		YJYZ7 = 7, // 扭亏
		YJYZ8 = 8, // 减亏
		YJYZ9 = 9, // 持平 
		YJYZ12 = 12, // 预增
	};
    
    enum E_FACTOR_DZJYYJJY
	{
    	DZJYYJJY1 = 1, //
	};
    
    enum E_FACTOR_GGZC
	{
    	GGZC1 = 1,
	};
    
    enum E_FACTOR_RZFPLZD7
	{
    	RZFPLZD7_1 = 1,
	};
    
    enum E_FACTOR_RDFPLZD7
	{
    	RDFPLZD7_1 = 1,
	};
    
    enum E_FACTOR_RHSLD20
	{
    	RHSLD20_1 = 1,
	};
    
    enum E_FACTOR_RZFZD15
	{
    	RZFZD15_1 = 1,
	};
    
    enum E_FACTOR_3DAYS_ZFPLZLJ20
	{
    	DAYS3_ZFPLZLJ20_1 = 1,
	};
    
    enum E_FACTOR_3DAYS_DFPLZLJ20
	{
    	DAYS3_DFPLZLJ20_1 = 1,
	};
    
    enum E_FACTOR_3DAYS_RJHSL_DAY5_30
	{
    	DAYS3_RJHSL_DAY5_30_1 = 1,
	};
    
    enum E_FACTOR_1WEEK_LXSB
	{
    	WEEK1_LXSB1 = 1,
	};
    
    enum E_FACTOR_JGCY
	{
    	JGCY1 = 1,
	};
    
    enum E_FACTOR_JGGZZDDGG
	{
    	JGGZZDDGG1 = 1,
	};
    
    enum E_FACTOR_JGSCGZDGG
	{
    	JGSCGZDGG1 = 1,
	};
    
    enum E_FACTOR_PJYCTGDGG
	{
    	PJYCTGDGG1 = 1,
	};
    
    enum E_FACTOR_YLYCTGDGG
	{
    	YLYCTGDGG1 = 1,
	};
    
    enum E_FACTOR_MBZFZDDGG
	{
    	MBZFZDDGG1 = 1,
	};
    
    
    enum E_HISTORY_PICK_SORT
	{
    	HISTORY_PICK_SORT_NONE = 0,
		HISTORY_PICK_SORT_DAY10_INC = 1,  // 按照10日最大收益排序-从大到小
		HISTORY_PICK_SORT_DAY10_INC_REVERSE = 2,  // 按照10日最大收益逆序排序-从小到大
		HISTORY_PICK_SORT_DAY5_INC = 3,   // 按照5日最大收益排序-从大到小
		HISTORY_PICK_SORT_DAY5_INC_REVERSE = 4,   // 按照5日最大收益逆序排序-从小到大
	};
    
    enum E_DATA_SAMPLE_TYPE
	{
    	// 源数据
    	DATA_SAMPLE_OPEN = 0,  // 开盘价
		DATE_SAMPLE_CLOSE = 1, // 收盘价
		DATA_SAMPLE_HIGH = 2,  // 最高价
		DATA_SAMPLE_LOW = 3,  // 最低价
		DATA_SAMPLE_DATETIME = 4, // 交易日
		
		// 技术因子
		DATA_SAMPLE_TECH_MACD = 100,  // MACD
		DATA_SAMPLE_TECH_KDJ = 101,  // KDJ
		DATA_SAMPLE_TECH_BOLL = 102,  // BOLL
		DATA_SAMPLE_TECH_RSI = 103,  // RSI
		DATA_SAMPLE_TECH_KXT = 104,  // KXT
	};
    
    enum E_TYPE_SIGNAL
	{
    	SIGNAL_NONE = 0,  // 无信号
		SIGNAL_BUY = 1,   // 买入信号
		SIGNAL_SELL = 2,  // 卖出信号
	};
    
    enum E_TYPE_REAL_TIME
	{
    	REAL_TIME_NO = 0, //非实时
    	REAL_TIME_YES = 1, //实时
	};
    
    //因子字段枚举
    enum E_TYPE_FIELD
	{
    	FIELD_SYL = 1,
		FIELD_SJL = 2,
		FIELD_GDHS = 3,
		FIELD_RJCG = 4,
		FIELD_JGJMRE = 5,
		FIELD_JGJMCE = 6,
		FIELD_YYBMRJE = 7,
		FIELD_YYBMCJE = 8,
		FIELD_MRJGHS = 9,
		FIELD_YZSBCS = 10,
		FIELD_MGJYXJL = 11,
		FIELD_MGSYZCL = 12,
		FIELD_ZZCBCL = 13,
		FIELD_ZZCJLL = 14,
		FIELD_XSMLL = 15,
		FIELD_YYZSR = 16,
		FIELD_YSZZL = 17,
		FIELD_JLR = 18,
		FIELD_JLRZZL = 19,
		FIELD_ZCFZL = 20,
		FIELD_ZSZ = 21,
		FIELD_LTSZ = 22,
		FIELD_ZGB = 23,
		FIELD_SXL = 24,
		FIELD_SXL2 = 25,
		FIELD_MGSY = 26,
		FIELD_MGJZC = 27,
		FIELD_MGWFPLR = 28,
		FIELD_MGZBGJ = 29,
		FIELD_MGGL = 30,
		FIELD_JZCSYL = 31,
		FIELD_HSL = 32, //换手率
		FIELD_ZF = 33, //振幅
		FIELD_CJE = 34,
		FIELD_CJL = 35,
		FIELD_LB = 36,
		FIELD_WB = 37,
		FIELD_SC = 38,
		FIELD_SSHY = 39, //所属行业
		FIELD_RXSJ = 40, //特色指标-入选时间
		FIELD_RXJG = 41, //特色指标-入选价格
		FIELD_RXHZF = 42, //特色指标-入选后涨幅
		FIELD_CGJS = 43, //持股家数
		FIELD_CGBD = 44, //持股变动
		FIELD_JMRE = 45, //净买入额
		FIELD_YECZ = 46, //余额差值
		FIELD_SDLTGDCGBL = 47, //十大流通股东持股比例
		FIELD_YYSRTBZZ = 48, //营业收入同比增长
		FIELD_ZZCZZL = 49, //总资产周转率
		FIELD_SPJ = 50, //收盘价
		FIELD_YJYZLX = 51, // 业绩预增类型
		FIELD_YJL = 52, //溢价率
		FIELD_BDGS = 53, //变动股数
		FIELD_ZFPLZ = 54, //涨幅偏离值
		FIELD_DFPLZ = 55, //跌幅偏离值
		FIELD_ZFLJPLZ = 56, //涨幅累计偏离值
		FIELD_DFLJPLZ = 57, //跌幅累计偏离值
		FIELD_RJHSL = 58, //日均换手率
		FIELD_SBCS = 59, //上榜次数
		FIELD_JGCYS = 60, //机构参与数
		FIELD_JGJS = 61, //机构家数
		FIELD_ZGMBJ = 62, //最高目标价
		FIELD_ZDMBJ = 63, //最低目标价
		FIELD_PJ = 64, //评级
		FIELD_MBJ = 65, //目标价
		FIELD_PJBD = 66, //评级变动
		FIELD_BCYC = 67, //本次预测
		FIELD_QCYC = 68, //前次预测
		FIELD_MBZF = 69, //目标涨幅
		FIELD_FXJ = 70, // 发行价
	};

    /**
    * 具体条件: 不同的条件选择方式不同，一个因子只能有一个方式
    * （1）区间匹配
    * （2）因子枚举
    * （3）板块code
    */
    struct ConditionItem
    {    
        1 require E_CONDITION_FACTOR eFactor = FACTOR_NONE;
        
        //方式一：区间查找
        2 optional double dMinValue = -1125899906842624.0; //-2^50
        3 optional double dMaxValue = 1125899906842624.0; //2^50
        
        //方式二：因子枚举, 例如MACD因子：MACD金叉、MACD顶背离、MACD底背离、MACD买入信号、MACD红二波
        4 optional int iValue = 0;

        //方式三：板块code, 主要是行业板块，概念板块 例如：建材板块：880016
        5 optional string sCode = "";
        
        6 optional string sName = "";  // eFactor描述
		7 optional string sSubName = "";  // iValue描述
		
		8 optional E_TYPE_SIGNAL eSignal = SIGNAL_NONE;  // 信号类型
		9 optional E_TYPE_REAL_TIME eRealTime = REAL_TIME_NO; //实时
		
		10 optional int iGroupId; //分组ID
        
    };
    
    struct UniqueStock
    {
        1 optional short shtMarket;
        2 optional string sCode;
    };    
    
    struct PickStockHq
    {
        0 optional unsigned short shtPrecise;    //精度
        1 optional short shtMarket;            //股票市场
        2 optional string sCode;                //股票代码
        3 optional string sName;                //股票名称
        4 optional double fNowPrice;             //现价
        5 optional double fOpen;                 //开盘价
        6 optional double fHigh;                 //最高价
        7 optional double fLow;                  //最低价
        8 optional double fClose;                //昨日收盘价
        9 optional long lVolume;                 //成交量：股数
        10 optional double fAmount;               //成交额
        11 optional byte bTransactionStatus;      //交易状态标志：‘O’表示其它状态‘P’停牌‘C’集合竞价‘H’暂停交易‘T’连续交易‘B’午休‘E’闭市
        12 optional int iType;                    //股票类型 
        13 optional double dPeRatio;              //市盈率
        14 optional double dTotalMarketValue;     //总市值
        15 optional double dLiangBi;              //量比 = 成交量 / (五日均量 * 开盘分钟数)
        16 optional double dWeiBi;                //委比 =（委买-委卖）/（委买+委卖）*100%
        17 optional double dTurnoverRate;         //换手率
        18 optional double dZGB;                  //总股本
    };
    
    struct SearchStockHq
	{
    	1 optional UniqueStock stStockId;
    	2 optional string sStockName;     // 股票名称
    	3 optional double fNowPrice;      // 最新价
    	4 optional double dChangeRate;    //涨跌幅度
		5 optional double fOpen;  // 开盘价
		6 optional double fHigh;  // 最高价 
	};
    
    enum E_TARGET_HQ_DETAIL
	{
    	HQ_DETAIL_NONE = 0,  // 不返回行情
		HQ_DETAIL_BASIC = 1, // TargetHq
		HQ_DETAIL_EXT = 2,   // TargetHq + TargetHqExt
	};
    
    //标的股行情
    struct TargetHq
    {
        1 optional double dPrice;              //现价
        2 optional double dChange;             //涨跌额
        3 optional double dChangeRate;         //涨跌幅度
        4 optional byte bTransactionStatus;    //交易状态标志：‘O’表示其它状态‘P’停牌‘C’集合竞价‘H’暂停交易‘T’连续交易‘B’午休‘E’闭市
		5 optional string sName;               //股票名称
    };
    
    //标的股行情-扩展
    struct TargetHqExt
    {
		1 optional long lVolume;               //成交量：股数
		2 optional double fAmount;             //成交额
		3 optional double dPeRatio;            //市盈率
    };
    
    // 因子字段
    struct FactorField
    {
    	1 optional E_TYPE_FIELD eType;
    	2 optional string sName;
    	3 optional string sUnit;
    	4 optional string sValue;
    	5 optional E_VALUE_PRECISION ePrec; //精度
    };
    
    struct FactorFieldVector
    {
    	1 optional vector<FactorField> vField;
    };
    
    //标的股
    struct TargetStockInfo
    {
        1 require UniqueStock stStockId;
        2 optional TargetHq   stStockHq;  //行情
        3 optional map<E_CONDITION_FACTOR, double> mFctValue;
        4 optional TargetHqExt stStockHqExt;  // 行情扩展
        5 optional vector<FactorField> vField; //根据因子返回不同字段
        6 optional int iTime; // 数据最新更新时间-年月日（例如20180101，即2018年1月1日）
    };
    
    
    /////////////////////////策略相关信息//////////////////////////
    enum E_STRATEGY_TYPE
    {
        EST_USER = 0, 
        EST_SYS_STRATEGY1 = 1,  //系统策略1
		EST_SYS_STRATEGY2 = 2,  //系统策略2
		EST_SYS_STRATEGY3 = 3,  //系统策略3
		EST_SYS_STRATEGY4 = 4,  //系统策略4
    };
    
    struct StrategyDetail
    {
        1 require vector<ConditionItem> vCondition;
    };
    
    //策略信息
    struct StrategyInfo
    {
        1 require string sName;
        2 optional E_STRATEGY_TYPE eType = EST_USER;
        3 optional StrategyDetail stDetail;
        
        //选股方案介绍，目前只有系统策略需要
        4 optional string sIntro;  // 简介
        5 optional string sTitle;  // 副标题
        // 策略更新的时戳
        6 optional int iTimestamp;  // 
        7 optional int iStrategyId;  // 策略Id
        8 optional string sIconUrl; // 图标url
    };
    
    // 每日股票池
    struct DailyPickStock
    {
    	1 require UniqueStock stStockId; 
    	2 optional string sStrategyName;  // 选股模型-名称
    	3 optional string sStockName;     // 股票名称
    	4 optional double fNowPrice;      // 最新价
    	5 optional double dChangeRate;    //涨跌幅度
    	6 optional int iStrategyId;  // 策略ID-对应数据库中的ID
    };
    
    // 每日股票池-多日
    struct DailyPickStockDateTime
    {
		1 optional int iDate; // 选股的日期
		2 optional int iTime; // 选股更新的时间
		3 optional vector<DailyPickStock> vStock;  // 股票池
    };
    
    // 历史牛股
    struct HistoryPickStock
    {
    	1 require UniqueStock stStockId;
    	2 optional string sStockName;  // 股票名称
    	3 optional int iDate;  // 股票入选日期
    	4 optional double fDay5Inc;  // 5日最大涨幅
    	5 optional double fDay10Ince;  // 10日最大涨幅
    };
    
    // 买入信号（卖出信号）
    struct SignalListStock
    {
    	1 require UniqueStock stStockId;
    	2 optional string sStockName;  // 股票名称
    	3 optional vector<ConditionItem> vItem;
		4 optional double dChangeRate;  // 涨跌幅度
		5 optional double fNowPrice;  // 最新价
		6 optional byte bTransactionStatus;  //交易状态标志：‘O’表示其它状态‘P’停牌‘C’集合竞价‘H’暂停交易‘T’连续交易‘B’休市‘E’闭市
    };
    
    // 系统策略每日回测
    struct TableDailyPickStock
    {
    	1 optional int iDate;
    	2 optional int iTime;
    	3 optional int iStrategyId;
    	4 optional UniqueStock stStockId;
    	5 optional float fPrice;
    	6 optional float fDay1Chg;
    	7 optional float fDay2Chg;
    	8 optional float fDay3Chg;
    	9 optional float fDay5Chg;
    	10 optional float fDay10Chg;
    	11 optional FactorFieldVector stFieldV;
    	12 optional E_TYPE_REAL_TIME eRealTime = REAL_TIME_NO; //实时
    };
    
    //回测-持有期
    enum E_STOCK_HOLD_DAY
	{
    	STOCK_HOLD_DAY1 = 1, //1天
		STOCK_HOLD_DAY2 = 2, //2天
		STOCK_HOLD_DAY3 = 3, //3天
		STOCK_HOLD_DAY5 = 5, //5天
		STOCK_HOLD_DAY10 = 10, //10天
	};
    
    // 回测结构
    struct BacktestResult
    {
    	1 optional E_STOCK_HOLD_DAY eDay; //持有期
		2 optional double dRate; //上涨概率
		3 optional double dChg; //平均涨跌幅
    };
    
    // 用户策略统计
    struct UserStrategyCountData
    {
    	1 optional ConditionItem stItem; // 因子
    	2 optional int iNum; // 因子个数
    	3 optional float fRatio; //因子占比
    };
    
    struct PsHeaderInfo
    {
    	1 optional string sOrgId; // 机构ID
    };
}; 
