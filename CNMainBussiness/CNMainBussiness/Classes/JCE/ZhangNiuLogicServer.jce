module YTG
{
    enum E_STATUS_CODE
    {
        ESC_Success             = 0,                // 成功

        ESC_Invalid_Param       = -4000,            // 参数错误
        ESC_Illegal_Req         = -4001,            // 非法请求
        ESC_NotExist            = -4004,		    // 数据不存在

        ESC_Server_Error        = -5000,            // 系统内部错误
        ESC_Server_Busy         = -5001,            // 服务器繁忙（eg. rpc出现-13001...）
        ESC_Dirty_Record        = -5002,            // 数据版本错误，即出现了“脏数据”

        EC_Unknown              = 9999              // 未知值
    };

    struct PushOrderInfoReq
    {
        0 optional   string                  sOrderId;               // 订单ID
        1 optional   string                  sUid;                   // 用户UID
        2 optional   long                    iTime;                  // 发生时间
        3 optional   int                     iStatus;                // 订单状态，180：新建、220：已开通、90：已退款、80：已正常过期、70：逾期未支付（失效）

        4 optional   string                  sProductId;             // 产品ID，对于E投顾业务的组合产品，多个子产品以竖线分隔开
        5 optional   int                     iTotalPrice;            // 订单价格
        6 optional   int                     iPayTotal;              // 支付金额
        7 optional   string                  sTGUid;                 // 投顾老师UID
        8 optional   int                     iChannel;               // 渠道号
        9 optional   int                     iPlat;                  // 下单平台（0-PC、1-Android、2-IOS）
        10 optional  int                     iAppVersion;            // APP版本号
        11 optional  int                     iTradeType;             // 1-购买、2-打赏
        12 optional  string                  sExt1;                  // 扩展信息（JSON串）
        13 optional  int                     iTotalDay;              // 自定义的开通时长，单位：天
    };

    struct PushOrderInfoRsp
    {
        0 optional   int                     iRet;                   // 状态码
        1 optional   string                  sMsg;                   // 描述
    };

    struct VideoInfo
       {
            0 optional int iId;
            1 optional string  sCode;                                             //课程编号
            2 optional string  sRightcode;                                        //权限编号
            3 optional string  sTitle;                                            //课程标题
            4 optional int  iType;                                                //课程类型:5:股市大讲堂（直播）;  6.短视频
            5 optional int  iTeacherId;                                           //讲师编号
            6 optional string  sTeacherName;                                      //讲师名
            7 optional string  sLiveVideoUrl;                                     //直播地址
            8 optional string  sVideourl;                                         //录播地址
            9 optional string  sImgurl;                                           //课程封面
            10 optional string  sImgurl1;                                         //小图，app上用,但现在一般没人设置,还是都用sImgurl
            11 optional string  sStartimgurl;                                     //开始封面
            12 optional string  sEndimgurl;                                       //结束封面
            13 optional int  iStarttime;                                          //开播时间
            14 optional int  iEndtime;                                            //结束时间
            15 optional string  sDateTime;                                        //格式化日期字符串
            16 optional string  sTags;                                            //标签
            17 optional string sTagNames;                                         //标签名
            18 optional int  iStageid;                                            //阶段
            19 optional string  sSuitable;                                        //合适人群
            20 optional string  sRemark;                                          //简介
            21 optional int  iPlaycount;                                          //播放次数
            22 optional int  iVideoStatus;                                        //状态
    };
    struct AppIndexVideoInfo
       {
            0 optional int iId;
            1 optional string  sTitle;                                            //课程标题
            2 optional string  sTeacherName;                                      //讲师名
            3 optional string  sIndexPageUrl;                                     //优直播首页地址
            4 optional string  sLinkUrl;                                          //优直播视频播放页地址
            5 optional string  sImgurl;                                           //课程封面
            6 optional int  iStarttime;                                           //开播时间
            7 optional int  iEndtime;                                             //结束时间
            8 optional string  sDateTime;                                         //格式化日期字符串
            9 optional string  sTags;                                             //标签
            10 optional string sTagNames;                                         //标签名
            11 optional int  iPlaycount;                                          //播放次数
            12 optional int  iVideoStatus;                                        //状态
            13 optional int  iType;                                               //课程类型 5:股市大讲堂（直播）;  6.短视频
    };
    struct GetVideoListReq{
		0 require  int iType;                                   //课程类型:5:股市大讲堂（直播）;  6.短视频
        1 optional  int iVideoStatus;                           //视频状态1：直播中  2：转码中  3：回看  4：未开播
        2 optional int   iCount;                                //需要获取的条数
        3 optional string sBid="appIndex";                      //业务标识
	};
	struct GetTouguVideoListReq{
        0 require  int iType;                                   //课程类型:5:股市大讲堂（直播）;  6.短视频
        1 optional  int iVideoStatus;                           //视频状态1：直播中  2：转码中  3：回看  4：未开播 0：all
        2 optional int   iCount=0;                              //需要获取的条数
        3 optional string sBid="tougu";                         //业务标识  可直接传入项目名称
        4 optional int iTeacherId=0;                            //讲师编号
        5 optional int iContainWillPlayHour=12;                 //包含多少小时以内即将播放的视频   12：包含未来12小时内未开播的视频   1个星期为:168小时
    };
	struct GetVideoListRsp{
        0 optional  int iErrCode;  //状态码  0为成功
        1 optional  string sErrMsg;   //状态信息
        2 optional  vector<VideoInfo> vVideoInfo;
    };
    struct GetAppIndexVideoListRsp{
        0 optional  int iErrCode;  //状态码  0为成功
        1 optional  string sErrMsg;   //状态信息
        2 optional  vector<AppIndexVideoInfo> vVideoInfo;
    };
    struct TeacherInfo{
        0 optional int iId;
        1 optional string  sTeacherName;                                  //讲师名
        2 optional string  sImgurl;                                       //讲师头像
        3 optional string  sPosition;                                     //职位
        4 optional string  sDesc;                                         //简介
        5 optional string  sPlatfrom;                                     //讲师来源
        6 optional int  iCreateTime;                                      //创建时间
        7 optional string sExt1;                                          //扩展数据
    };
    struct GetTeacherByTgIdReq{
        0 require  int iTgId;                                   //投顾编号
        1 optional string sBid="etg-h5";                        //业务标识
    };
    struct GetTeacherByTgIdRsp{
        0 optional  int iErrCode;  //状态码  0为成功
        1 optional  string sErrMsg;   //状态信息
        2 optional  TeacherInfo oTeacherInfo; //讲师信息
    };
    interface ZhangNiuLogicServer
    {
        // TODO 定义相关接口
        int getVideoList(GetVideoListReq req, out GetVideoListRsp rsp);//获取视频列表  暂时没有人用
        int getAppIndexVideoList(GetVideoListReq req, out GetAppIndexVideoListRsp rsp);//app首页获取视频列表
        int getTouguVideoList(GetTouguVideoListReq req, out GetVideoListRsp rsp);//投顾空间获取视频
        int PushOrderInfo(PushOrderInfoReq req, out PushOrderInfoRsp rsp); //打赏订单同步
        int getTeacherByTgId(GetTeacherByTgIdReq req, out GetTeacherByTgIdRsp rsp); //跟据投顾id获取优直播讲师信息
    };
};
