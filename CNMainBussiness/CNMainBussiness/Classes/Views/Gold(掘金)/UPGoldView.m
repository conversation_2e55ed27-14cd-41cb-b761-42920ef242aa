//
//  UPGoldView.m
//  UPStockMain
//
//  Created by lwd on 2022/9/14.
//  Copyright © 2022 UpChina. All rights reserved.
//

#import "UPGoldView.h"
#import "UPGoldCell.h"
//#import <UPDongfangSDK/UPDongfangSDK.h>
#import <UPCommon/UPDFHttpQuestManager.h>

@interface UPGoldView ()<UITableViewDelegate,UITableViewDataSource>
@property (nonatomic, strong) UITableView * tableView;
@property (nonatomic, copy) void (^scrollCallback) (UIScrollView *scrollView);
@property (nonatomic, strong) NSArray * dataSource;
@end

@implementation UPGoldView
- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

- (UITableView *)tableView {
    if(!_tableView) {
        _tableView = [[UITableView alloc] init];
        _tableView.backgroundColor = UIColor.up_contentBgColor;
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_tableView registerClass:[UPGoldCell class] forCellReuseIdentifier:@"UPGoldCell"];
        [_tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"UITableViewCell"];

    }
    return _tableView;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataSource.count + 1;
}

- (UITableViewCell*)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row < self.dataSource.count) {
        UPGoldCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UPGoldCell"];
        cell.model = self.dataSource[indexPath.row];
        return cell;
    } else {
        UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UITableViewCell"];
        
        UIView *vv = [[UIView alloc] init];
        vv.backgroundColor = UIColor.up_contentBgColor;
        UILabel *label = [[UILabel alloc] init];
        label.text = @"查看更多";
        label.font = [UIFont up_fontOfSize:14];
        label.textColor = UIColor.up_bgHotStockTextColor;
        label.textAlignment = NSTextAlignmentLeft;
        [cell.contentView addSubview:vv];
        [vv mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.bottom.equalTo(cell.contentView);
            make.height.mas_equalTo(UPHeight(64));
        }];
        [vv addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(vv);
        }];
        return cell;
    }
    
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 40;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    UIView * view = [[UIView alloc] init];
    view.backgroundColor = UIColor.whiteColor;
    
    UIView * iconView = [[UIView alloc] init];
    iconView.backgroundColor = UIColor.up_brandColor;
    iconView.frame = CGRectMake(14, (40-12)/2, 5, 12);
    iconView.layer.cornerRadius = 2.5;
    iconView.layer.masksToBounds = YES;
    [view addSubview:iconView];
    
    UILabel *label = [[UILabel alloc] init];
    label.text = @"龙头掘金";
    label.font = [UIFont up_boldFontOfSize:18];
    label.textColor = UIColor.up_textPrimaryColor;
    label.textAlignment = NSTextAlignmentLeft;
    [view addSubview:label];
    label.frame = CGRectMake(30, 0, 78, 40);
    
    UIImageView * more = [[UIImageView alloc] initWithImage:UPTImg(@"智选/更多")];
    [view addSubview:more];
    [more mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(view);
        make.right.equalTo(view).offset(-15);
        make.width.equalTo(@5);
        make.height.equalTo(@9);
    }];
    
    UITapGestureRecognizer * tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(gotoMore)];
    [view addGestureRecognizer:tap];
    
    return view;
}

//- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
//
//        UIView *vv = [[UIView alloc] init];
//        vv.backgroundColor = UIColor.up_contentBgColor;
//        UILabel *label = [[UILabel alloc] init];
//        label.text = @"查看更多";
//        label.font = [UIFont up_fontOfSize:14];
//        label.textColor = UIColor.up_bgHotStockTextColor;
//        label.textAlignment = NSTextAlignmentLeft;
//        
//        [vv addSubview:label];
//        [label mas_makeConstraints:^(MASConstraintMaker *make) {
//            make.center.equalTo(vv);
//        }];
//        UITapGestureRecognizer * tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(gotoMore)];
//        [vv addGestureRecognizer:tap];
//        return vv;
//}

//- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
//    return 64;
//}




- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row < self.dataSource.count) {
        UPGoldModel * model = self.dataSource[indexPath.row];
//        NSLog(@"%@",[NSString stringWithFormat:@"http://estock.app.dn8188.com/h5/dn_test_academy/#/GraphicArticle?id=%@",model.goldId]);
        [UPRouter navigate:model.detailLink];
        [UPNewsManager addHasRead:model.goldId];
    } else {
        [self gotoMore];
    }
    
    [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
}

// MARK: - UPPagerViewListViewDelegate
- (UIView *)listView {
    return self;
}

- (UIScrollView *)listScrollView {
    return self.tableView;
}

- (void)listViewDidScrollCallback:(void (^)(UIScrollView *scrollView))callback {
    self.scrollCallback = callback;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    !self.scrollCallback ?: self.scrollCallback(scrollView);
}

- (void)refreshDataFinished:(nonnull void (^)(void))finished {
    [self requestData];
    if (finished) {
        finished();
    }
}

- (void)requestData {
    WeakSelf(weakSelf);
    [[UPDFHttpQuestManager sharedInstance] getDFGoldData:^(NSArray<UPGoldModel *> * _Nonnull array) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakSelf.dataSource = array;
            [weakSelf.tableView reloadData];
        });
    }];
}

- (void)gotoMore {
    [UPRouter navigate:UPURLHomeViewJJLBlUrl];
}

@end
