//
//  UPSettingListCell.m
//  UPUser
//
//  Created by <PERSON> on 17/4/12.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import "UPStockSettingListCell.h"

@interface UPStockSettingListCell ()

@property (nonatomic, strong) UILabel *leftTitleLabel;

@property (nonatomic, strong) UILabel *rightAccessoryLabel;

@property (nonatomic, strong) UIImageView *rightAccessoryView;

@end

@implementation UPStockSettingListCell

-(instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self) {
        [self setupViews];
    }
    return self;
}

/**
 初始化
 */
- (void)setupViews {
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    [self.contentView addSubview:self.leftTitleLabel];
    [self.leftTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.centerY.equalTo(self.contentView);
    }];

    [self.contentView addSubview:self.rightAccessoryView];
    [self.rightAccessoryView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView).offset(-15);
        make.centerY.equalTo(self.contentView);
    }];

    [self.contentView addSubview:self.rightAccessoryLabel];
    [self.rightAccessoryLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.leftTitleLabel.mas_right).offset(10).priorityHigh();
        make.right.equalTo(self.contentView).offset(-36);
        make.centerY.equalTo(self.contentView);
    }];

}


// MARK: - SETTER & GETTER
- (void)setHideAccessoryArrow:(BOOL)hideAccessoryArrow {
    _hideAccessoryArrow = hideAccessoryArrow;
    self.rightAccessoryView.hidden = hideAccessoryArrow;

    if (self.rightAccessoryView.hidden == hideAccessoryArrow) {
        [self.rightAccessoryLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.contentView).offset(-12);
            make.centerY.equalTo(self.contentView);
        }];
    } else {
        [self.rightAccessoryLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.contentView).offset(-36);
            make.centerY.equalTo(self.contentView);
        }];
    }

}

- (void)setTitle:(NSString *)title {
    _title = title;
    self.leftTitleLabel.text = title;
}

- (void)setAccessoryTitle:(NSString *)accessoryTitle {
    _accessoryTitle = accessoryTitle;
    self.rightAccessoryLabel.text = accessoryTitle;
}


- (UILabel *)leftTitleLabel {
    if (!_leftTitleLabel) {
        _leftTitleLabel = [[UILabel alloc] init];
        _leftTitleLabel.textColor = UIColor.up_textPrimaryColor;
        _leftTitleLabel.font = [UIFont up_fontOfSize:16];
        [_leftTitleLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh
                                                         forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _leftTitleLabel;
}

- (UILabel *)rightAccessoryLabel {
    if (!_rightAccessoryLabel) {
        _rightAccessoryLabel = [[UILabel alloc] init];
        _rightAccessoryLabel.font = [UIFont up_fontOfSize:15];
        _rightAccessoryLabel.textColor = UIColor.up_textSecondary1Color;
        _rightAccessoryLabel.textAlignment = NSTextAlignmentRight;
        _rightAccessoryLabel.lineBreakMode = NSLineBreakByTruncatingMiddle;
        [_rightAccessoryLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow
                                                         forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _rightAccessoryLabel;
}

- (UIImageView *)rightAccessoryView {
    if (!_rightAccessoryView) {
        _rightAccessoryView = [[UIImageView alloc] initWithImage:UPTImgNoCache(@"User/我的-设置-箭头")];
    }
    return _rightAccessoryView;
}

@end
