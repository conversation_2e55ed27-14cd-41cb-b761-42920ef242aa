//
//  UPNotifyAnimationView.h
//  UPCommon
//
//  Created by caoxk on 2021/11/3.
//

#import <UIKit/UIKit.h>
#import <UPTAFPush/TAFMessageManager.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^UPNotifyAnimationViewHandler)(UPTAFPushNotifyMsg *msg);

@interface UPNotifyAnimationView : UIView

@property (strong, nonatomic) UIView *contentView;

+ (void)showWithMessage:(UPTAFPushNotifyMsg *)message parent:(UIView *)parent handler:(UPNotifyAnimationViewHandler)handler;

@end

NS_ASSUME_NONNULL_END
