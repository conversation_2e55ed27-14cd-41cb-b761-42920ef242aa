//
//  UPHomeMainLiveViewCell.m
//  UPStockMain
//
//  Created by liz<PERSON>xiang on 2022/9/20.
//  Copyright © 2022 UpChina. All rights reserved.
//

#import "UPHomeMainLiveViewCell.h"
#import "UPHomeMainLiveHeaderView.h"


@interface UPLiveViewItemView : UIView

@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *timeLable;
@property (nonatomic, strong) UIView *timeView;
@property (nonatomic, strong) UIView *bgView;

@property (nonatomic, strong) UIImageView *markImageView;
@property (nonatomic, strong) UILabel *lableLab;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *teacherLable;
@property (nonatomic, strong) UIButton *joinButton;



@property (nonatomic, strong) UPLiveRoomModel *roomModel;

- (void)configUIWithIconModel:(UPLiveRoomModel *)roomModel;
@property (nonatomic, copy) void(^clickJoinButtonBlock)(UPLiveRoomModel *model);
@property (nonatomic, copy) void(^jumpUrlBlock)(UPLiveRoomModel *model);

@end

@implementation UPLiveViewItemView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = UIColor.up_contentBgColor;
        self.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(jumpUrl)];
        [self addGestureRecognizer:tap];
        [self setupUI];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.layer.shadowColor = [UIColor up_colorFromHexString:@"#4275D8"].CGColor;
    self.layer.shadowOffset = CGSizeMake(0, 0);
    self.layer.shadowOpacity = 0.1;
    self.layer.shadowRadius = 12;
    self.bgView.clipsToBounds = YES;
    self.bgView.layer.cornerRadius = 6.f;
}
//|live_status |Integer  直播状态 0今日预告 1直播中 2 回放 3明日预告
- (void)configUIWithIconModel:(UPLiveRoomModel *)roomModel {
    self.roomModel = roomModel;
    [self.markImageView up_setImageWithURLString:roomModel.coverImage placeholderImage:UPTImg(@"Home/首页-占位图")];
    self.teacherLable.text = [NSString stringWithFormat:@"讲师 %@",roomModel.realName];
//    self.lableLab.text = roomModel.liveRoomName;
//    self.titleLabel.text = [NSString stringWithFormat:@"              %@",roomModel.liveTitle];
    self.titleLabel.attributedText = [self.titleLabel up_labelText:roomModel.thirdCategoryName withLabelTextColor:UIColor.up_bgHotStockTextColor detailText:roomModel.title detailTextColor:UIColor.up_textPrimaryColor];
    if (roomModel.liveState == 0 || roomModel.liveState == 3)  {
//        self.iconView.backgroundColor = [UIColor up_colorFromHexString:@"#FB6401"];
        self.iconImageView.image = UPTImg(@"Home/观点-预告中");
//        self.iconlableView.text = @"预约中";
        self.timeLable.text = [NSString stringWithFormat:@"%@ %@-%@",[roomModel.publishTime substringWithRange:NSMakeRange(5, 5)],[roomModel.liveStartTime substringToIndex:roomModel.liveStartTime.length -3],[roomModel.liveEndTime substringToIndex:roomModel.liveEndTime.length - 3]];
//        if (roomModel.liveState == 3) {
//            self.timeLable.text = [NSString stringWithFormat:@"%@ %@-%@",[roomModel.publishTime substringWithRange:NSMakeRange(5, 5)],[roomModel.liveStartTime substringToIndex:roomModel.liveStartTime.length -3],[roomModel.liveEndTime substringToIndex:roomModel.liveEndTime.length - 3]];
//        } else {
//            self.timeLable.text = [NSString stringWithFormat:@"今天 %@-%@",[roomModel.liveStartTime substringToIndex:roomModel.liveStartTime.length -3],[roomModel.liveEndTime substringToIndex:roomModel.liveEndTime.length - 3]];
//        }

    } else if (roomModel.liveState == 1) {
        self.iconImageView.image = UPTImg(@"Home/观点-直播中");

        self.timeLable.text = [NSString stringWithFormat:@"今天 %@-%@",[roomModel.liveStartTime substringToIndex:roomModel.liveStartTime.length -3],[roomModel.liveEndTime substringToIndex:roomModel.liveEndTime.length - 3]];

    } else {
        self.iconImageView.image = UPTImg(@"Home/观点-休息中");
        self.timeLable.hidden = YES;

    }
        if (!roomModel.flag && ![UPAppConfig isRving]) {
            [self.joinButton setImage:UPTImg(@"智选/自选锁") forState:0];
            self.joinButton.imageView.contentMode = UIViewContentModeScaleAspectFit;
            [self.joinButton setTitle:@"" forState:0];
            [self.joinButton up_setBackgroundColor:UIColor.up_contentBgColor forState:0];
            [self.joinButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(self.markImageView.mas_right).offset(UPWidth(-15));
                make.centerY.equalTo(self.teacherLable.mas_centerY);
//                make.size.mas_equalTo(CGSizeMake(UPWidth(20), UPWidth(20)));
//                make.top.equalTo(self.markImageView.mas_bottom).offset(UPHeight(62));
                
            }];
        } else {
            if (roomModel.liveState == 0 || roomModel.liveState == 3) {
                if (!roomModel.subscribeState) {
                    [self.joinButton up_setBackgroundColor:[UIColor up_colorFromHexString:@"#FB6401"] forState:0];
                    [self.joinButton setTitleColor:UIColor.up_contentBgColor forState:0];
                    [self.joinButton setTitle:@"预约" forState:0];
                   
                } else {
                    [self.joinButton up_setBackgroundColor:UIColor.up_textSecondary2Color forState:0];
                    [self.joinButton setTitleColor:UIColor.up_textSecondary1Color forState:0];
                    [self.joinButton setTitle:@"已预约" forState:0];
                }
            } else if (roomModel.liveState == 2) {
                [self.joinButton setTitleColor:UIColor.up_textPrimaryColor forState:0];
                [self.joinButton up_setBackgroundColor:UIColor.up_contentBgColor forState:0];
                [self.joinButton setTitle:@"看回放" forState:0];
                self.joinButton.layer.borderColor = [UIColor up_colorFromHexString:@"#C3CBD6"].CGColor;
                self.joinButton.layer.borderWidth = 1.f;
            } else {

                [self.joinButton setTitleColor:UIColor.up_contentBgColor forState:0];
                [self.joinButton up_setBackgroundColor:UIColor.up_brandColor forState:0];
                [self.joinButton setTitle:@"马上看" forState:0];

            }
            [self.joinButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(self.markImageView.mas_right).offset(UPWidth(-15));
                make.centerY.equalTo(self.teacherLable.mas_centerY);
//                make.top.equalTo(self.markImageView.mas_bottom).offset(UPHeight(62));
                make.size.mas_equalTo(CGSizeMake(55, 23));

            }];
        }
        
}

- (void)setupUI {
    
    self.bgView = [[UIView alloc] init];
    self.bgView.backgroundColor = UIColor.up_contentBgColor;
    [self addSubview:self.bgView];
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);

    }];
    
    [self.bgView addSubview:self.markImageView];

//    [self.markImageView addSubview:self.timeView];
    [self.markImageView addSubview:self.iconImageView];
    [self.markImageView addSubview:self.timeLable];

//    [self addSubview:self.lableLab];
    [self.bgView addSubview:self.titleLabel];
    [self.bgView addSubview:self.teacherLable];
    [self.bgView addSubview:self.joinButton];

    [self.markImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(self.bgView);
        make.height.equalTo(@(UPHeight(112)));
    }];
    
//    [self.timeView mas_updateConstraints:^(MASConstraintMaker *make) {
//        make.left.top.right.equalTo(self.markImageView);
//        make.height.equalTo(@20);
//    }];
    
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.markImageView);
        make.size.mas_equalTo(CGSizeMake(167, 20));
    }];

    
    [self.timeLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.iconImageView.mas_centerY);
        make.left.equalTo(self.iconImageView.mas_left).offset(UPWidth(58));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.markImageView.mas_left).offset(UPWidth(11));
        make.right.equalTo(self.markImageView.mas_right).offset(UPWidth(-7));
        make.top.equalTo(self.markImageView.mas_bottom).offset(UPHeight(8));
    }];
    
//    [self.lableLab mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.top.equalTo(self.titleLabel);
//        make.size.mas_equalTo(CGSizeMake(UPWidth(50), UPHeight(18)));
////        make.right.equalTo(self.markImageView);
////        make.top.equalTo(self.markImageView.mas_bottom).offset(8);
//    }];
    
    [self.teacherLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel);
        make.bottom.equalTo(self.mas_bottom).offset(UPHeight(-17));
        make.top.equalTo(self.markImageView.mas_bottom).offset(UPHeight(59));
    }];
    
    [self.joinButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.titleLabel);
        make.bottom.equalTo(self.teacherLable.mas_bottom);
        make.top.equalTo(self.markImageView.mas_bottom).offset(UPHeight(62));
        make.size.mas_equalTo(CGSizeMake(55, 23));
    }];
    
}


// MARK: - Getter && Setter
- (UIImageView *)iconImageView {
    if (!_iconImageView) {
        _iconImageView = [[UIImageView alloc] initWithImage:UPTImg(@"Home/观点-直播中")];
    }

    return _iconImageView;
}

- (UIImageView *)markImageView {
    if (!_markImageView) {
        _markImageView = [[UIImageView alloc] initWithImage:UPTImg(@"Home/首页-占位图")];
        [_markImageView setContentMode:UIViewContentModeScaleAspectFill];
        _markImageView.clipsToBounds = YES;
    }
    
    return _markImageView;
}

- (UILabel *)timeLable {
    if (!_timeLable) {
        _timeLable = [[UILabel alloc] init];
        _timeLable.textColor = UIColor.up_contentBgColor;
        _timeLable.font = [UIFont up_mediumfontOfSize:12];
        _timeLable.textAlignment = NSTextAlignmentLeft;
        _timeLable.text = @"今天 17:00-18:00";
    }
    return _timeLable;
}



- (UIView *)timeView {
    if (!_timeView) {
        _timeView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 167, 20)];
        _timeView.backgroundColor = [UIColor up_colorFromHexString:@"#80FFFFFF"];
    }
    return _timeView;
}

- (UILabel *)lableLab {
    if (!_lableLab) {
        _lableLab = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, UPWidth(50), UPHeight(18))];
        _lableLab.backgroundColor = UIColor.up_contentBgColor;
        _lableLab.font = [UIFont up_fontOfSize:10];
        _lableLab.layer.masksToBounds = YES;
        _lableLab.layer.cornerRadius = 1;
        _lableLab.layer.borderWidth = 1;    //边框宽度
        _lableLab.layer.borderColor = UIColor.up_bgHotStockTextColor.CGColor;
        
        _lableLab.textColor = UIColor.up_bgHotStockTextColor;
        _lableLab.textAlignment = NSTextAlignmentCenter;
        _lableLab.font = [UIFont up_fontOfSize:10];
        
    }
    return _lableLab;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.font = [UIFont up_mediumfontOfSize:15];
        _titleLabel.textAlignment = NSTextAlignmentLeft;
        _titleLabel.numberOfLines = 2;
        _titleLabel.text = @"    打盘强势反包如何操作？";
    }
    return _titleLabel;
}

- (UILabel *)teacherLable {
    if (!_teacherLable) {
        _teacherLable = [[UILabel alloc] init];
        _teacherLable.textColor = UIColor.up_textSecondary1Color;
        _teacherLable.font = [UIFont up_fontOfSize:12];
        _teacherLable.textAlignment = NSTextAlignmentLeft;
        _teacherLable.text = @"讲师 小白";
    }
    return _teacherLable;
}

- (UIButton *)joinButton {
    if (!_joinButton) {
        _joinButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_joinButton setTitle:@"马上看" forState:0];
        _joinButton.frame = CGRectMake(0, 0, 55, 23);
        _joinButton.titleLabel.font = [UIFont up_mediumfontOfSize:12];
        [_joinButton setTitleColor:UIColor.up_contentBgColor forState:0];
//        [_joinButton up_setBackgroundColor:UIColor.up_brandColor forState:0];
        [_joinButton addTarget:self action:@selector(addClick) forControlEvents:UIControlEventTouchUpInside];
        _joinButton.layer.cornerRadius = 4;
        _joinButton.layer.masksToBounds = YES;
    }
    return _joinButton;
}

- (void)addClick {
    [DNStatisticMananger clickWithId:DNSCENEID_SY_GDZB eventmemo:self.roomModel.title contentid:[NSString stringWithFormat:@"%d",self.roomModel.planId]];
    self.clickJoinButtonBlock(self.roomModel);
}

- (void)jumpUrl {
    [DNStatisticMananger clickWithId:DNSCENEID_SY_GDZB eventmemo:self.roomModel.title contentid:[NSString stringWithFormat:@"%d",self.roomModel.planId]];
    self.jumpUrlBlock(self.roomModel);
    
}
    
@end

@interface UPHomeMainLiveViewCell () <UIScrollViewDelegate>

@property (nonatomic, strong) UIScrollView *scrollView;

@property (nonatomic, copy) NSArray <UPLiveRoomModel *>*pDataArr;

@property (nonatomic, strong) UIView *scrollContentView;

@property (nonatomic, strong) UPPageControl *pageControl;


@property (nonatomic, strong) UPHomeMainLiveHeaderView *headerView;



@end

@implementation UPHomeMainLiveViewCell


- (void)configUIWithLiveArr:(NSArray<UPLiveRoomModel *> *)liveArr {
    self.headerView.hidden = NO;
    self.headerView.model = liveArr[0];
    NSMutableArray *MliveArr = [NSMutableArray arrayWithArray:liveArr];
    [MliveArr removeObjectAtIndex:0];
    [self setupSubviewsWithLiveArr:MliveArr];
}


- (void)configUINoData {
    self.headerView.hidden = YES;
    [self.headerView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_top).offset(UPHeight(0));
        make.height.equalTo(@0.5);
    }];
    [self.scrollView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@1);
        make.bottom.equalTo(self.contentView.mas_bottom);
    }];
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.backgroundColor = UIColor.up_contentBgColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}



- (void)setupUI {
    self.headerView = [[UPHomeMainLiveHeaderView alloc] init];
    WeakSelf(weakSelf);
    self.headerView.clickJoinButtonBlock = ^(UPLiveRoomModel * _Nonnull model) {
        weakSelf.clickJoinButtonBlock(model);
    };
    self.headerView.jumpUrlBlock = ^(UPLiveRoomModel * _Nonnull model) {
        weakSelf.jumpUrlBlock(model);
    };
    [self.contentView addSubview:self.headerView];
    [self.contentView addSubview:self.scrollView];
    [self.scrollView addSubview:self.scrollContentView];
//    [self.contentView addSubview:self.pageControl];
    
    [self.headerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left).offset(UPWidth(15));
        make.top.equalTo(self.contentView.mas_top).offset(UPHeight(15));
        make.right.equalTo(self.contentView.mas_right).offset(UPWidth(-15));
        make.height.equalTo(@200);
    }];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.edges.equalTo(self).insets(UIEdgeInsetsMake(0, 15, 30, 15));
        make.top.equalTo(self.headerView.mas_bottom).offset(UPHeight(15));
        make.left.equalTo(self.contentView.mas_left).offset(UPWidth(15));
        make.right.equalTo(self.contentView.mas_right).offset(UPWidth(-15));
        make.height.equalTo(@222);
        make.bottom.equalTo(self.contentView.mas_bottom).offset(UPHeight(-13));

    }];
    
    
//    [self.pageControl mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerX.equalTo(self.contentView);
//        make.top.equalTo(self.scrollView.mas_bottom).offset(UPHeight(15));
//        make.bottom.equalTo(self.contentView.mas_bottom).offset(UPHeight(-10));
//        make.size.mas_equalTo(CGSizeMake(40, 6));
//    }];
        
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
//    self.pageControl.frame = CGRectMake((self.frame.size.width - 40)/2, self.frame.size.height - 20, 40, 6);
    
    NSInteger column = 2;
//    NSInteger row = ceil(self.pDataArr.count / 2.0) > 2?2:ceil(self.pDataArr.count / 2.0);
    CGFloat margin = 15.f;
    CGFloat width = 200;
    CGFloat height = 215;
    NSInteger pageCount = self.pDataArr.count / column;
//    if (self.pDataArr.count % column) {
//        pageCount = pageCount + 1;
//    }
    self.scrollContentView.frame = CGRectMake(0, 0, self.pDataArr.count * UPWidth(220), 215);
    self.scrollView.contentSize = self.scrollContentView.frame.size;
    
    [self.pDataArr enumerateObjectsUsingBlock:^(UPLiveRoomModel *model, NSUInteger idx, BOOL *stop) {
        UPLiveViewItemView *itemView = [self.scrollContentView viewWithTag:1000 + idx];
        NSInteger page = idx / column;
//        CGFloat left = ;
        if (idx == 0) {
            itemView.frame = CGRectMake(0, 0, width,  height);
        } else {
            itemView.frame = CGRectMake((width + margin) *idx, 0, width,  height);
        }
//        itemView.layer.cornerRadius = 6.f;
////        itemView.layer.masksToBounds = YES;
    }];
}

- (void)setupSubviewsWithLiveArr:(NSArray<UPLiveRoomModel *>*)liveArr {
    if (self.pDataArr.count != liveArr.count) {
        for (UIView *subview in self.scrollContentView.subviews) {
            [subview removeFromSuperview];
        }
    }
    self.headerView.hidden = NO;
    [self.headerView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_top).offset(UPHeight(15));
        make.height.equalTo(@200);
    }];
    
    
    if (liveArr.count > 0) {
        self.pDataArr = liveArr;
        NSInteger column = 2;
    //    NSInteger row = ceil(self.pDataArr.count / 2.0) > 2?2:ceil(self.pDataArr.count / 2.0);
        
        NSInteger pageCount = self.pDataArr.count / 2;
        if (self.pDataArr.count % (column)) {
            pageCount = pageCount + 1;
        }
        [self.pDataArr enumerateObjectsUsingBlock:^(UPLiveRoomModel *model, NSUInteger idx, BOOL *stop) {
            UPLiveViewItemView *itemView = [self.scrollContentView viewWithTag:1000 + idx];
           
            if (!itemView) {
                itemView = [[UPLiveViewItemView alloc] init];
            
                itemView.clickJoinButtonBlock = ^(UPLiveRoomModel *model) {
                    self.clickJoinButtonBlock(model);
                };
                itemView.jumpUrlBlock = ^(UPLiveRoomModel *model) {
                    self.jumpUrlBlock(model);
                };
                
                itemView.tag = 1000 + idx;
                [self.scrollContentView addSubview:itemView];
            }
            [itemView configUIWithIconModel:model];
        }];
        [self.scrollView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@222);
        }];
    } else {
//            [self.headerView mas_updateConstraints:^(MASConstraintMaker *make) {
//                make.left.equalTo(self.contentView.mas_left).offset(UPWidth(15));
//                make.top.equalTo(self.contentView.mas_top).offset(UPHeight(15));
//                make.right.equalTo(self.contentView.mas_right).offset(UPWidth(-15));
//                make.height.equalTo(@200);
//                make.bottom.equalTo(self.contentView.mas_bottom).offset(UPHeight(-10));
//            }];

            [self.scrollView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.equalTo(@0);
            }];
    }

    
    
    

    
}




/// MARK: - UIScrollViewDelegate
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    CGFloat offsetX = scrollView.contentOffset.x;
    CGFloat page = offsetX / scrollView.frame.size.width;
    self.pageControl.currentPage = ceil(page);
}

/// MARK: - Lazy Loading
- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.backgroundColor = UIColor.clearColor;
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.showsHorizontalScrollIndicator = NO;
        _scrollView.pagingEnabled = YES;
        _scrollView.delegate = self;
    }
    return _scrollView;
}

- (UIView *)scrollContentView {
    if (!_scrollContentView) {
        _scrollContentView = [[UIView alloc] init];
        _scrollContentView.backgroundColor = UIColor.clearColor;
    }
    return _scrollContentView;
}

- (UPPageControl *)pageControl{
    if(_pageControl==nil){
        _pageControl = [[UPPageControl alloc] init];
        _pageControl.controlSize = 5;
        _pageControl.controlSpacing = 6;
        _pageControl.currentMultiple = 2.4;
        _pageControl.currentColor = UIColor.up_brandColor;
        _pageControl.otherColor = UIColor.up_textSecondary1Color;
        
    }
    return _pageControl;
}



@end
