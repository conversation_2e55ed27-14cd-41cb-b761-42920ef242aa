//
//  UPHomeMainLiveHeaderView.m
//  UPStockMain
//
//  Created by lizhixiang on 2022/9/16.
//  Copyright © 2022 UpChina. All rights reserved.
//

#import "UPHomeMainLiveHeaderView.h"

@interface UPHomeMainLiveHeaderView ()

@property (nonatomic, strong) UIImageView *liveLogoImageView;
//@property (nonatomic, strong) UILabel *liveTextLable;
//@property (nonatomic, strong) UIImageView *liveSubLogoImageView;
@property (nonatomic, strong) UIImageView *coverUrlImageView;
@property (nonatomic, strong) UILabel *liveMainTextLable;
@property (nonatomic, strong) UILabel *liveTeacherLable;
@property (nonatomic, strong) UILabel *LiveTimeLable;
@property (nonatomic, strong) UIButton *joinButton;
@property (nonatomic, strong) UILabel *lableLab;
@property (nonatomic, strong) UIView *lineView;

@end

@implementation UPHomeMainLiveHeaderView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = UIColor.up_contentBgColor;
        self.userInteractionEnabled = YES;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(jumpUrl)];
        [self addGestureRecognizer:tap];
        [self layoutUI];
    }
    return self;
}


- (void)layoutSubviews {
    [super layoutSubviews];

    self.layer.shadowColor =  [UIColor up_colorFromHexString:@"#4275D8"].CGColor;
    self.layer.shadowOffset = CGSizeMake(0, 0);
    self.layer.shadowOpacity = 0.1;
    self.layer.shadowRadius = 25;
    self.layer.cornerRadius = 20;

}

- (void)layoutUI {
    
    [self addSubview:self.liveLogoImageView];
//    [self addSubview:self.liveTextLable];
//    [self addSubview:self.liveSubLogoImageView];
    [self addSubview:self.coverUrlImageView];
    [self addSubview:self.liveMainTextLable];
    [self addSubview:self.liveTeacherLable];
    [self addSubview:self.LiveTimeLable];
    [self addSubview:self.lineView];
    [self addSubview:self.joinButton];
    [self addSubview:self.lableLab];

    [self.liveLogoImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left).offset(UPWidth(16));
        make.top.equalTo(self.mas_top).offset(UPHeight(20));
        make.size.mas_equalTo(CGSizeMake(UPWidth(118), UPHeight(22)));
    }];
    
//    [self.liveTextLable mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerY.equalTo(self.liveLogoImageView);
//        make.left.equalTo(self.liveLogoImageView.mas_right).offset(8);
//        make.height.equalTo(@21);
//    }];
//
//    [self.liveSubLogoImageView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.centerY.equalTo(self.liveTextLable);
//        make.left.equalTo(self.liveTextLable.mas_right).offset(4);
//    }];
    
    [self.coverUrlImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.liveLogoImageView);
        make.top.equalTo(self.liveLogoImageView.mas_bottom).offset(UPWidth(18));
        make.size.mas_equalTo(CGSizeMake(UPWidth(125), UPHeight(80)));
    }];
    
    [self.liveMainTextLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.coverUrlImageView.mas_top).offset(1);
        make.left.equalTo(self.coverUrlImageView.mas_right).offset(UPWidth(12));
        make.right.equalTo(self.mas_right).offset(UPWidth(-16));
    }];
    
    [self.lableLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.liveMainTextLable);
        make.bottom.equalTo(self.coverUrlImageView.mas_bottom);
//        make.size.mas_equalTo(CGSizeMake(50, 18));
    }];
    
    [self.liveTeacherLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.lableLab);
        make.right.mas_equalTo(self.liveMainTextLable);
    }];
    
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.mas_centerX);
        make.top.equalTo(self.coverUrlImageView.mas_bottom).offset(UPHeight(20));
        make.size.mas_equalTo(CGSizeMake(315, 0.5));
    }];
    
    [self.LiveTimeLable mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.lineView.mas_left).offset(UPWidth(1));
        make.top.equalTo(self.lineView.mas_bottom).offset(UPHeight(13));
        make.height.equalTo(@17);
        make.bottom.equalTo(self.mas_bottom).offset(UPHeight(-20));
    }];
    
    [self.joinButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.LiveTimeLable);
        make.right.equalTo(self.mas_right).offset(UPWidth(-20));
        make.size.mas_equalTo(CGSizeMake(80, 23));
    }];
    
}
/**
 |live_status |Integer   |直播状态 0今日预告 1直播中 2 回放 3明日预告 | |flag |String   |权限标志  |
 */

- (void)setModel:(UPLiveRoomModel *)model {
    _model = model;
    [self.coverUrlImageView up_setImageWithURLString:model.coverImage placeholderImage:UPTImg(@"Home/首页-占位图")];
    
    self.liveMainTextLable.text = model.title;
//    self.lableLab.text = model.liveRoomName;
    self.lableLab.attributedText = [self.lableLab up_labelText:model.thirdCategoryName withLabelTextColor:UIColor.up_bgHotStockTextColor detailText:@"" detailTextColor:UIColor.up_textPrimaryColor];
    self.liveTeacherLable.text = [NSString stringWithFormat:@"讲师 %@", model.realName];
//    self.lableLab.text = model.liveStatus
    if (model.liveState == 0 || model.liveState == 3) {
        self.liveLogoImageView.image = UPTImg(@"Home/首页-直播-预告");
        if (model.subscribeState) {
            [self.joinButton up_setBackgroundColor:UIColor.up_textSecondary2Color forState:0];
            [self.joinButton setTitleColor:UIColor.up_textSecondary1Color forState:0];
            [self.joinButton setTitle:@"已预约" forState:0];
        } else {
            [self.joinButton up_setBackgroundColor:[UIColor up_colorFromHexString:@"#FB6401"] forState:0];
            [self.joinButton setTitleColor:UIColor.up_contentBgColor forState:0];
            [self.joinButton setTitle:@"预约" forState:0];
        }
        if (model.liveState == 0) {
            self.LiveTimeLable.text = [NSString stringWithFormat:@"今天 %@-%@",[model.liveStartTime substringToIndex:model.liveStartTime.length -3],[model.liveEndTime substringToIndex:model.liveEndTime.length - 3]];
        } else {
            self.LiveTimeLable.text = [NSString stringWithFormat:@"明天 %@-%@",[model.liveStartTime substringToIndex:model.liveStartTime.length -3],[model.liveEndTime substringToIndex:model.liveEndTime.length - 3]];
        }
        
    } else if (model.liveState == 1) {
        self.liveLogoImageView.image = UPTImg(@"Home/首页-直播");
        [self.joinButton up_setBackgroundColor:UIColor.up_brandColor forState:0];
        [self.joinButton setTitleColor:UIColor.up_contentBgColor forState:0];
        [self.joinButton setTitle:@"马上看" forState:0];
        self.LiveTimeLable.text = [NSString stringWithFormat:@"今天 %@-%@",[model.liveStartTime substringToIndex:model.liveStartTime.length -3],[model.liveEndTime substringToIndex:model.liveEndTime.length - 3]];

    } else {
        self.liveLogoImageView.image = UPTImg(@"Home/首页-直播-休息");
        [self.joinButton setTitleColor:UIColor.up_textPrimaryColor forState:0];
        [self.joinButton up_setBackgroundColor:UIColor.up_contentBgColor forState:0];
        [self.joinButton setTitle:@"看回放" forState:0];
        self.joinButton.layer.borderColor = [UIColor up_colorFromHexString:@"#C3CBD6"].CGColor;
        self.joinButton.layer.borderWidth = 1.f;
        self.LiveTimeLable.hidden = YES;
    }
    
//    self.liveMainTextLable.text = model.liveTitle;
//    self.liveTeacherLable.text = [NSString stringWithFormat:@"讲师 %@",model.lecturerName];
//    self.lableLab.text = model.authProve;
//    self.LiveTimeLable.text = [NSString stringWithFormat:@"%@-%@",model.startTime,model.endTime];
    
}

- (void)addClick:(UIButton *)sender {
    [DNStatisticMananger clickWithId:DNSCENEID_SY_GDZB eventmemo:self.model.title contentid:[NSString stringWithFormat:@"%d",self.model.planId]];
    self.clickJoinButtonBlock(self.model);
}

- (void)jumpUrl {
    [DNStatisticMananger clickWithId:DNSCENEID_SY_GDZB eventmemo:self.model.title contentid:[NSString stringWithFormat:@"%d",self.model.planId]];

    self.jumpUrlBlock(self.model);
}



// MARK: - Getter && Setter

- (UIImageView *)liveLogoImageView {
    if (!_liveLogoImageView) {
        _liveLogoImageView = [[UIImageView alloc] initWithImage:UPTImg(@"Home/首页-直播")];
    }
    
    return _liveLogoImageView;
}

//- (UIImageView *)liveSubLogoImageView {
//    if (!_liveSubLogoImageView) {
//        _liveSubLogoImageView = [[UIImageView alloc] initWithImage:UPTImg(@"Home/首页-直播-子")];
//    }
//
//    return _liveSubLogoImageView;
//}

- (UIImageView *)coverUrlImageView {
    if (!_coverUrlImageView) {
        _coverUrlImageView = [[UIImageView alloc] initWithImage:UPTImg(@"Home/首页-占位图")];
        _coverUrlImageView.layer.masksToBounds = YES;
        _coverUrlImageView.layer.cornerRadius = 6.f;
        [_coverUrlImageView setContentMode:UIViewContentModeScaleAspectFill];
        _coverUrlImageView.clipsToBounds = YES;
    }
    return _coverUrlImageView;
}

//- (UILabel *)liveTextLable {
//    if (!_liveTextLable) {
//        _liveTextLable = [[UILabel alloc] init];
//        _liveTextLable.textColor = UIColor.up_brandColor;
//        _liveTextLable.font = [UIFont up_mediumfontOfSize:12];
//        _liveTextLable.textAlignment = NSTextAlignmentLeft;
//        _liveTextLable.text = @"热播中";
//    }
//    return _liveTextLable;
//}

- (UILabel *)liveMainTextLable {
    if (!_liveMainTextLable) {
        _liveMainTextLable = [[UILabel alloc] init];
        _liveMainTextLable.textColor = UIColor.up_textPrimaryColor;
        _liveMainTextLable.font = [UIFont up_mediumfontOfSize:16];
        _liveMainTextLable.numberOfLines = 2;
        _liveMainTextLable.textAlignment = NSTextAlignmentLeft;
        _liveMainTextLable.text = @"";
    }
    return _liveMainTextLable;
}

- (UILabel *)liveTeacherLable {
    if (!_liveTeacherLable) {
        _liveTeacherLable = [[UILabel alloc] init];
        _liveTeacherLable.textColor = UIColor.up_textSecondary1Color;
        _liveTeacherLable.font = [UIFont up_fontOfSize:12];
        _liveTeacherLable.textAlignment = NSTextAlignmentRight;
        _liveTeacherLable.text = @"讲师 小白";
    }
    return _liveTeacherLable;
}

- (UILabel *)lableLab {
    if (!_lableLab) {
        _lableLab = [[UILabel alloc] init];
        _lableLab.backgroundColor = UIColor.whiteColor;
        _lableLab.font = [UIFont up_fontOfSize:12];
        _lableLab.layer.masksToBounds = YES;
        _lableLab.layer.cornerRadius = 1;
//        _lableLab.layer.borderWidth = .5f;    //边框宽度
//        _lableLab.layer.borderColor = UIColor.up_bgHotStockTextColor.CGColor;
        _lableLab.text = @"";
        _lableLab.textColor = UIColor.up_bgHotStockTextColor;
        _lableLab.textAlignment = NSTextAlignmentCenter;
        _lableLab.font = [UIFont up_fontOfSize:10];
        
    }
    return _lableLab;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [[UIView alloc] init];
        _lineView.backgroundColor = [UIColor up_colorFromHexString:@"#E7E9FF"];
    }
    return _lineView;
}

- (UILabel *)LiveTimeLable {
    if (!_LiveTimeLable) {
        _LiveTimeLable = [[UILabel alloc] init];
        _LiveTimeLable.textColor = UIColor.up_textSecondary2Color;
        _LiveTimeLable.font = [UIFont up_fontOfSize:12];
        _LiveTimeLable.textAlignment = NSTextAlignmentLeft;
        _LiveTimeLable.text = @"今天 17:00-18:00";
    }
    return _LiveTimeLable;
}

- (UIButton *)joinButton {
    if (!_joinButton) {
        _joinButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_joinButton setTitle:@"马上看" forState:0];
        _joinButton.titleLabel.font = [UIFont up_mediumfontOfSize:13];
        [_joinButton up_setBackgroundColor:UIColor.up_brandColor forState:0];
        [_joinButton setTitleColor:UIColor.up_contentBgColor forState:0];

        [_joinButton addTarget:self action:@selector(addClick:) forControlEvents:UIControlEventTouchUpInside];
        _joinButton.layer.cornerRadius = 4;
        _joinButton.layer.masksToBounds = YES;
    }
    return _joinButton;
}

@end
