//
//  UPStockEventListView.m
//  UPStockMain
//
//  Created by <PERSON><PERSON><PERSON> on 2020/6/12.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockEventListView.h"
#import "UPStockEventListCell.h"
#import "UPStockEventListADCell.h"
#import "UPStockEventListHeaderView.h"
#import "UPStockPayNewsCell.h"
@interface UPStockEventListView() <UITableViewDelegate, UITableViewDataSource,  UPADDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray <UPNewsListInfo *> *totalDataArray; // 原数据（未加工）

@property (nonatomic, strong) UPStockEventListHeaderView *bannerHeaderView; // 三个banner

@property (strong, nonatomic) UPErrorView *emptyView;

@property (nonatomic, copy) void (^scrollCallback) (UIScrollView *scrollView);

@end

@implementation UPStockEventListView
- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self layoutUI];
        
        [self.tableView up_setLoadMoreTarget:self action:@selector(onLoadMore)];
    }
    return self;
}

// MARK: - Private
- (void)startNewsData:(void (^)(void))finished {
    // 请求banner
    [self sendGetAD];
    // 请求列表
    [self reqNewsEvents:nil handle:finished];
}
- (void)onLoadMore {
    UPNewsListInfo *model = self.totalDataArray.lastObject;
    [self reqNewsEvents:model.baseInfo.newsID handle:nil];
    [self.tableView up_beginLoadMore];
}

- (void)layoutUI {
    [self addSubview:self.tableView];
    [self addSubview:self.emptyView];
    
    [self.emptyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

// banner图请求
- (void)sendGetAD {
    [UPADManager getAD_wup:kUPADManagerPositionMainNews uid:UPUserManager.uid delegate:self];
}

// 请求要闻
- (void)reqNewsEvents:(NSString *)startID handle:(void (^)(void))finished {
    UPNewsIDListReq *req = [[UPNewsIDListReq alloc] init];
    req.listType = UPNewsListTypeEvents;
    req.startID = startID;
    if (startID.length > 0) {
        req.direction = UPNewsRequestDirectionHistory;
        req.curNewsSum = self.totalDataArray.count;
    } else {
        req.direction = UPNewsRequestDirectionNew;
        req.curNewsSum = 0;
    }
    req.requestNum = 20;
    
    WeakSelf(weakSelf)
    
    [UPNewsManager requestHAEventsList:req completionHandler:^(UPNewsListRsp *rsp, NSError *error) {
        if (!error) {
            NSMutableArray *tempArray = [NSMutableArray array];
            if (startID.length > 0) {
                [tempArray addObjectsFromArray:weakSelf.totalDataArray];
            }
            
            self.tableView.up_enableLoadMore = !(rsp.newsInfoArray.count < 20);

            [tempArray addObjectsFromArray:rsp.newsInfoArray];
            
            weakSelf.totalDataArray = tempArray;
            
            // 下拉刷新时回到顶部
            if (0 == startID.length && self.totalDataArray.count && self.tableView.numberOfSections &&[self.tableView numberOfRowsInSection:0]) {
                [self.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:0] atScrollPosition:UITableViewScrollPositionNone animated:NO];
            }
            
            [weakSelf.tableView reloadData];
        }
                
        if (!weakSelf.totalDataArray.count) {
            [self showEmptyViewIsError:error ? YES : NO];
        } else {
            [self hideEmptyView];
        }
        
        [self.tableView up_endLoadMore];
        
        if (finished) {
            finished();
        }
    }];
}

- (void)showEmptyViewIsError:(BOOL)isError {
    [self.emptyView showIsError:isError];
    self.tableView.hidden = YES;
    [self bringSubviewToFront:self.emptyView];
}

- (void)hideEmptyView {
    self.emptyView.hidden = YES;
    self.tableView.hidden = NO;
    [self bringSubviewToFront:self.emptyView];
}

// MARK: - Getter && Setter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.estimatedRowHeight = 44;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
        _tableView.separatorColor = UIColor.up_dividerColor;
        _tableView.tableHeaderView = self.bannerHeaderView;
        _tableView.backgroundColor = [UIColor up_contentBgColor];
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectZero];
        
        [_tableView registerClass:[UPStockEventListCell class] forCellReuseIdentifier:@"UPStockEventListCell"];
        [_tableView registerClass:[UPStockPayNewsCell class] forCellReuseIdentifier:@"UPStockPayNewsCell"];
        [_tableView registerClass:[UPStockEventListADCell class] forCellReuseIdentifier:@"UPStockEventListADCell"];
    }
    return _tableView;
}

- (UPStockEventListHeaderView *)bannerHeaderView {
    if (!_bannerHeaderView) {
        _bannerHeaderView = [[UPStockEventListHeaderView alloc] initWithFrame:CGRectMake(0, 0, self.up_width, UPWidth(0))];
    }
    return _bannerHeaderView;
}
// MARK: - UITableViewDelegate
// cell 行高
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    UPNewsListInfo *eventModel = self.totalDataArray[indexPath.row];
    [UPNewsManager addHasRead:eventModel.baseInfo.newsID];
    UPRouterNavigate(eventModel.baseInfo.linkURL);
    [self.tableView reloadData];
}


// MARK: - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.totalDataArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UPNewsListInfo *eventModel = self.totalDataArray[indexPath.row];
    
    BOOL isRead = [UPNewsManager isRead:eventModel.baseInfo.newsID];
    eventModel.baseInfo.isRead = isRead;
    
    if (UPNewsRecomTypeAD == eventModel.baseInfo.recomType) { // 广告
        UPStockEventListADCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UPStockEventListADCell"];
        
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        
        cell.eventModel = self.totalDataArray[indexPath.row];
        
        return cell;
    } else {
        UPNewsRecomType newsType = eventModel.baseInfo.recomType;
        if (newsType == UPNewsRecomTypeEssentialNewsInTrade || newsType == UPNewsRecomTypeNineSpecial || newsType == UPNewsRecomTypeResearchForChance) {
            // 付费资讯
            UPStockPayNewsCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UPStockPayNewsCell"];
            cell.model = eventModel;
            return cell;
        } else {
            // 要闻
            UPStockEventListCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UPStockEventListCell"];
            
            cell.selectionStyle = UITableViewCellSelectionStyleNone;
            
            cell.eventModel = eventModel;
            
            return cell;
        }
    }
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    UIEdgeInsets edge = UIEdgeInsetsMake(0, 7.5, 0, 7.5);
    if ([cell respondsToSelector:@selector(setSeparatorInset:)]) {
        [cell setSeparatorInset:edge];
    }
    
    if ([cell respondsToSelector:@selector(setLayoutMargins:)]) {
        [cell setLayoutMargins:edge];
    }
}

// MARK: - UPADDelegate
- (void)upADResponse:(UPADError)error info:(nullable UPADInfo *)info {
    CGFloat height = 0;
    if (UPADErrorNone == error) {
        self.bannerHeaderView.materials = info.materials;
        height = UPWidth(135);
    }
    self.bannerHeaderView.up_height = height;
    [self.tableView reloadData];
}

- (UPErrorView *)emptyView {
    if (!_emptyView) {
        _emptyView = [UPErrorView new];
        _emptyView.hidden = YES;
    }
    return _emptyView;
}

// MARK: - UPPagerViewListViewDelegate
- (UIView *)listView {
    return self;
}

- (UIScrollView *)listScrollView {
    return self.tableView;
}

- (void)listViewDidScrollCallback:(void (^)(UIScrollView *scrollView))callback {
    self.scrollCallback = callback;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    !self.scrollCallback ?: self.scrollCallback(scrollView);
}

/**
 当下拉刷新时需要调用此方法刷新数据并回调以结束刷新
 */
- (void)refreshDataFinished:(void (^)(void))finished {
    [self startNewsData:finished];
}
@end
