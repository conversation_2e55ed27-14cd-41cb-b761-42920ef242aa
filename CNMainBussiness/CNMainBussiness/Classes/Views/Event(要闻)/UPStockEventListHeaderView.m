//
//  UPStockEventListHeaderView.m
//  UPStockMain
//
//  Created by Chen<PERSON><PERSON> Hu on 2020/6/18.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockEventListHeaderView.h"
#import <SDWebImage/UIImageView+WebCache.h>

@interface UPStockEventListHeaderView ()

@property (nonatomic, strong) UIScrollView *backScrollView;   // 背景滚动

@property (nonatomic, strong) UIView *lineView;   // 分隔线

@end

@implementation UPStockEventListHeaderView
- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = UIColor.up_contentBgColor;
    }
    return self;
}

- (void)setupViews {
    [self addSubview:self.backScrollView];
    [self addSubview:self.lineView];
}

- (void)setConstraints {
    [self.backScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(self);
        make.bottom.equalTo(self.lineView.mas_top);
    }];
    
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@7.5);
        make.right.equalTo(@-7.5);
        make.bottom.equalTo(self);
        make.height.equalTo(@0.5);
    }];
}

// 更新广告
- (void)updateADBtn {
    CGFloat pad = UPWidth(15);
    CGFloat width = UPWidth(220);
    CGFloat height = UPWidth(105);
    NSInteger num = self.materials.count;  // 广告数量
            
    for (UIView *v in self.backScrollView.subviews) {
        [v removeFromSuperview];
    }
    
    for (int i = 0; i < num; i++) {
        UPADMaterialInfo *info = self.materials[i];
        UIImageView *img = [[UIImageView alloc] init];
        [img sd_setImageWithURL:[NSURL URLWithString:info.imageUrl] placeholderImage:UPTImg(@"Home/首页-占位图")];
        img.userInteractionEnabled = YES;
        UITapGestureRecognizer *ges = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(btnClick:)];
        [img addGestureRecognizer:ges];
        img.contentMode = UIViewContentModeScaleAspectFill;
        img.tag = i;
        img.layer.cornerRadius = UPWidth(5);
        img.layer.masksToBounds = YES;
        [self.backScrollView addSubview:img];
        
        [img mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.backScrollView).offset(pad + (pad + width)*i);
            make.centerY.equalTo(self.backScrollView);
            make.width.equalTo(@(width));
            make.height.equalTo(@(height));
            if (num - 1 == i) {
                make.right.equalTo(self.backScrollView).offset(-pad);
            }
        }];
    }
}

- (void)btnClick:(UITapGestureRecognizer *)ges {
    NSInteger tag = ges.view.tag;
    
    if (tag < 5) {
        NSInteger index = tag + 1;
    }
    
    if (tag < self.materials.count) {
        UPADMaterialInfo *info = self.materials[tag];
        UPRouterNavigate(info.url);
    }
}
// MARK: - Getter & Setter
- (UIScrollView *)backScrollView {
    if (!_backScrollView) {
        _backScrollView = [[UIScrollView alloc] init];
        _backScrollView.showsHorizontalScrollIndicator = NO;
        _backScrollView.showsVerticalScrollIndicator = NO;
    }
    return _backScrollView;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [UIView new];
        _lineView.backgroundColor = UIColor.up_dividerColor;
    }
    return _lineView;
}

- (void)setMaterials:(NSArray<UPADMaterialInfo *> *)materials {
    _materials = materials;
    [self updateADBtn];
}
@end
