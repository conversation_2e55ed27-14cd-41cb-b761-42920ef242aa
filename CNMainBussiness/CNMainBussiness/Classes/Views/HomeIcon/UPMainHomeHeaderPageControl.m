//
//  UPMainHomeHeaderPageControl.m
//  UPStockMain
//
//  Created by 方恒 on 2020/9/1.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMainHomeHeaderPageControl.h"

@interface UPMainHomeHeaderPageControl ()

@property (nonatomic, strong) NSMutableArray *dotsArray;
@property (nonatomic, assign) NSInteger lastPage;

@end

@implementation UPMainHomeHeaderPageControl

+ (instancetype)pageControl {
    UPMainHomeHeaderPageControl *pageControl = [[UPMainHomeHeaderPageControl alloc] init];
    pageControl.dotsArray = [[NSMutableArray alloc] init];
    pageControl.selectedColor = UIColor.up_textSecondary1Color;
    pageControl.normalColor = UIColor.up_textSecondary2Color;
    return pageControl;
}

- (void)loadPages {
    [self.subviews enumerateObjectsUsingBlock:^(__kindof UIView *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
        [obj removeFromSuperview];
    }];
    
    _dotsArray = [[NSMutableArray alloc] init];
    for (int i = 0; i < _numberOfPages; ++i) {
        UIView *view = [[UIView alloc] init];
        view.layer.cornerRadius = 2.5;
        if (i == _currentPage) {
            view.backgroundColor = _selectedColor;
        } else {
            view.backgroundColor = _normalColor;
        }
        
        [self addSubview:view];
        
        [view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@5).priorityHigh();
            make.top.bottom.equalTo(self);
            if (i == 0) {
                make.centerX.equalTo(self.mas_left);
                
            } else {
                if (i == _numberOfPages - 1) {
                    make.centerX.equalTo(self.mas_right);
                }
                
                make.centerX.equalTo(self.mas_left).offset(i * (10.f + 2.f));
            }
            
            if (i == _currentPage) {
                make.width.equalTo(@11);
            } else {
                make.width.equalTo(@5);
            }
        }];
        
        [_dotsArray addObject:view];
    }
}

- (void)updateView {
    if (_numberOfPages <= 0) {
        return;
    }
    
    UIView *currentView = self.dotsArray[MIN(_currentPage, self.dotsArray.count - 1)];
    UIView *lastView = self.dotsArray[MIN(_lastPage, self.dotsArray.count - 1)];
    
    lastView.backgroundColor = _normalColor;
    currentView.backgroundColor = _selectedColor;
    
    [lastView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@5);
    }];
    [currentView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@11);
    }];
    
    [UIView animateWithDuration:0.2f animations:^{
        [self layoutIfNeeded];
    }];
    
}

- (void)setCurrentPage:(NSInteger)currentPage {
    if (currentPage == _currentPage) {
        return;
        
    }
    if (currentPage > _numberOfPages - 1) {
        return;
    }
    
    _lastPage = _currentPage;
    _currentPage = currentPage;
    [self updateView];
}

- (void)setNumberOfPages:(NSInteger)numberOfPages {
    if (_numberOfPages == numberOfPages) {
        return;
    }
    
    _numberOfPages = numberOfPages;
    [self loadPages];
}

- (void)setStyle:(UPMainHomeHeaderPageControlStyle)style {
    _style = style;
    
    if (style == UPMainHomeHeaderPageControlStyleLight) {
        self.selectedColor = [UIColor colorWithWhite:1.f alpha:0.4f];
        self.normalColor = [UIColor colorWithWhite:1.f alpha:0.2f];
    } else {
        self.selectedColor = [UIColor colorWithWhite:0.f alpha:0.4f];
        self.normalColor = [UIColor colorWithWhite:0.f alpha:0.2f];
    }
}

@end
