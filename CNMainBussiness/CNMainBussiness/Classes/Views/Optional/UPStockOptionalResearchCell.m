//
//  UPStockOptionalResearchCell.m
//  UPStockMain
//
//  Created by caoxk on 2020/6/9.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockOptionalResearchCell.h"
#import <UPMarketUISDK/UPMarketUICalculateUtil.h>
#import <UPMarketUISDK/UPMarketUICompareTool.h>

@interface UPStockOptionalResearchCell()
@property (strong, nonatomic) UILabel *stockNameLabel;
@property (strong, nonatomic) UILabel *stockCodeLabel;
@property (strong, nonatomic) UILabel *changeLabel;
@property (strong, nonatomic) UILabel *titleLabel;
@property (strong, nonatomic) UILabel *sourceLabel;
@property (strong, nonatomic) UILabel *identifierLabel;
@property (strong, nonatomic) UILabel *dateLabel;
@property (strong, nonatomic) UIView *lineView;
@property (strong, nonatomic) UPNewsStockInfo *stockInfo;

@end

@implementation UPStockOptionalResearchCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.up_contentBgColor;
        [self setupViews];
        [self layoutUI];
    }
    return self;
}

- (void)setupViews {
    [self.contentView addSubview:self.stockNameLabel];
    [self.contentView addSubview:self.stockCodeLabel];
    [self.contentView addSubview:self.changeLabel];
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.sourceLabel];
    [self.contentView addSubview:self.identifierLabel];
    [self.contentView addSubview:self.dateLabel];
    [self.contentView addSubview:self.lineView];
    [self addStockClickView];
}

- (void)layoutUI {
    [self.stockNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.top.equalTo(self.contentView).offset(15);
        make.width.mas_lessThanOrEqualTo(UPWidth(175));
    }];
    
    [self.stockCodeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.stockNameLabel.mas_right).offset(3);
        make.centerY.equalTo(self.stockNameLabel);
    }];
    
    [self.changeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.stockCodeLabel.mas_right).offset(15);
        make.centerY.equalTo(self.stockNameLabel);
    }];
    
    [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.right.equalTo(self.contentView).offset(-15);
        if (!self.stockNameLabel.hidden) {
            make.top.equalTo(self.stockNameLabel.mas_bottom).offset(5);
        } else {
            make.top.equalTo(self.contentView).offset(15);
        }
        make.bottom.equalTo(self.contentView).offset(-38);
    }];
    
    [self.identifierLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.size.mas_equalTo(CGSizeMake(self.identifierLabel.up_width + 8, self.identifierLabel.up_height));
        make.bottom.equalTo(self.contentView).offset(-15);
    }];
    
    [self.sourceLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (self.identifierLabel.hidden) {
            make.left.equalTo(self.contentView).offset(15);
        } else {
            make.left.equalTo(self.identifierLabel.mas_right).offset(15);
        }
        make.bottom.equalTo(self.contentView).offset(-15);
    }];
    
    [self.dateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.titleLabel.mas_right);
        make.bottom.equalTo(self.contentView).offset(-15);
    }];
    
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.right.equalTo(self.contentView).offset(-15);
        make.bottom.equalTo(self.contentView);
        make.height.mas_equalTo(0.5);
    }];
}

- (void)addStockClickView {
    UIView *v = [UIView new];
    v.backgroundColor = [UIColor clearColor];
    UITapGestureRecognizer *ges = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(stockClick)];
    [v addGestureRecognizer:ges];
    [self.contentView addSubview:v];
    [v mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.left.equalTo(self.stockNameLabel);
        make.right.equalTo(self.changeLabel);
    }];
}

- (void)stockClick {
    if (self.stockClickBlock && self.stockInfo) {
        self.stockClickBlock(self.stockInfo.setCode, self.stockInfo.code);
    }
}

#pragma mark - Getter && Setter

- (void)setModel:(UPNewsListInfo *)model {
    _model = model;
    self.titleLabel.text = model.baseInfo.title;
    self.sourceLabel.text = model.baseInfo.source;
    
    if (model.baseInfo.isRead) {
        self.titleLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
        self.dateLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
        self.sourceLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
    } else {
        self.titleLabel.textColor = UIColor.up_textPrimaryColor;
        self.dateLabel.textColor = [UIColor up_textSecondary1Color];
        self.sourceLabel.textColor = [UIColor up_textSecondary1Color];
    }
    
    [self setupStockInfo:model.stockArray.firstObject];
    
    self.dateLabel.text = [NSString up_newsTimeFormatterWithTimeStamp:model.baseInfo.timestamp];

    UPNewsTagInfo *tagInfo = model.tagArray.firstObject;
    BOOL isValideTag = tagInfo.type == UPNewsTagTypeResearch || tagInfo.type == UPNewsTagTypeResearchChange;
    if (tagInfo && isValideTag) {
        if (tagInfo.value == 1) {
            // 买入
            self.identifierLabel.textColor = UIColor.up_brandColor;
            self.identifierLabel.backgroundColor = [UIColor up_colorFromHexString:@"#1AF71828"];
        } else if (tagInfo.value == 2) {
            // 增持
            self.identifierLabel.backgroundColor = [UIColor colorWithRed:34/255.0 green:119/255.0 blue:204/255.0 alpha:0.15/1.0];
            self.identifierLabel.textColor = [UIColor up_colorFromHexString:@"#FF2277CC"];
        } else if (tagInfo.value == 4 || tagInfo.value == 5) {
            // 卖出/减持
            self.identifierLabel.backgroundColor = [UIColor up_colorFromHexString:@"#1AFF9D03"];
            self.identifierLabel.textColor = [UIColor up_colorFromHexString:@"#FFFF9D03"];
        } else {
            self.identifierLabel.backgroundColor = [UIColor up_colorFromHexString:@"#1A2277CC"];
            self.identifierLabel.textColor = [UIColor up_colorFromHexString:@"#FF2277CC"];
        }
        self.identifierLabel.hidden = NO;
        self.identifierLabel.text = tagInfo.title;
        [self.identifierLabel sizeToFit];
    } else {
        self.identifierLabel.hidden = YES;
    }
    [self layoutUI];
}

- (void)setupStockInfo:(UPNewsStockInfo *)model {
    self.stockNameLabel.hidden = YES;
    self.stockCodeLabel.hidden = YES;
    self.changeLabel.hidden = YES;
    self.stockNameLabel.text = @"";
    self.changeLabel.text = @"";
    
    if (!model) return;
    
    self.stockNameLabel.hidden = NO;
    self.stockCodeLabel.hidden = NO;
    self.changeLabel.hidden = NO;
    self.stockInfo = model;
    
    self.stockNameLabel.text = model.name;
    self.stockCodeLabel.text = model.code;
    self.changeLabel.textColor = [UPMarketUICompareTool compareWithData:model.changeValue baseData:0 precise:0];
    self.changeLabel.text = [NSString stringWithFormat:@"%.2f %@",model.nowPrice, [UPMarketUICalculateUtil transPercent:model.changeRatio needSymbol:YES]];
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont up_fontOfSize:UPWidth(15)];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.numberOfLines = 2;
    }
    return _titleLabel;
}

- (UILabel *)sourceLabel {
    if (!_sourceLabel) {
        _sourceLabel = [UILabel new];
        _sourceLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _sourceLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _sourceLabel;
}

- (UILabel *)dateLabel {
    if (!_dateLabel) {
        _dateLabel = [UILabel new];
        _dateLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _dateLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _dateLabel;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [UIView new];
        _lineView.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.1];
    }
    return _lineView;
}

- (UILabel *)identifierLabel {
    if (!_identifierLabel) {
        _identifierLabel = [UILabel new];
        _identifierLabel.font = [UIFont up_fontOfSize:UPWidth(11)];
        _identifierLabel.textColor = UIColor.up_textSecondary1Color;
        _identifierLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _identifierLabel;
}

- (UILabel *)stockNameLabel {
    if (!_stockNameLabel) {
        _stockNameLabel = [[UILabel alloc] init];
        _stockNameLabel.font = [UIFont up_boldFontOfSize:UPWidth(13)];
        _stockNameLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _stockNameLabel;
}

- (UILabel *)stockCodeLabel {
    if (!_stockCodeLabel) {
        _stockCodeLabel = [[UILabel alloc] init];
        _stockCodeLabel.font = [UIFont up_boldFontOfSize:UPWidth(13)];
        _stockCodeLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _stockCodeLabel;
}

- (UILabel *)changeLabel {
    if (!_changeLabel) {
        _changeLabel = [[UILabel alloc] init];
        _changeLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _changeLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _changeLabel;
}
@end
