//
//  UPStockOptionalEventCell.m
//  UPStockMain
//
//  Created by caoxk on 2020/6/9.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockOptionalEventCell.h"
#import <UPMarketUISDK/UPMarketUICalculateUtil.h>
#import <UPMarketUISDK/UPMarketUICompareTool.h>

@interface UPStockOptionalEventCell()
@property (strong, nonatomic) UILabel *dateLabel;
@property (strong, nonatomic) UILabel *yearLabel;
@property (strong, nonatomic) UILabel *stockNameLabel;
@property (strong, nonatomic) UILabel *stockCodeLabel;
@property (strong, nonatomic) UILabel *changeLabel;
@property (strong, nonatomic) UILabel *titleLabel;
@property (strong, nonatomic) UIView *lineView;
@property (strong, nonatomic) UIImageView *circleView;
@property (strong, nonatomic) UPMarketMonitorStockInfo *stockInfo;

@end

@implementation UPStockOptionalEventCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupViews];
        [self layoutUI];
    }
    return self;
}

- (void)setupViews {
    [self.contentView addSubview:self.dateLabel];
    [self.contentView addSubview:self.yearLabel];
    [self.contentView addSubview:self.stockNameLabel];
    [self.contentView addSubview:self.stockCodeLabel];
    [self.contentView addSubview:self.changeLabel];
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.lineView];
    [self.contentView addSubview:self.circleView];
    [self addStockClickView];
}

- (void)layoutUI {
    [self.dateLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.top.equalTo(self.contentView).offset(15);
        make.size.mas_equalTo(CGSizeMake(42, 18));
    }];
    
    [self.circleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.changeLabel);
        make.centerX.equalTo(self.lineView);
        make.width.height.mas_equalTo(16);
    }];
    
    [self.yearLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.dateLabel);
        make.top.equalTo(self.dateLabel.mas_bottom).offset(5);
    }];
    
    [self.lineView mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (self.isTop) {
            make.top.equalTo(self.contentView).offset(30);
        } else {
            make.top.equalTo(self.contentView);
        }
        make.bottom.equalTo(self.contentView);
        make.width.mas_equalTo(1);
        make.left.equalTo(self.dateLabel.mas_right).offset(14);
    }];
    
    [self.stockNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.dateLabel.mas_right).offset(33);
        make.width.mas_lessThanOrEqualTo(UPWidth(175));
        make.top.equalTo(self.contentView).offset(15);
    }];
    
    [self.stockCodeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.stockNameLabel.mas_right).offset(3);
        make.centerY.equalTo(self.stockNameLabel);
    }];
    
    [self.changeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.stockCodeLabel.mas_right).offset(15);
        make.centerY.equalTo(self.stockNameLabel);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.stockNameLabel.mas_bottom).offset(5);
        make.left.equalTo(self.stockNameLabel);
        make.bottom.equalTo(self.contentView);
    }];
}

- (void)addStockClickView {
    UIView *v = [UIView new];
    v.backgroundColor = [UIColor clearColor];
    UITapGestureRecognizer *ges = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(stockClick)];
    [v addGestureRecognizer:ges];
    [self.contentView addSubview:v];
    [v mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.left.equalTo(self.stockNameLabel);
        make.right.equalTo(self.changeLabel);
    }];
}

- (void)stockClick {
    if (self.stockClickBlock && self.stockInfo) {
        self.stockClickBlock(self.stockInfo.setCode, self.stockInfo.code);
    }
}

#pragma mark - Public

- (void)setModel:(UPMarketMonitorData *)model {
    _model = model;
    [self setupStockInfo:model];
    self.titleLabel.text = model.stockMsgInfo.mainTitle;
    self.dateLabel.text = [NSString up_dateStringWithDateFormat:@"MM-dd" timestamp:model.stockMsgInfo.date];
    self.yearLabel.text = [NSString up_dateStringWithDateFormat:@"yyyy" timestamp:model.stockMsgInfo.date];
}

- (void)setIsTop:(BOOL)isTop {
    _isTop = isTop;
    [self layoutUI];
}

- (void)setIsDifferentYear:(BOOL)isDifferentYear {
    _isDifferentYear = isDifferentYear;
    self.yearLabel.hidden = !isDifferentYear;
    if (isDifferentYear) {
        self.circleView.image = UPTImg(@"Home/首页-最新大事");
        self.dateLabel.backgroundColor = [UIColor up_bgColor];
    } else {
        self.circleView.image = UPTImg(@"Home/首页-历史大事");
        self.dateLabel.backgroundColor = [UIColor clearColor];
    }
}

- (void)setupStockInfo:(UPMarketMonitorData *)model {
    self.stockNameLabel.hidden = YES;
    self.stockCodeLabel.hidden = YES;
    self.changeLabel.hidden = YES;
    self.stockNameLabel.text = @"";
    self.changeLabel.text = @"";
    
    if (!model) return;
    
    self.stockNameLabel.hidden = NO;
    self.stockCodeLabel.hidden = NO;
    self.changeLabel.hidden = NO;
    self.stockInfo = model.stockInfo;
    
    self.stockNameLabel.text = model.stockInfo.name;
    self.stockCodeLabel.text = model.stockInfo.code;
    self.changeLabel.textColor = [UPMarketUICompareTool compareWithData:model.stockInfo.changeValue baseData:0 precise:0];
    self.changeLabel.text = [NSString stringWithFormat:@"%.2f %@",model.stockInfo.nowPrice, [UPMarketUICalculateUtil transPercent:model.stockInfo.changeRatio needSymbol:YES]];
}

#pragma mark - Getter && Setter

- (UILabel *)dateLabel {
    if (!_dateLabel) {
        _dateLabel = [[UILabel alloc] init];
        _dateLabel.textColor = [UIColor up_textSecondary1Color];
        _dateLabel.font = [UIFont up_fontOfSize:13];
        _dateLabel.textAlignment = NSTextAlignmentCenter;
        _dateLabel.layer.cornerRadius = 2;
        _dateLabel.layer.masksToBounds = YES;
    }
    return _dateLabel;
}

- (UILabel *)yearLabel {
    if (!_yearLabel) {
        _yearLabel = [[UILabel alloc] init];
        _yearLabel.textColor = [UIColor up_textSecondary1Color];
        _yearLabel.font = [UIFont up_fontOfSize:13];
    }
    return _yearLabel;
}

- (UILabel *)stockNameLabel {
    if (!_stockNameLabel) {
        _stockNameLabel = [[UILabel alloc] init];
        _stockNameLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _stockNameLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _stockNameLabel;
}

- (UILabel *)stockCodeLabel {
    if (!_stockCodeLabel) {
        _stockCodeLabel = [[UILabel alloc] init];
        _stockCodeLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _stockCodeLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _stockCodeLabel;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [UIView new];
        _lineView.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.1];
    }
    return _lineView;
}

- (UILabel *)changeLabel {
    if (!_changeLabel) {
        _changeLabel = [[UILabel alloc] init];
        _changeLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _changeLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _changeLabel;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont up_fontOfSize:UPWidth(15)];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.numberOfLines = 0;
    }
    return _titleLabel;
}

- (UIImageView *)circleView {
    if (!_circleView) {
        _circleView = [UIImageView new];
        _circleView.image = UPTImg(@"/Home/首页-历史大事");
        _circleView.backgroundColor = [UIColor up_contentBgColor];
    }
    return _circleView;
}
@end
