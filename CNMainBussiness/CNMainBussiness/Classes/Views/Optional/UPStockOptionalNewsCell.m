//
//  UPStockOptionalNewsCell.m
//  UPStockMain
//
//  Created by caoxk on 2020/6/8.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockOptionalNewsCell.h"
#import <UPMarketUISDK/UPMarketUICalculateUtil.h>
#import <UPMarketUISDK/UPMarketUICompareTool.h>

@interface UPStockOptionalNewsCell()
@property (strong, nonatomic) UILabel *stockNameLabel;
@property (strong, nonatomic) UILabel *stockCodeLabel;
@property (strong, nonatomic) UILabel *changeLabel;
@property (strong, nonatomic) UILabel *titleLabel;
@property (strong, nonatomic) UILabel *sourceLabel;
@property (strong, nonatomic) UILabel *dateLabel;
@property (strong, nonatomic) UIView *lineView;
@property (strong, nonatomic) UPNewsStockInfo *stockInfo;
@end

@implementation UPStockOptionalNewsCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.backgroundColor = UIColor.up_contentBgColor;
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self layoutUI];
    }
    return self;
}

- (void)layoutUI {
    [self.contentView addSubview:self.stockNameLabel];
    [self.contentView addSubview:self.stockCodeLabel];
    [self.contentView addSubview:self.changeLabel];
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.sourceLabel];
    [self.contentView addSubview:self.dateLabel];
    [self.contentView addSubview:self.lineView];
    
    [self.stockNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.contentView).offset(15);
        make.width.mas_lessThanOrEqualTo(UPWidth(175));
    }];
    
    [self.stockCodeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.stockNameLabel.mas_right).offset(3);
        make.centerY.equalTo(self.stockNameLabel);
    }];
    
    [self.changeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.stockCodeLabel.mas_right).offset(15);
        make.centerY.equalTo(self.stockNameLabel);
    }];
    
    [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.right.equalTo(self.contentView).offset(-15);
        if (!self.stockNameLabel.hidden) {
            make.top.equalTo(self.stockNameLabel.mas_bottom).offset(5);
        } else {
            make.top.equalTo(self.contentView).offset(15);
        }
        make.bottom.equalTo(self.contentView).offset(-38);
    }];
    
    [self.sourceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(5);
        make.bottom.equalTo(self.contentView).offset(-15);
    }];
    
    [self.dateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.titleLabel.mas_right);
        make.centerY.equalTo(self.sourceLabel);
    }];
    
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.right.equalTo(self.contentView).offset(-15);
        make.bottom.equalTo(self.sourceLabel).offset(14);
        make.height.mas_equalTo(0.5);
    }];
    
    [self addStockClickView];
}

- (void)addStockClickView {
    UIView *v = [UIView new];
    v.backgroundColor = [UIColor clearColor];
    UITapGestureRecognizer *ges = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(stockClick)];
    [v addGestureRecognizer:ges];
    [self.contentView addSubview:v];
    [v mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.left.equalTo(self.stockNameLabel);
        make.right.equalTo(self.changeLabel);
    }];
}

- (void)stockClick {
    if (self.stockClickBlock && self.stockInfo) {
        self.stockClickBlock(self.stockInfo.setCode, self.stockInfo.code);
    }
}

#pragma mark - Getter && Setter

- (void)setModel:(UPNewsListInfo *)model {
    _model = model;
    
    if (model.baseInfo.isRead) {
        self.titleLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
        self.dateLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
        self.sourceLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
    } else {
        self.titleLabel.textColor = UIColor.up_textPrimaryColor;
        self.dateLabel.textColor = [UIColor up_textSecondary1Color];
        self.sourceLabel.textColor = [UIColor up_textSecondary1Color];
    }
    
    self.titleLabel.text = model.baseInfo.title;
    
    [self setupStockInfo:model.stockArray.firstObject];
    
    self.sourceLabel.text = model.baseInfo.source;
    
    self.dateLabel.text = [NSString up_newsTimeFormatterWithTimeStamp:model.baseInfo.timestamp];
}

#pragma mark - Private
- (void)setupStockInfo:(UPNewsStockInfo *)model {
    self.stockNameLabel.hidden = YES;
    self.stockCodeLabel.hidden = YES;
    self.changeLabel.hidden = YES;
    self.stockNameLabel.text = @"";
    self.changeLabel.text = @"";
    
    if (!model) return;

    self.stockNameLabel.hidden = NO;
    self.stockCodeLabel.hidden = NO;
    self.changeLabel.hidden = NO;
    self.stockInfo = model;
    
    self.stockNameLabel.text = model.name;
    self.stockCodeLabel.text = model.code;
    self.changeLabel.textColor = [UPMarketUICompareTool compareWithData:model.changeValue baseData:0 precise:0];
    self.changeLabel.text = [NSString stringWithFormat:@"%.2f %@",model.nowPrice, [UPMarketUICalculateUtil transPercent:model.changeRatio needSymbol:YES]];
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont up_fontOfSize:UPWidth(17)];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.numberOfLines = 2;
    }
    return _titleLabel;
}

- (UILabel *)sourceLabel {
    if (!_sourceLabel) {
        _sourceLabel = [UILabel new];
        _sourceLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _sourceLabel.textColor = [UIColor up_textSecondary1Color];
    }
    return _sourceLabel;
}

- (UILabel *)dateLabel {
    if (!_dateLabel) {
        _dateLabel = [UILabel new];
        _dateLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _dateLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _dateLabel;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [UIView new];
        _lineView.backgroundColor = UIColor.up_dividerColor;
    }
    return _lineView;
}

- (UILabel *)stockNameLabel {
    if (!_stockNameLabel) {
        _stockNameLabel = [[UILabel alloc] init];
        _stockNameLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _stockNameLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _stockNameLabel;
}

- (UILabel *)stockCodeLabel {
    if (!_stockCodeLabel) {
        _stockCodeLabel = [[UILabel alloc] init];
        _stockCodeLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _stockCodeLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _stockCodeLabel;
}

- (UILabel *)changeLabel {
    if (!_changeLabel) {
        _changeLabel = [[UILabel alloc] init];
        _changeLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _changeLabel.textColor = UIColor.up_textSecondary1Color;
    }
    return _changeLabel;
}
@end
