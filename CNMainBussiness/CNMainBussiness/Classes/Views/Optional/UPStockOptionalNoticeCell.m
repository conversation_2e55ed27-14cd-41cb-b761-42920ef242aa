//
//  UPStockOptionalNoticeCell.m
//  UPStockMain
//
//  Created by caoxk on 2020/6/8.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPStockOptionalNoticeCell.h"
#import <UPMarketUISDK/UPMarketUICalculateUtil.h>
#import <UPMarketUISDK/UPMarketUICompareTool.h>

@interface UPStockOptionalNoticeCell()
@property (strong, nonatomic) UILabel *stockNameLabel;
@property (strong, nonatomic) UILabel *stockCodeLabel;
@property (strong, nonatomic) UILabel *changeLabel;
@property (strong, nonatomic) UILabel *titleLabel;
@property (strong, nonatomic) UILabel *sourceLabel;
@property (strong, nonatomic) UILabel *dateLabel;
@property (strong, nonatomic) UIView *lineView;
@property (strong, nonatomic) UPNewsStockInfo *stockInfo;

@property (nonatomic, strong) UIImage * liKongImg;

@property (nonatomic, strong) UIImage * liHaoImg;

@end

@implementation UPStockOptionalNoticeCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        self.backgroundColor = UIColor.up_contentBgColor;
        [self setupUI];
        [self layoutUI];
    }
    return self;
}

- (void)setupUI {
    [self.contentView addSubview:self.stockNameLabel];
    [self.contentView addSubview:self.stockCodeLabel];
    [self.contentView addSubview:self.changeLabel];
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.sourceLabel];
    [self.contentView addSubview:self.dateLabel];
    [self.contentView addSubview:self.lineView];
    [self addStockClickView];
}

- (void)addStockClickView {
    UIView *v = [UIView new];
    v.backgroundColor = [UIColor clearColor];
    UITapGestureRecognizer *ges = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(stockClick)];
    [v addGestureRecognizer:ges];
    [self.contentView addSubview:v];
    [v mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.left.equalTo(self.stockNameLabel);
        make.right.equalTo(self.changeLabel);
    }];
}

- (void)stockClick {
    if (self.stockClickBlock && self.stockInfo) {
        self.stockClickBlock(self.stockInfo.setCode, self.stockInfo.code);
    }
}

- (void)layoutUI {
    [self.stockNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(UPWidth(20));
        make.top.equalTo(self.contentView).offset(UPWidth(15));
        make.width.mas_lessThanOrEqualTo(UPWidth(175));
    }];
    
    [self.stockCodeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.stockNameLabel.mas_right).offset(3);
        make.centerY.equalTo(self.stockNameLabel);
    }];
    
    [self.changeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.stockCodeLabel.mas_right).offset(15);
        make.centerY.equalTo(self.stockNameLabel);
    }];
    
    [self.titleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(UPWidth(20));
        make.right.equalTo(self.contentView).offset(UPWidth(-20));
        if (!self.stockNameLabel.hidden) {
            make.top.equalTo(self.stockNameLabel.mas_bottom).offset(8);
        } else {
            make.top.equalTo(self.contentView).offset(15);
        }
        make.bottom.equalTo(self.contentView).offset(-50);
    }];
    
    [self.sourceLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(UPWidth(20));
        make.size.mas_equalTo(CGSizeMake(self.sourceLabel.up_width + 8, 18));
        make.bottom.equalTo(self.contentView).offset(-20);
    }];
    
    [self.dateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView).offset(UPWidth(-20));
        make.bottom.equalTo(self.contentView).offset(-20);
    }];
    
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.right.equalTo(self.contentView).offset(-15);
        make.bottom.equalTo(self.contentView);
        make.height.mas_equalTo(0.5);
    }];
}

#pragma mark - Getter && Setter

- (void)setModel:(UPNewsListInfo *)model {
    _model = model;
    
    if (model.baseInfo.isRead) {
        self.titleLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
        self.dateLabel.textColor = [UIColor up_colorFromHexString:@"#909090"];
    } else {
        self.titleLabel.textColor = UIColor.up_textPrimaryColor;
        self.dateLabel.textColor = [UIColor up_textSecondary1Color];
    }
    
    NSString * typeStr = @"";
    UIColor * typeColor = UIColor.up_riseColor;
    if (self.model.tagArray != 0) {
        typeStr = [self typeStr:self.model.tagArray.firstObject];
        typeColor = [self typeColor:self.model.tagArray.firstObject];
    }
    if (IsValidateString(typeStr) && IsValidateString(model.baseInfo.title)) {
        UIImage * image = nil;
        if ([typeStr isEqualToString:@"利空"]) {
            image = self.liKongImg;
        }else if([typeStr isEqualToString:@"利好"]){
            image = self.liHaoImg;
        }
        if (image) {
            NSTextAttachment *attach = [[NSTextAttachment alloc] init];
            attach.bounds = CGRectMake(0, -3, image.size.width, image.size.height); //这个-2.5是为了调整下标签跟文字的位置
            attach.image = image;
            //添加到富文本对象里
            NSAttributedString * imageStr = [NSAttributedString attributedStringWithAttachment:attach];
            NSMutableAttributedString * attributedStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"  %@",model.baseInfo.title]];
            [attributedStr insertAttributedString:imageStr atIndex:0];//加入文字前面
            self.titleLabel.attributedText = attributedStr;
        }else{
            self.titleLabel.text = model.baseInfo.title;
        }
    }else{
        self.titleLabel.text = model.baseInfo.title;
    }
    
    
    [self setupStockInfo:model.stockArray.firstObject];
    
    NSDate *date = [[NSDate alloc] initWithTimeIntervalSince1970:model.baseInfo.timestamp];
    NSDate *today = [NSDate date];
    BOOL isTheSameDay = today.up_month == date.up_month && today.up_year == date.up_year && today.up_dayOfYear == date.up_dayOfYear;
    
//    if (today.up_year == date.up_year) {
//        self.dateLabel.text = [NSString up_dateStringWithDateFormat:@"MM-dd" timestamp:model.baseInfo.timestamp];
//    } else {
//        self.dateLabel.text = [NSString up_dateStringWithDateFormat:@"yyyy-MM-dd HH:mm" timestamp:model.baseInfo.timestamp];
//    }
    self.dateLabel.text = [NSString up_dateStringWithDateFormat:@"yyyy-MM-dd HH:mm" timestamp:model.baseInfo.timestamp];
    
    [self setupSourceLabelWithTagArray:model.tagArray];
    
    [self layoutUI];
}

#pragma mark - Private

- (void)setupStockInfo:(UPNewsStockInfo *)model {
    self.stockNameLabel.hidden = YES;
    self.stockCodeLabel.hidden = YES;
    self.changeLabel.hidden = YES;
    self.stockNameLabel.text = @"";
    self.changeLabel.text = @"";
    
    if (!model) return;

    self.stockNameLabel.hidden = NO;
    self.stockCodeLabel.hidden = NO;
    self.changeLabel.hidden = NO;
    self.stockInfo = model;
    
    self.stockNameLabel.text = model.name;
    self.stockCodeLabel.text = model.code;

    self.changeLabel.textColor = [UPMarketUICompareTool compareWithData:model.changeValue baseData:0 precise:0];
    self.changeLabel.text = [NSString stringWithFormat:@"%.2f %@",model.nowPrice, [UPMarketUICalculateUtil transPercent:model.changeRatio needSymbol:YES]];
}

- (void)setupSourceLabelWithTagArray:(NSArray *)tagArray {
    self.sourceLabel.hidden = YES;
    self.sourceLabel.text = @"";
    
    for (NSInteger i = 0; i < tagArray.count; i++) {
        UPNewsTagInfo *tagInfo = tagArray[i];
        if (tagInfo.type == UPNewsTagTypeNotice || tagInfo.type == UPNewsTagTypeNoticeCategory) {
//            if (tagInfo.value == 1 || tagInfo.value == 2) {
//                // 利好
//                self.sourceLabel.textColor = UIColor.up_riseColor;
//                self.sourceLabel.backgroundColor = [UIColor up_colorFromHexString:@"#1AF71828"];
//            } else if (tagInfo.value == 4 || tagInfo.value == 5) {
//                self.sourceLabel.backgroundColor = [UIColor up_colorFromHexString:@"#1A21AB6F"];
//                self.sourceLabel.textColor = [UIColor up_fallColor];
//            } else {
//                self.sourceLabel.backgroundColor = [UIColor up_colorFromHexString:@"#1A5C5C5C"];
//                self.sourceLabel.textColor = [UIColor up_textSecondaryColor];
//            }
            self.sourceLabel.hidden = NO;
            self.sourceLabel.text = [NSString stringWithFormat:@"%@",tagInfo.title];
        }
    }
    self.sourceLabel.textColor = UIColor.up_brandColor;
    self.sourceLabel.layer.borderWidth = 0.5;
    self.sourceLabel.layer.borderColor = UIColor.up_brandColor.CGColor;
    self.sourceLabel.layer.cornerRadius = 2;
    self.sourceLabel.backgroundColor = UIColor.up_contentBgColor;
    [self.sourceLabel sizeToFit];
}

- (NSString*)typeStr:(UPNewsTagInfo *)tagInfo{
    NSString * typeStr = @"";
    if (tagInfo.value == 1 || tagInfo.value == 2) {
        // 利好
        typeStr = @"利好";
    } else if (tagInfo.value == 4 || tagInfo.value == 5) {
        typeStr = @"利空";
    }else{
        typeStr  = @"";
    }
    return typeStr;
}

- (UIColor*)typeColor:(UPNewsTagInfo *)tagInfo{
    if (tagInfo.value == 1 || tagInfo.value == 2) {
        return UIColor.up_riseColor;
    } else if (tagInfo.value == 4 || tagInfo.value == 5) {
        return UIColor.up_fallColor;
    }
    return UIColor.grayColor;
}

- (UILabel *)stockNameLabel {
    if (!_stockNameLabel) {
        _stockNameLabel = [[UILabel alloc] init];
        _stockNameLabel.font = [UIFont up_fontOfSize:UPWidth(15)];
        _stockNameLabel.textColor = [UIColor up_colorFromHexString:@"#657180"];
    }
    return _stockNameLabel;
}

- (UILabel *)stockCodeLabel {
    if (!_stockCodeLabel) {
        _stockCodeLabel = [[UILabel alloc] init];
        _stockCodeLabel.font = [UIFont up_fontOfSize:UPWidth(15)];
        _stockCodeLabel.textColor = [UIColor up_colorFromHexString:@"#657180"];
    }
    return _stockCodeLabel;
}

- (UILabel *)changeLabel {
    if (!_changeLabel) {
        _changeLabel = [[UILabel alloc] init];
        _changeLabel.font = [UIFont up_fontOfSize:UPWidth(15) weight:UIFontWeightSemibold];
        _changeLabel.textColor = UIColor.up_textSecondaryColor;
    }
    return _changeLabel;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont up_fontOfSize:UPWidth(15) weight:UIFontWeightSemibold];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.numberOfLines = 2;
    }
    return _titleLabel;
}

- (UILabel *)sourceLabel {
    if (!_sourceLabel) {
        _sourceLabel = [UILabel new];
        _sourceLabel.font = [UIFont up_fontOfSize:UPWidth(11)];
        _sourceLabel.textColor = UIColor.up_textSecondaryColor;
        _sourceLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _sourceLabel;
}

- (UILabel *)dateLabel {
    if (!_dateLabel) {
        _dateLabel = [UILabel new];
        _dateLabel.font = [UIFont up_fontOfSize:UPWidth(13)];
        _dateLabel.textColor = UIColor.up_textSecondaryColor;
    }
    return _dateLabel;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [UIView new];
        _lineView.backgroundColor = [UIColor up_colorFromString:@"#E4E6F2"];
    }
    return _lineView;
}

- (UIImage *)liKongImg{
    if (!_liKongImg) {
        UILabel * label = [UILabel new];
        label.backgroundColor = UIColor.up_fallColor;
        label.textColor = UIColor.whiteColor;
        label.font = [UIFont up_fontOfSize:UPWidth(12)];
        label.text = @"利空";
        label.clipsToBounds = YES;
        label.layer.cornerRadius = 2;
        label.frame = CGRectMake(0, 0, UPWidth(38), UPWidth(18));
        label.textAlignment = NSTextAlignmentCenter;
        _liKongImg = [self imageWithUIView:label];
    }
    
    return _liKongImg;
}

- (UIImage *)liHaoImg{
    if (!_liHaoImg) {
        UILabel * label = [UILabel new];
        label.backgroundColor = UIColor.up_riseColor;
        label.textColor = UIColor.whiteColor;
        label.font = [UIFont up_fontOfSize:UPWidth(12)];
        label.text = @"利好";
        label.layer.cornerRadius = 2;
        label.clipsToBounds = YES;
        label.frame = CGRectMake(0, 0, UPWidth(38), UPWidth(18));
        label.textAlignment = NSTextAlignmentCenter;
        _liHaoImg = [self imageWithUIView:label];
    }
    return _liHaoImg;
}

- (UIImage*)imageWithUIView:(UIView*) view{
    CGFloat scale = [UIScreen mainScreen].scale;
    UIImage *imageRet = [[UIImage alloc]init];
    //UIGraphicsBeginImageContextWithOptions(区域大小, 是否是非透明的, 屏幕密度);
    UIGraphicsBeginImageContextWithOptions(view.frame.size, NO, scale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    imageRet = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    
    return imageRet;
}

@end

