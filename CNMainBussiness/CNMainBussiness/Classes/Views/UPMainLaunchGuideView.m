//
//  UPMainLaunchGuideView.m
//  UPStockMain
//
//  Created by sammy<PERSON> on 2020/5/9.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UPMainLaunchGuideView.h"

#define LAUNCH_GUIDE_VERSION_KEY (@"LAUNCH_GUIDE_VERSION_KEY")
#define LAUNCH_GUIDE_VERSION (4)

@interface UPMainLaunchGuideView () <UIScrollViewDelegate>

@property(nonatomic, strong) UIScrollView * scrollView;
@property(nonatomic, strong) UIPageControl * pageControl;
@property(nonatomic, strong) NSArray<UIView *> * guideViews;
@property(nonatomic, strong) UIButton * goButton;
@property(nonatomic, strong) UIButton * jumpButton;

@end

@implementation UPMainLaunchGuideView

+(BOOL)needShowGuide {
    NSInteger guideVersion = [NSUserDefaults.standardUserDefaults integerForKey:LAUNCH_GUIDE_VERSION_KEY];

    if(guideVersion < LAUNCH_GUIDE_VERSION) {
        [NSUserDefaults.standardUserDefaults setInteger:LAUNCH_GUIDE_VERSION forKey:LAUNCH_GUIDE_VERSION_KEY];
        [NSUserDefaults.standardUserDefaults synchronize];

        return YES;
    }
    
    return NO;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.scrollView = [[UIScrollView alloc] init];
        self.scrollView.pagingEnabled = YES;
        self.scrollView.showsHorizontalScrollIndicator = NO;
        self.scrollView.bounces = NO;
        self.scrollView.delegate = self;

        for (UIView * view in self.guideViews) {
            [self.scrollView addSubview:view];
        }

        [self addSubview:self.scrollView];
        
        
        self.jumpButton = [[UIButton alloc] init];
        [self.jumpButton setTitle:@"跳过" forState:UIControlStateNormal];
        [self.jumpButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
        [self.jumpButton addTarget:self action:@selector(goAndHaveFun) forControlEvents:UIControlEventTouchUpInside];
        self.jumpButton.layer.cornerRadius = 20;
        self.jumpButton.layer.masksToBounds = YES;
        self.jumpButton.titleLabel.font = [UIFont up_fontOfSize:17];
        [self.jumpButton setBackgroundColor:[UIColor up_colorFromHexString:@"#3D000000"]];
        [self addSubview:self.jumpButton];

        

        self.pageControl = [[UIPageControl alloc] init];
        self.pageControl.pageIndicatorTintColor = UIColor.up_textSecondary2Color;
        self.pageControl.currentPageIndicatorTintColor = UIColor.up_brandColor;
        self.pageControl.numberOfPages = self.guideViews.count;
        [self addSubview:self.pageControl];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];

    self.scrollView.frame = self.bounds;
    self.scrollView.contentSize = CGSizeMake(self.up_width * self.guideViews.count, self.up_height);

    NSInteger index = 0;

    for (UIView * child in self.guideViews) {
        child.frame = CGRectMake(self.up_width * index, 0, self.up_width, self.up_height);
        index++;
    }
    
    self.jumpButton.frame = CGRectMake(self.up_width - 120, 60, 80, 40);

    self.pageControl.frame = CGRectMake(0, self.up_height - 80, self.up_width, 50);
}

// MARK: UIScrollViewDelegate

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat width = scrollView.up_width;
    NSInteger page = (scrollView.contentOffset.x + width / 2) / width;
    self.pageControl.currentPage = page;
}

// MARK: Getter & Setter

- (NSArray<UIView *> *)guideViews {
    if(!_guideViews) {
        // 因为每个页面可能不同, 所以分开加
        _guideViews = @[
            [self guideView1],
            [self guideView2],
            [self guideView3],
            [self guideView4],
        ];
    }

    return _guideViews;
}

// MARK: Private

-(UIView *)guideView1 {
    return  [[UIImageView alloc] initWithImage: UPTImgNoCacheInModule(@"LaunchGuide/1",@"ceniu")];
}

-(UIView *)guideView2 {
    return [[UIImageView alloc] initWithImage:UPTImgNoCacheInModule(@"LaunchGuide/2",@"ceniu")];
}

-(UIView *)guideView3 {
    return [[UIImageView alloc] initWithImage:UPTImgNoCacheInModule(@"LaunchGuide/3",@"ceniu")];
}

-(UIView *)guideView4 {
    // TODO: 还有登录按钮等
    UIView * view = [[UIView alloc] init];

    UIImageView * imageView = [[UIImageView alloc] initWithImage: UPTImgNoCacheInModule(@"LaunchGuide/4",@"ceniu")];
    self.goButton = [[UIButton alloc] init];
    [self.goButton setTitle:@"立即体验" forState:UIControlStateNormal];
    [self.goButton setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    [self.goButton addTarget:self action:@selector(goAndHaveFun) forControlEvents:UIControlEventTouchUpInside];
    self.goButton.layer.cornerRadius = 25;
    self.goButton.layer.masksToBounds = YES;
    self.goButton.titleLabel.font = [UIFont up_fontOfSize:19];
    
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.colors = @[(__bridge id)[UIColor up_colorFromHexString:@"#FF7C83"].CGColor, (__bridge id)[UIColor up_colorFromHexString:@"#EB2832"].CGColor];
    gradientLayer.locations = @[@0.1, @1.0];
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(1.0, 0);
    
    gradientLayer.frame = CGRectMake(0, 0, 230, 50);
    [self.goButton.layer insertSublayer:gradientLayer atIndex:0];
    
    [view addSubview:imageView];
    [view addSubview:self.goButton];

    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(view);
    }];

    [self.goButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(view);
        make.bottom.equalTo(view).offset(-UPWidth(85));
        make.width.equalTo(@150);
        make.height.equalTo(@50);
    }];
    
    return view;
}

-(void)goAndHaveFun {
    __strong id<UPMainLaunchGuideViewDelegate> delegate = self.delegate;
    
    if(delegate && [delegate respondsToSelector:@selector(launchGuideView:openURL:)]) {
        [delegate launchGuideView:self openURL:nil];
    }
}

@end
