//
//  UIColor+UPStockMain.m
//  UPStockMain
//
//  Created by <PERSON><PERSON><PERSON> on 2020/6/11.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import "UIColor+UPStockMain.h"

@implementation UIColor (UPStockMain)

+ (UIColor *)upstockmain_flash_title_bg_color {
    return UPTColor(@"up_stockMain_flash_title_bg_color");
}

+ (UIColor *)upstockmain_flash_stock_bg_color {
    return UPTColor(@"up_stockMain_flash_stock_bg_color");
}

+ (UIColor *)upstockmain_flash_vertical_color {
    return UPTColor(@"up_stockmain_flash_vertical_color");
}

+ (UIColor *)upstockmain_flash_font_shadeBGColor {
    return UPTColor(@"up_stockMain_flash_font_shade_bg_Color");
}

+ (UIColor *)upstockMain_headline_news_type_bg_color {
    return UPTColor(@"up_stockMain_headline_news_type_bg_color");
}

+ (UIColor *)upsotckMain_headline_news_title_color {
    return UPTColor(@"up_sotckMain_headline_news_title_color");
}

+ (UIColor *)upstockMain_optinal_qa_card_bg_color {
    return UPTColorInModule(@"up_stockMain_optinal_qa_card_bg_color",@"ceniu");
}

+ (UIColor *)upstockmain_newstock_title_color {
    return UPTColor(@"up_stockMain_newstock_title_color");
}

+ (UIColor *)upstockmain_option_new_nologin_color {
    return UPTColorInModule(@"up_stockmain_option_new_nologin_color",@"ceniu");
}
@end
