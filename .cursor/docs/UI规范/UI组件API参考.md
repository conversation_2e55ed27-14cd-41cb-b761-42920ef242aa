# UI组件API参考

## UILabel 分类方法

### 基本初始化
```objc
// 基本初始化
- (instancetype)initWithFrame:(CGRect)frame 
                         font:(UIFont *)font 
                    textColor:(UIColor *)textColor 
              backgroundColor:(UIColor *)backgroundColor 
                numberOfLines:(NSInteger)numberOfLines;

// 带文本对齐的初始化
- (instancetype)initWithFrame:(CGRect)frame 
                         font:(UIFont *)font 
                    textColor:(UIColor *)textColor 
              backgroundColor:(UIColor *)backgroundColor 
                numberOfLines:(NSInteger)numberOfLines 
                textAlignment:(NSTextAlignment)textAlignment;
```

## UIButton 分类方法

### 便捷初始化
```objc
// 完整初始化方法
- (instancetype)initWithFrame:(CGRect)frame 
                         font:(UIFont *)font 
                normalTextColor:(UIColor *)normalTextColor 
                backgroundColor:(UIColor *)backgroundColor 
                         title:(NSString *)title 
                         image:(UIImage *)image 
                        target:(id)target 
                        action:(SEL)action;
```

### 图文布局
```objc
// 设置图文位置关系
- (void)layoutButtonWithEdgInsetsStyle:(ButtonEdgeInsetsStyle)style 
                     imageTitleSpacing:(CGFloat)spacing;
```

**ButtonEdgeInsetsStyle 枚举值：**
- `ButtonEdgeInsetsStyleImageLeft` - 图片在左
- `ButtonEdgeInsetsStyleImageRight` - 图片在右  
- `ButtonEdgeInsetsStyleImageTop` - 图片在上
- `ButtonEdgeInsetsStyleImageBottom` - 图片在下

## UITableView 分类方法

### 基本初始化
```objc
// 基本初始化
- (instancetype)initWithFrame:(CGRect)frame 
                        style:(UITableViewStyle)style 
                     delegate:(id<UITableViewDelegate>)delegate 
                   dataSource:(id<UITableViewDataSource>)dataSource 
               viewController:(UIViewController *)viewController;

// 带下拉刷新和上拉加载的初始化
- (instancetype)initWithFrame:(CGRect)frame 
                        style:(UITableViewStyle)style 
                     delegate:(id<UITableViewDelegate>)delegate 
                   dataSource:(id<UITableViewDataSource>)dataSource 
               viewController:(UIViewController *)viewController 
                 headerTarget:(id)headerTarget 
                 headerAction:(SEL)headerAction 
                 footerTarget:(id)footerTarget 
                 footerAction:(SEL)footerAction;
```

### Cell 注册和复用
```objc
// 注册单个Cell类
- (void)registerCellClass:(Class)cellClass;

// 注册多个Cell类
- (void)registerCellClassArray:(NSArray<Class> *)cellClassArray;

// 复用Cell
- (UITableViewCell *)reuseCellClass:(Class)cellClass;
```

### Header/Footer 注册和复用
```objc
// 注册单个Header/Footer类
- (void)registerViewClass:(Class)viewClass;
// 复用Header/Footer
- (__kindof UITableViewHeaderFooterView *)reuseViewClass:(Class)viewClass;
```


## UIStackView 分类方法

### 便捷初始化
```objc
// 初始化StackView
- (instancetype)initWithAxis:(UILayoutConstraintAxis)axis 
                   alignment:(UIStackViewAlignment)alignment 
                distribution:(UIStackViewDistribution)distribution 
                     spacing:(CGFloat)spacing 
            arrangedSubviews:(NSArray<UIView *> *)arrangedSubviews;
```

## 日期处理分类方法

### NSDate 分类方法
```objc
// 日期转字符串
- (NSString *)dateStringWithFormatString:(NSString *)formatString;

// 字符串转日期
+ (NSDate *)dateFromFormatedString:(NSString *)string format:(NSString *)format;

// 日期比较
- (BOOL)isToday;
- (BOOL)isYesterday;
- (BOOL)isThisYear;
- (BOOL)isSameDayWithDate:(NSDate *)date;

// 获取时间戳
+ (NSTimeInterval)getNowTimeStamp:(NSDate *)date;
```

## 使用要点

1. **必须使用分类方法**：所有UI组件都应使用提供的分类初始化方法
2. **避免系统默认方法**：不要使用系统的默认初始化方法
3. **保持一致性**：同类型组件使用相同的初始化模式
4. **参考核心规范**：遵循UI开发核心规范中的强制要求 