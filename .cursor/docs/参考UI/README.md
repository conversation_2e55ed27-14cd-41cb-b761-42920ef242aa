# UI设计规范文档索引

本文档集合整理了项目中常用的UI组件、布局规范和实现示例，方便开发人员查阅和使用。


## 列表页面规范
- [06.简单的列表](06.简单的列表.md) - 普通列表页面的基础布局规范
- [08.可展开折叠的Cell](08.可展开折叠的Cell.md) - 内容可展开收起的列表单元格
- [12.宽度超过一屏、带固定列的列表](12.宽度超过一屏、带固定列的列表.md) - 横向滚动列表实现

## 导航与页面切换规范
- [07.多页面切换](07.多页面切换.md) - 使用标签切换不同内容页面
- [11.底部弹窗](11.底部弹窗.md) - 自下而上弹出的模态窗口实现

## 文本显示规范
- [09.带图片标签的Label](09.带图片标签的Label.md) - 图文混排标签实现
- [10.一行多个Label显示](10.一行多个Label显示.md) - 水平多标签布局实现方案

## 使用说明
1. 在开发时引用文档：`@UI/[文档名]`
2. 参考规范模板([00.规范模板](00.规范模板.md))创建新的UI规范文档
3. 每个规范文档需包含：
   - UI设计图
   - 页面结构分析
   - 组件说明
   - 代码示例(Model/View/Controller)
   - 注意事项
   - 版本记录

## 文档结构
```
UI/
├── README.md                          # 本索引文件
├── 00.规范模板.md                     # 文档编写规范模板
├── 06.简单的列表.md                   # 列表页面示例
├── 07.多页面切换.md                   # 页面切换示例
├── 08.可展开折叠的Cell.md             # 可折叠的Cell
├── 09.带图片标签的Label.md            # 图文标签
├── 10.一行多个Label显示.md            # 多标签布局
├── 11.底部弹窗.md                     # 底部弹窗
├── 12.宽度超过一屏、带固定列的列表.md # 横向滚动列表
└── images/                            # 文档引用的图片资源
```

## 如何贡献
1. 发现问题或有改进建议，请提交Issue
2. 添加新的UI规范文档，请按照"00.规范模板.md"格式编写
3. 更新文档时，请同步更新README.md索引 