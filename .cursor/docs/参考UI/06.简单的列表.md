# 简单的列表

### UI 设计图

<img src="images/简单列表.jpg" alt="简单列表" style="zoom:50%;" />

### 页面结构分析

研报是一个简单列表页面，使用 UITableView 显示，重点关注内部的 Cell。

Cell 可以分为三部分：
* 上面使用 3 个 UILabel，分别展示股票名、现价、涨跌幅
* 中间使用 1 个 YYLabel，因为有一个 Cell 里面显示了超过 2 行显示省略号，所以numberOfLines设置为 2
* 下面使用 4 个 Label，分别展示标签、深度分析、目标价和时间，其中标签背景色宽高要根据标签文字适配，所以使用 ZLTagLabel，前面三个 Label 会可能隐藏，所以用 UIStackView 包裹一层。

### 组件说明

- **UITableView**: 主列表视图，用于显示报告数据
- **UIStackView**: 用于管理多个label的横向布局，特别是当某些label可能隐藏时
- **ZLTagLabel**: 自定义标签label，根据文本内容自适应大小
- **YYLabel**: 支持富文本和更多展示特性的label，用于处理多行文本

### 代码示例

#### Model层
```objc
@interface FMOptionalReportModel : NSObject

@property (nonatomic, copy) NSString *stockCode; // 股票代码
@property (nonatomic, copy) NSString *stockName; // 股票名称
@property (nonatomic, assign) NSInteger secuMarket; // 发行市场：83-上海证券交易所，90-深圳证券交易所

@property (nonatomic, copy) NSString *reportId;
@property (nonatomic, copy) NSString *orgName; // 机构名称
@property (nonatomic, copy) NSString *title; // 标题
@property (nonatomic, assign) long long infoPublDate; // 发布时间
@property (nonatomic, assign) NSInteger currentRating; // 本期评级
@property (nonatomic, copy) NSString *exRightGoalPrice; // 除权目标价格(元)
@property (nonatomic, assign) NSInteger researchDepth; // 研究深度7就显示"深度分析"

@property (nonatomic, copy) NSString *ex_currentRatingStr;
@property (nonatomic, strong) UIColor *ex_ratingColor;

@end

// 实现评级和颜色的转换方法
- (NSString *)ex_currentRatingStr {
    NSString *ratingStr;
    switch (self.currentRating) {
        case 10:
            ratingStr = @"买入";
            break;
        // ...其他评级
        default:
            ratingStr = @"";
            break;
    }
    return ratingStr;
}

- (UIColor *)ex_ratingColor {
    UIColor *color;
    switch (self.currentRating) {
        case 10:
            color = ColorWithHex(0xFC3C3E);
            break;
        // ...其他颜色
    }
    return color;
}

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{@"reportId" : @"id"};
}
```

#### View层
```objc
@interface FMOptionalReportCell : UITableViewCell
@property (nonatomic, strong) FMOptionalReportModel *model;
@end

@implementation FMOptionalReportCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    // 上部分：股票信息
    UIStackView *stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisHorizontal 
                                                     alignment:UIStackViewAlignmentCenter 
                                                  distribution:UIStackViewDistributionEqualSpacing 
                                                       spacing:10 
                                                arrangedSubviews:@[self.stockNameLabel, self.priceLabel, self.changePercentLabel]];
    [self.contentView addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.top.equalTo(10);
        make.height.equalTo(22.5);
    }];
    WEAKSELF
    [stackView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
    }]];
    
    // 中间部分：标题
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.top.equalTo(stackView.mas_bottom).offset(10);
        make.right.equalTo(-15);
    }];
    
    // 底部部分：标签信息
    UIStackView *stackView2 = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisHorizontal 
                                                      alignment:UIStackViewAlignmentCenter 
                                                   distribution:UIStackViewDistributionEqualSpacing 
                                                       spacing:10 
                                                arrangedSubviews:@[self.tagLabel, self.depthAnalysisLabel, self.targetPriceLabel]];
    [self.contentView addSubview:stackView2];
    [stackView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(12);
        make.height.equalTo(20);
        make.bottom.equalTo(-10);
    }];
    
    // 时间标签
    [self.contentView addSubview:self.timeLabel];
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(stackView2);
        make.right.equalTo(self.titleLabel);
    }];
}

- (void)setModel:(FMOptionalReportModel *)model {
    _model = model;
    
    self.stockNameLabel.text = model.stockName;
    // 价格、涨跌幅此处省略
    
    self.titleLabel.text = [NSString stringWithFormat:@"%@-%@", model.orgName, model.title];
    
    // 评级标签处理
    if (model.ex_currentRatingStr.length) {
        self.tagLabel.text = model.ex_currentRatingStr;
        self.tagLabel.backgroundColor = model.ex_ratingColor;
        self.tagLabel.hidden = NO;
    } else {
        self.tagLabel.hidden = YES;
    }
    
    // 深度分析标签
    self.depthAnalysisLabel.hidden = model.researchDepth != 7;
    
    // 目标价格标签
    if (model.exRightGoalPrice.length) {
        self.targetPriceLabel.hidden = NO;
        NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
        formatter.numberStyle = NSNumberFormatterDecimalStyle;
        formatter.minimumFractionDigits = 0;
        formatter.maximumFractionDigits = 2;
        NSString *formattedPrice = [formatter stringFromNumber:@(model.exRightGoalPrice.floatValue)];
        self.targetPriceLabel.text = [NSString stringWithFormat:@"目标价 %@", formattedPrice];
    } else {
        self.targetPriceLabel.hidden = YES;
    }
    
    // 时间处理
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.infoPublDate / 1000.0];
    self.timeLabel.text = [date dateStringWithFormatString:@"yyyy-MM-dd"];
}

- (UILabel *)stockNameLabel {
    if (!_stockNameLabel) {
        _stockNameLabel = [[UILabel alloc] init];
        _stockNameLabel.font = FontWithSize(16);
        _stockNameLabel.textColor = ColorWithHex(0x0074FA);
    }
    return _stockNameLabel;
}

- (UILabel *)priceLabel {
    if (!_priceLabel) {
        _priceLabel = [[UILabel alloc] init];
        _priceLabel.font = FontWithSize(14);
    }
    return _priceLabel;
}

- (UILabel *)changePercentLabel {
    if (!_changePercentLabel) {
        _changePercentLabel = [[UILabel alloc] init];
        _changePercentLabel.font = FontWithSize(14);
    }
    return _changePercentLabel;
}


- (YYLabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[YYLabel alloc] init];
        _titleLabel.textColor = ColorWithHex(0x333333);
        _titleLabel.font = FontWithSize(15);
        _titleLabel.numberOfLines = 2;
        _titleLabel.preferredMaxLayoutWidth = UI_SCREEN_WIDTH - 30;
    }
    
    return _titleLabel;
}

- (ZLTagLabel *)tagLabel {
    if (!_tagLabel) {
        _tagLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:FMWhiteColor backgroundColor:FMNavColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _tagLabel.widthPadding = 10;
        _tagLabel.heightPadding = 4;
        UI_View_Radius(_tagLabel, 2);
    }
    
    return _tagLabel;
}

- (UILabel *)depthAnalysisLabel {
    if (!_depthAnalysisLabel) {
        _depthAnalysisLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:FMNavColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        _depthAnalysisLabel.text = @"深度分析";
        _depthAnalysisLabel.hidden = YES;
    }
    
    return _depthAnalysisLabel;
}

- (UILabel *)targetPriceLabel {
    if (!_targetPriceLabel) {
        _targetPriceLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:ColorWithHex(0x466484) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    
    return _targetPriceLabel;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:ColorWithHex(0x999999) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    }
    
    return _timeLabel;
}
@end
```

#### Controller层
```objc
@interface FMOptionalReportViewController ()<UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *dataArr;
@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;

@end

@implementation FMOptionalReportViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = FMWhiteColor;
    
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
    
    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 10;
    [self.tableView.mj_header beginRefreshing];
}

#pragma mark - UITableView
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMOptionalReportCell *cell = [tableView reuseCellClass:[FMOptionalReportCell class]];
    cell.model = self.dataArr[indexPath.row];
    return cell;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - HTTP
- (void)requestData {
    NSArray *selfStocks = [FMSelfOptionStockCacheTool getSelfStocks];
    [HttpRequestTool requestF10OptionalReportWithPage:self.page pageSize:self.pageSize  stockCodes:selfStocks start:^{
    } failure:^{
        [self endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self endRefreshForSuccess];

            if (self.page == 1) {
                [self.tableView.mj_footer resetNoMoreData];
                [self.dataArr removeAllObjects];
            }

            NSArray *dataArr = [NSArray modelArrayWithClass:[FMOptionalReportModel class] json:dic[@"data"]];
            if (dataArr.count < self.pageSize) {
                [self.tableView.mj_footer endRefreshingWithNoMoreData];
            }
            [self.dataArr addObjectsFromArray:dataArr];
            
            if (!self.dataArr.count) {
                [self.tableView showNoDataViewWithImage:ImageWithName(@"common_nodata") string:@"暂无数据" attributes:nil offsetY:80];
                self.tableView.mj_footer.hidden = YES;
            } else {
                [self.tableView dismissNoDataView];
                self.tableView.mj_footer.hidden = NO;
            }
            
            [self.tableView reloadData];
        } else {
            [self endRefreshForFailure];
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - Private
- (void)headerAction {
    self.page = 1;
    [self requestData];
}

- (void)footerAction {
    self.page++;
    [self requestData];
}

// 失败状态下停止refresh
- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.currentPage = self.page;
}

#pragma mark - Getter/Setter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(headerAction) footerTarget:self footerAction:@selector(footerAction)];
        _tableView.separatorInset = UIEdgeInsetsMake(0, 0, 0, 0);
        _tableView.mj_footer.hidden = YES;
        [_tableView registerCellClass:[FMOptionalReportCell class]];
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.estimatedRowHeight = 110;
    }
    
    return _tableView;
}

- (NSMutableArray *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray array];
    }
    
    return _dataArr;
}
```

### 注意事项
- 使用UIStackView来灵活处理需要显示/隐藏的标签，避免频繁调整约束
- 使用YYLabel处理可能需要多行显示的标题文本
- 对于日期和价格等数据，需要进行合适的格式化处理
- 标签的显示和隐藏需要根据实际数据进行判断
- 实现上拉加载更多和下拉刷新功能，优化用户体验
- 处理无数据状态的显示，避免空白页面

### 版本记录
| 版本 | 日期 | 修改说明 |
|-----|------|---------|
| 1.0 | 2023-10-01 | 初始版本 |
