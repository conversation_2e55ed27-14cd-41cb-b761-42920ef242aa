# 宽度超过一屏、带固定列的列表

### UI 设计图

<img src="images/宽度很宽的列表.png" alt="宽度很宽的列表" style="zoom:40%;" />

这是 UI 设计图，在列表右边还有很多字段没有显示出来

<img src="images/宽度很宽的列表原型图.jpg" alt="原型图" style="zoom:50%;" />

这是原型设计图

### 页面结构分析

"已上市"列表中，顶部灰色红字是 title行，后面每一行 Cell 表示一个股票的数据。名称代码这一列是固定的（相当于 Excel 里面的冻结窗格），页面左滑时可以展示超出屏幕范围的数据。

### 组件说明

- **FMLHBFixColumnTableView**: 支持固定列的表格视图，可以水平滚动查看更多列
  - `fixViewSize`: 固定列的大小
  - `fixColumnTitle`: 固定列的标题
  - `sortModels`: 表格可排序的列配置
  - `scrollContentWidth`: 可滚动内容的宽度
  
- **FMLHBFixColumnTitleModel**: 列标题模型，用于配置表头
  - `title`: 列标题文字
  - `width`: 列宽度
  - `margin`: 列的起始偏移量
  - `sortKey`: 排序使用的键值
  - `isAlignmentLeft`: 是否左对齐
  
- **FMLHBFixColumnModel**: 列数据模型
  - `stockModel`: 固定列的数据模型
  - `itemModels`: 可滚动列的数据模型集合

### 代码示例

#### Controller层

```objc
#import "FMIPOCalendarListedListViewController.h"
#import "FMLHBFixColumnTableView.h"
#import "HttpRequestTool+IPO.h"
#import "FMIPOCalendarInMarketModel.h"
#import "FMIPOCalendarApplyDetailViewController.h"

@interface FMIPOCalendarListedListViewController ()<FMLHBFixColumnTableViewDelegate>

@property (nonatomic, strong) FMLHBFixColumnTableView *tableView;

@property (nonatomic, strong) NSMutableArray<FMLHBFixColumnModel *> *fixColumnModels;
@property (nonatomic, strong) NSArray<FMLHBFixColumnTitleModel *> *sortModels;

@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;


@end

@implementation FMIPOCalendarListedListViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.view.backgroundColor = FMWhiteColor;
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(0);
        make.bottom.equalTo(0);
    }];
    self.tableView.hidden = YES;
    
    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 100;
    
    [SVProgressHUD show];
    
    [self requestData];
}

#pragma mark - FMLHBFixColumnTableViewDelegate
- (void)columnTableView:(FMLHBFixColumnTableView *)tableView selectedIndex:(NSInteger)index {
    FMLHBFixColumnModel *model = self.fixColumnModels[index];
    UPMarketCodeMatchInfo *matchInfo = [FMUPDataTool matchInfoWithSetCodeAndCode:model.stockModel.extraDic[@"stockCode"]];
    if (![matchInfo isKindOfClass:UPMarketCodeMatchInfo.class] || !matchInfo.code.length) {
        return;
    }
    [UPRouterUtil goMarketStock:matchInfo.setCode code:matchInfo.code];
}

#pragma mark - request
- (void)requestData {
    WEAKSELF
    [HttpRequestTool requestIPOCalendarInMarketWithWithPage:self.page pageSize:self.pageSize start:^{
    } failure:^{
        [SVProgressHUD dismiss];
        [self endRefreshForFailure];
        [self.view showReloadNetworkViewWithBlock:^{
            [__weakSelf requestData];
        }];
    } success:^(NSDictionary *dic) {
        [SVProgressHUD dismiss];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [self endRefreshForSuccess];
            if (self.page == 1) {
                [self.tableView.mj_footer resetNoMoreData];
                [self.fixColumnModels removeAllObjects];
            } 

            NSArray *dataArr = [NSArray modelArrayWithClass:[FMIPOCalendarInMarketModel class] json:dic[@"data"]];
            if (dataArr.count < self.pageSize) {
                [self.tableView.mj_footer endRefreshingWithNoMoreData];
                self.tableView.tableFooterView = [FMStockDetaiDisclaimerTool tableFooterView];
            } else {
                self.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 0)];
            }
            [self dealData:dataArr];
            [self.tableView reloadData];
            self.tableView.hidden = NO;
        } else {
            [self.view showReloadNetworkViewWithBlock:^{
                [__weakSelf requestData];
            }];
        }
    }];
}

- (void)dealData:(NSArray<FMIPOCalendarInMarketModel *> *)dataArr {
    for (NSInteger i = 0 ; i < dataArr.count; i++) {
        FMIPOCalendarInMarketModel *model = dataArr[i];
        FMLHBFixColumnModel *fixColumnModel = [FMLHBFixColumnModel new];
        
        FMLHBFixColumnFixItemModel *stockModel = [FMLHBFixColumnFixItemModel new];
        stockModel.nameAttrStr = [[NSAttributedString alloc] initWithString:model.stockName attributes:@{NSFontAttributeName : FontWithSize(16), NSForegroundColorAttributeName : ColorWithHex(0x333333)}];
        NSMutableAttributedString *stockCodeAttrStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@" %@", [model.stockCode substringFromIndex:2]] attributes:@{NSFontAttributeName : FontWithSize(13), NSForegroundColorAttributeName : ColorWithHex(0x999999)}];
        [stockCodeAttrStr insertAttributedString:[self stockCodeImgAttachmentWithModel:model] atIndex:0];
        stockModel.subtitleAttrStr = stockCodeAttrStr;
        stockModel.extraDic[@"stockCode"] = model.stockCode;
        fixColumnModel.stockModel = stockModel;
        
        NSMutableArray *itemModels = [NSMutableArray array];
        NSArray *titles = [self getColumnTitleWithModel:model];
        for (NSInteger i = 0; i < titles.count; i++) {
            FMLHBFixColumnScollItemModel *model = [FMLHBFixColumnScollItemModel new];
            model.margin = self.tableView.sortModels[i].margin;
            model.width = self.tableView.sortModels[i].width;
            model.titleAttrStr = titles[i];
            [itemModels addObject:model];
        }
        fixColumnModel.itemModels = itemModels;
        
        [self.fixColumnModels addObject:fixColumnModel];
    }
    
    self.tableView.fixColumnModes = self.fixColumnModels;
    
    if (!self.fixColumnModels.count) {
        self.tableView.mj_footer.hidden = YES;
    } else {
        self.tableView.mj_footer.hidden = NO;
    }

}

- (NSMutableAttributedString *)stockCodeImgAttachmentWithModel:(FMIPOCalendarInMarketModel *)model {
    NSString *str;
    UIColor *bgColor;
    if (model.secuMarket == 83) {
        str = @"沪";
       bgColor = ColorWithHex(0xff5757);
    } else if (model.secuMarket == 90) {
        str = @"深";
       bgColor = ColorWithHex(0xFF8031);
    }
    if (model.listedSector == 7) {
        str = @"科";
       bgColor = ColorWithHex(0x2D46B9);
    } else if (model.listedSector == 6) {
        str = @"创";
       bgColor = ColorWithHex(0x4D89FF);
    }
    CGSize textSize = [str sizeWithAttributes:@{NSFontAttributeName : BoldFontWithSize(11)}];
    CGSize textImageSize = CGSizeMake(16, 16);
    UIImage *bgImg = [UIImage imageWithColor:bgColor andSize:textImageSize];
    UIImage *textImg = [bgImg addTextWithImageSize:textImageSize text:str textRect:CGRectMake((textImageSize.width - textSize.width) * 0.5, (textImageSize.height - textSize.height) * 0.5, textSize.width, textSize.height) textAttributes:@{NSFontAttributeName : BoldFontWithSize(11), NSForegroundColorAttributeName : FMWhiteColor}];
    return [NSAttributedString yy_attachmentStringWithContent:[textImg addCornerRadius:2.0] contentMode:UIViewContentModeCenter attachmentSize:textImageSize alignToFont:FontWithSize(13) alignment:YYTextVerticalAlignmentCenter];
}

- (NSArray *)getColumnTitleWithModel:(FMIPOCalendarInMarketModel *)model {
    NSDictionary *defaultDic = @{NSFontAttributeName : MediumFontWithSize(15), NSForegroundColorAttributeName : ColorWithHex(0x333333)};
    // 上市日
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.listDate / 1000.0];
    NSMutableAttributedString *attrstr0 = [[NSMutableAttributedString alloc] initWithString:[date dateStringWithFormatString:@"yyyy-MM-dd"] attributes:defaultDic];
    // 发行价
    NSMutableAttributedString *attrstr1 = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%.2f元", model.issuePrice] attributes:defaultDic];
    // 每签最高获利
    NSString *profit = [FMHelper formattedStockValueWithNumber:model.maximumProfitPerLot positiveSuffix:nil negativeSuffix:@"-" decimalPlacesForBillions:2 decimalPlacesForTenThousands:2];
    if (![profit hasSuffix:@"万"] && ![profit hasSuffix:@"亿"]) {
        profit = [profit stringByAppendingString:@"元"];
    }
    NSMutableAttributedString *attrstr2 = [[NSMutableAttributedString alloc] initWithString:profit attributes:@{NSFontAttributeName : MediumFontWithSize(15), NSForegroundColorAttributeName : [self judgeDataColorWithData:model.maximumProfitPerLot]}];
    // 首日最高涨幅
    NSMutableAttributedString *attrstr3 = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%.2f%%", model.firstHighChangePCT] attributes:@{NSFontAttributeName : MediumFontWithSize(15), NSForegroundColorAttributeName : [self judgeDataColorWithData:model.firstHighChangePCT]}];
    // 首日开盘涨幅
    NSMutableAttributedString *attrstr4 = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%.2f%%", model.firstOpenChangePCT] attributes:@{NSFontAttributeName : MediumFontWithSize(15), NSForegroundColorAttributeName : [self judgeDataColorWithData:model.firstOpenChangePCT]}];
    // 首日收盘涨幅
    NSMutableAttributedString *attrstr5 = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%.2f%%", model.firstChangePCT] attributes:@{NSFontAttributeName : MediumFontWithSize(15), NSForegroundColorAttributeName : [self judgeDataColorWithData:model.firstChangePCT]}];
    
    return @[attrstr0, attrstr1, attrstr2, attrstr3, attrstr4, attrstr5];
}

- (UIColor *)judgeDataColorWithData:(CGFloat)data {
    if (data > 0) {
        return FMNavColor;
    } else if (data < 0) {
        return ColorWithHex(0x1DAA34);
    } else {
        return ColorWithHex(0x333333);
    }
}

// 失败状态下停止refresh
- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.currentPage = self.page;
}

- (void)footerAction {
    self.page++;
    [self requestData];
}


#pragma mark - setter/getter
- (FMLHBFixColumnTableView *)tableView {
    if (!_tableView) {
        _tableView = [[FMLHBFixColumnTableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
        _tableView.separatorInset = UIEdgeInsetsMake(0, 0, 0, 0);
        _tableView.estimatedRowHeight = 63;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        
        MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(footerAction)];
        footer.stateLabel.textColor = ColorWithHex(0xbfbfbf);
        _tableView.mj_footer = footer;
        _tableView.mj_footer.hidden = YES;
        
        _tableView.infoDelegate = self;
        _tableView.fixColumnTitle = @"名称代码";
        _tableView.fixViewSize = CGSizeMake(100, 63);
        _tableView.sortModels = self.sortModels;
        _tableView.scrollContentWidth = self.sortModels[0].width + self.sortModels[0].margin;
    }
    
    return _tableView;
}

- (NSMutableArray <FMLHBFixColumnModel *> *)fixColumnModels {
    if (!_fixColumnModels) {
        _fixColumnModels = [NSMutableArray array];
    }
    
    return _fixColumnModels;
}

- (NSArray<FMLHBFixColumnTitleModel *> *)sortModels {
    if (!_sortModels) {
        NSMutableArray *mutableArray = [NSMutableArray array];
        
        {
            FMLHBFixColumnTitleModel *model = [[FMLHBFixColumnTitleModel alloc] init];
            model.title = @"上市日";
            model.width = 95.0f;
            [mutableArray addObject:model];
        }
        {
            FMLHBFixColumnTitleModel *model = [[FMLHBFixColumnTitleModel alloc] init];
            model.title = @"发行价";
            model.width = 90.0f;
            [mutableArray addObject:model];
        }
        {
            FMLHBFixColumnTitleModel *model = [[FMLHBFixColumnTitleModel alloc] init];
            model.title = @"每签最高获利";
            model.width = 110.f;
            [mutableArray addObject:model];
        }
        {
            FMLHBFixColumnTitleModel *model = [[FMLHBFixColumnTitleModel alloc] init];
            model.title = @"首日最高涨幅";
            model.width = 110.0f;
            [mutableArray addObject:model];
        }
        {
            FMLHBFixColumnTitleModel *model = [[FMLHBFixColumnTitleModel alloc] init];
            model.title = @"首日开盘涨幅";
            model.width = 110.0f;
            [mutableArray addObject:model];
        }
        {
            FMLHBFixColumnTitleModel *model = [[FMLHBFixColumnTitleModel alloc] init];
            model.title = @"首日收盘涨幅";
            model.width = 110.f;
            [mutableArray addObject:model];
        }

        for (NSInteger i = mutableArray.count - 1; i >= 0; --i) {
            FMLHBFixColumnTitleModel *model = mutableArray[i];
            if (i == mutableArray.count - 1) {
                model.margin = 15.f;
            } else {
                FMLHBFixColumnTitleModel *lastModel = mutableArray[i + 1];
                model.margin = lastModel.margin + lastModel.width;
            }
        }
        
        _sortModels = [mutableArray copy];
    }
    
    return _sortModels;
}

@end
```

### 注意事项
- 设置 `sortModels` 时需要从右到左计算每个列的 margin 值，确保列表正确布局
- 实现多个回调方法处理各种交互场景，如点击、排序和滚动
- 对数据展示需要进行正负值的颜色区分处理
- 设置滚动宽度时需将所有列宽与边距考虑在内

### 版本记录
| 版本 | 日期 | 修改说明 |
|-----|------|---------|
| 1.0 | 2024-01-27 | 初始版本 |

