# 底部弹窗

### UI 设计图

<img src="images/底部弹框.jpg" alt="底部弹框" style="zoom:40%;" />

### 页面结构分析

这是一个带有动画效果的底部弹框，常用于选择操作或展示信息。中间的内容区域根据后台返回的数据动态生成，可能包含多组数据，因此使用UITableView展示。

TableView的Cell高度是动态的，需要根据数据内容计算整体TableView的高度，同时设置一个最大高度限制，防止内容过多时占满整个屏幕。

### 组件说明

- **UIView**: 作为弹窗的主容器，处理背景半透明和动画效果
- **UITableView**: 用于展示列表数据，支持动态高度计算
- **UIButton**: 用于"确定"操作按钮和关闭按钮
- **UILabel**: 用于标题和内容文本显示
- **ZLTagLabel**: 用于显示标签信息

### 代码示例

#### Model层
```objc
@interface FMMemberCenterProductGroupModel : NSObject

@property (nonatomic, copy) NSString *groupName;       // 分组名称
@property (nonatomic, copy) NSString *introduction;    // 分组介绍
@property (nonatomic, copy) NSString *promotion;       // 促销信息
@property (nonatomic, assign) BOOL isChoosed;          // 是否选中
@property (nonatomic, assign) CGFloat cellHeight;      // Cell高度

@end
```

#### View层
```objc
// 自定义Cell
@interface FMMemberCenterProductChooseGroupCell : UITableViewCell

@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) ZLTagLabel *tagLabel;
@property (nonatomic, strong) UILabel *descLabel;
@property (nonatomic, strong) UIImageView *chooseImgV;

@property (nonatomic, strong) FMMemberCenterProductGroupModel *model;

@end

@implementation FMMemberCenterProductChooseGroupCell

- (void)setupUI {
    self.selectionStyle = UITableViewCellSelectionStyleNone;

    // 分组名称
    UILabel *nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(16) textColor:ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:nameLabel];
    [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@16.5);
        make.height.equalTo(@22.5);
    }];
    self.nameLabel = nameLabel;
    
    // 促销标签
    ZLTagLabel *tagLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:FMWhiteColor backgroundColor:FMNavColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    tagLabel.widthPadding = 10.0f;
    tagLabel.heightPadding = 5.0f;
    [self.contentView addSubview:tagLabel];
    [tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(nameLabel.mas_right).offset(5);
        make.right.lessThanOrEqualTo(@-40);
        make.centerY.equalTo(nameLabel);
    }];
    self.tagLabel = tagLabel;
    UI_View_BorderRadius(self.tagLabel, 2, 1, FMNavColor);

    // 描述文本
    UILabel *descLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:descLabel];
    [descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(nameLabel);
        make.right.equalTo(@-40);
        make.top.equalTo(nameLabel.mas_bottom).offset(5);
    }];
    self.descLabel = descLabel;
    
    // 选中状态图标    
    UIImageView *chooseImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"MemberCenter_ProductChooseGroupChoosed")];
    [self.contentView addSubview:chooseImgV];
    [chooseImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-15);
        make.centerY.equalTo(@0);
        make.width.height.equalTo(@16);
    }];
    self.chooseImgV = chooseImgV;
}

- (void)setModel:(FMMemberCenterProductGroupModel *)model {
    _model = model;
    
    self.nameLabel.text = model.groupName;
    
    // 根据是否有促销信息显示标签
    if (model.promotion.length) {
        self.tagLabel.text = model.promotion;
        self.tagLabel.hidden = NO;
    } else {
        self.tagLabel.hidden = YES;
    }

    self.descLabel.text = model.introduction;
    
    // 设置选中状态    
    if (model.isChoosed) {
        self.chooseImgV.image = ImageWithName(@"MemberCenter_ProductChooseGroupChoosed");
    } else {
        self.chooseImgV.image = ImageWithName(@"MemberCenter_ProductChooseGroupNoChoosed");
    }
}
@end

// 底部弹窗视图
@interface FMMemberCenterProductChooseGroupView : UIView

- (instancetype)initWithDataArr:(NSArray <FMMemberCenterProductGroupModel *> *)dataArr chooseBlock:(void(^)(FMMemberCenterProductGroupModel *groupModel))chooseBlock;
- (void)show;

@end

@implementation FMMemberCenterProductChooseGroupView

- (instancetype)initWithDataArr:(NSArray <FMMemberCenterProductGroupModel *> *)dataArr chooseBlock:(nonnull void (^)(FMMemberCenterProductGroupModel *))chooseBlock{
    self = [super init];
    if (self) {
        self.dataArr = dataArr;
        
        // 计算每个Cell的高度和总高度
        for (FMMemberCenterProductGroupModel *model in dataArr) {
            if (model.isChoosed) {
                self.choosedModel = model;
            }
            NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
            style.lineSpacing = 3.0f;
            model.cellHeight = ceil([model.introduction sizeWithAttr:@{NSParagraphStyleAttributeName : style, NSFontAttributeName : FontWithSize(14)} andMaxSize:CGSizeMake(UI_SCREEN_WIDTH - 55, CGFLOAT_MAX)].height) + 60;
            self.tableViewHeight += model.cellHeight;
        }
        
        // 限制最大高度
        if (self.tableViewHeight > UI_SCREEN_HEIGHT - UI_SAFEAREA_TOP_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT - 150) {
            self.tableViewHeight = UI_SCREEN_HEIGHT - UI_SAFEAREA_TOP_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT - 150;
        }
        self.chooseBlock = chooseBlock;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // 半透明背景
    UIView *bgView = [[UIView alloc] init];
    [self addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
    bgView.backgroundColor = ColorWithHexAlpha(0x000000, 0.6);
    
    // 内容视图
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, UI_SCREEN_HEIGHT, UI_SCREEN_WIDTH, self.tableViewHeight + 130 + UI_SAFEAREA_BOTTOM_HEIGHT)];
    contentView.backgroundColor = FMWhiteColor;
    [self addSubview:contentView];
    [contentView layerAndBezierPathWithRect:contentView.bounds cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerTopLeft|UIRectCornerTopRight];
    self.contentView = contentView;
    
    // 标题
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) textColor:FMZeroColor backgroundColor:FMWhiteColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [contentView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(@13);
        make.height.equalTo(@24);
    }];
    titleLabel.text = @"选择分组";
    
    // 关闭按钮
    UIButton *closeBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMWhiteColor title:nil image:ImageWithName(@"MemberCenter_ProductChooseGroupClose") target:self action:@selector(dismiss)];
    [contentView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@30);
        make.top.equalTo(@10);
        make.right.equalTo(@-10);
    }];
    
    // 分割线
    [contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(@49.3);
        make.height.equalTo(@0.7);
    }];
    
    // 列表视图
    [contentView addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(@50);
        make.height.equalTo(@(self.tableViewHeight));
    }];
    
    // 确定按钮
    [contentView addSubview:self.commitBtn];
    [self.commitBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(self.tableView.mas_bottom).offset(15);
        make.height.equalTo(@45);
    }];
}

// 显示弹窗
- (void)show {
    [[UIApplication sharedApplication].keyWindow addSubview:self];
    self.frame = [UIScreen mainScreen].bounds;
    [UIView animateWithDuration:0.3 animations:^{
        self.contentView.frame = CGRectMake(0, UI_SCREEN_HEIGHT - self.tableViewHeight - 130 - UI_SAFEAREA_BOTTOM_HEIGHT, UI_SCREEN_WIDTH, self.tableViewHeight + 130 + UI_SAFEAREA_BOTTOM_HEIGHT);
    }];
}

// 关闭弹窗
- (void)dismiss {
    [UIView animateWithDuration:0.3 animations:^{
        self.contentView.frame = CGRectMake(0, UI_SCREEN_HEIGHT, UI_SCREEN_WIDTH, self.tableViewHeight + 130 + UI_SAFEAREA_BOTTOM_HEIGHT);
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

// UITableViewDelegate和UITableViewDataSource实现
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMMemberCenterProductChooseGroupCell *cell = [tableView reuseCellClass:[FMMemberCenterProductChooseGroupCell class]];
    cell.model = self.dataArr[indexPath.row];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMMemberCenterProductGroupModel *model = self.dataArr[indexPath.row];
    return model.cellHeight;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    
    // 更新选中状态
    self.choosedModel.isChoosed = NO;
    FMMemberCenterProductGroupModel *model = self.dataArr[indexPath.row];
    model.isChoosed = YES;
    self.choosedModel = model;
    
    [tableView reloadData];
}

// 确定按钮点击处理
- (void)commit {
    if (self.chooseBlock) {
        self.chooseBlock(self.choosedModel);
    }
    [self dismiss];
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:nil];
        _tableView.separatorInset = UIEdgeInsetsMake(0, 15, 0, 15);
        _tableView.separatorColor = FMSepLineColor;
        [_tableView registerCellClass:[FMMemberCenterProductChooseGroupCell class]];
        _tableView.tableFooterView = [UIView new];
        _tableView.bounces = NO;
    }
    return _tableView;
}

- (UIButton *)commitBtn {
    if (!_commitBtn) {
        _commitBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(16) normalTextColor:FMWhiteColor backgroundColor:FMNavColor title:@"确定" image:nil target:self action:@selector(commit)];
        UI_View_Radius(_commitBtn, 22.5);
    }
    
    return _commitBtn;
}
@end

#### Controller层
```objc
@interface FMMemberCenterViewController : UIViewController

@property (nonatomic, strong) NSArray<FMMemberCenterProductGroupModel *> *groupModels;

@end

@implementation FMMemberCenterViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    [self loadData];
}

// 显示选择分组弹窗
- (void)showChooseGroupView {
    FMMemberCenterProductChooseGroupView *chooseView = [[FMMemberCenterProductChooseGroupView alloc] initWithDataArr:self.groupModels chooseBlock:^(FMMemberCenterProductGroupModel *groupModel) {
        // 处理选择结果
        [self updateGroupSelection:groupModel];
    }];
    [chooseView show];
}

// 更新选中的分组
- (void)updateGroupSelection:(FMMemberCenterProductGroupModel *)groupModel {
    // 更新UI和数据
    NSLog(@"已选择分组：%@", groupModel.groupName);
    
    // 可以在这里执行网络请求或其他更新操作
    [self requestUpdateGroup:groupModel.groupName];
}

// 加载数据
- (void)loadData {
    // 模拟数据
    NSMutableArray *groups = [NSMutableArray array];
    
    FMMemberCenterProductGroupModel *model1 = [[FMMemberCenterProductGroupModel alloc] init];
    model1.groupName = @"会员组1";
    model1.introduction = @"这是会员组1的简介，包含会员相关权益说明";
    model1.promotion = @"推荐";
    model1.isChoosed = YES;
    [groups addObject:model1];
    
    FMMemberCenterProductGroupModel *model2 = [[FMMemberCenterProductGroupModel alloc] init];
    model2.groupName = @"会员组2";
    model2.introduction = @"这是会员组2的简介，包含会员相关权益说明及其他更多详细内容，文字可能会比较多，需要自动计算高度";
    [groups addObject:model2];
    
    self.groupModels = groups;
}

@end
```

### 注意事项
- 使用UIView的动画方法实现弹出和消失的过渡效果
- 计算TableView内容高度并设置最大限制，避免内容过多时占满整个屏幕
- 为每个Cell计算动态高度，确保内容完整显示
- 使用cornerRadius给内容视图添加圆角效果，提升UI美观度
- 添加半透明背景层，使用户关注弹窗内容
- 通过Block回调处理用户选择结果
- 处理内存管理，在dismiss时从父视图中移除
- 考虑不同屏幕尺寸下的适配，使用安全区域和相对布局
- 当弹窗内容较多时，确保可以滚动查看全部内容
- 为提高用户体验，可以在背景上添加点击手势来关闭弹窗

### 版本记录
| 版本 | 日期 | 修改说明 |
|-----|------|---------|
| 1.0 | 2023-10-01 | 初始版本 |
	
```

