# 可展开折叠的Cell

### UI 设计图

<img src="images/展开折叠 Cell.jpg" alt="可展开折叠的 Cell" style="zoom:50%;" />

### 页面结构分析

这是一个列表页面，可以用 UITableView 加载。每个 Cell 包含股票名、涨幅和详情 Label。关键功能是如何处理详情文本的展开和收起，本例使用 YYLabel 的 truncationToken 特性来实现，并在 Model 中添加展开状态的标识。

### 组件说明

- **UITableView**: 主列表视图，用于显示可折叠的内容
- **YYLabel**: 实现"展开/收起"功能的核心组件，通过truncationToken属性添加交互按钮
- **UITapGestureRecognizer**: 用于处理标签点击事件，触发展开/折叠操作
- **UIButton**: 用作收起按钮，在展开状态显示

### 代码示例

#### Model层
```objc
@interface FMF10ConceptThemeModel : NSObject
@property (nonatomic, copy) NSString *conceptName;
@property (nonatomic, copy) NSString *remark;
@property (nonatomic, assign, getter=isDescUnFold) BOOL descUnFold;
@end
```

#### View层
```objc
@interface FMF10ConceptThemeCell : UITableViewCell
@property (nonatomic, strong) FMF10ConceptThemeModel *model;
@property (nonatomic, copy) void(^refreshBlock)();
@end

@implementation FMF10ConceptThemeCell

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    // 标题
    [self.contentView addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(15);
    }];
    
    // 代码标签
    [self.contentView addSubview:self.codeLabel];
    [self.codeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel.mas_right).offset(5);
        make.centerY.equalTo(self.nameLabel);
    }];
    
    // 描述文本(可展开折叠部分)
    [self.contentView addSubview:self.descLabel];
    [self.descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(self.nameLabel.mas_bottom).offset(10);
    }];
    
    // 收起按钮
    [self.contentView addSubview:self.foldBtn];
    [self.foldBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.bottom.equalTo(-15);
        make.top.equalTo(self.descLabel.mas_bottom).offset(5);
        make.height.equalTo(20);
    }];
    
    // 分割线
    [self.contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.right.bottom.equalTo(0);
        make.height.equalTo(0.5);
    }];
}

- (void)setModel:(FMF10ConceptThemeModel *)model {
    _model = model;
    
    self.nameLabel.text = model.conceptName;
    self.codeLabel.hidden = YES;
    
    // 设置富文本描述
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:model.remark];
    attrStr.yy_lineSpacing = 3.0;
    attrStr.yy_font = FontWithSize(15);
    attrStr.yy_color = ColorWithHex(0x333333);
    self.descLabel.attributedText = attrStr;
    [self addSeeMoreButton];

    // 根据展开状态处理UI
    if (model.isDescUnFold) {
        self.descLabel.numberOfLines = 0;
        if ([self calculateNumberOfLinesForText:model.remark font:FontWithSize(15) width:UI_SCREEN_WIDTH - 30] > 3) {
            self.foldBtn.hidden = NO;
            [self.foldBtn mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.descLabel.mas_bottom).offset(5);
                make.height.equalTo(20);
            }];
        } else {
            self.foldBtn.hidden = YES;
            [self.foldBtn mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.descLabel.mas_bottom).offset(0);
                make.height.equalTo(0);
            }];
        }
    } else {
        self.descLabel.numberOfLines = 3;
        self.foldBtn.hidden = YES;
        [self.foldBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.descLabel.mas_bottom).offset(0);
            make.height.equalTo(0);
        }];
    }
}

// 计算文本行数的辅助方法
- (NSInteger)calculateNumberOfLinesForText:(NSString *)text font:(UIFont *)font width:(CGFloat)width {
    UILabel *tempLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, width, CGFLOAT_MAX)];
    tempLabel.numberOfLines = 0;
    tempLabel.font = font;
    tempLabel.text = text;
    
    [tempLabel sizeToFit];
    
    CGSize textSize = [tempLabel sizeThatFits:CGSizeMake(width, CGFLOAT_MAX)];
    NSInteger numberOfLines = (NSInteger)(textSize.height / tempLabel.font.lineHeight);
    
    return numberOfLines;
}

// 添加"展开"按钮
- (void)addSeeMoreButton {
    NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@"...展开"];
    [text yy_setColor:ColorWithHex(0x0074FA) range:[text.string rangeOfString:@"展开"]];
    text.yy_font = FontWithSize(15);
    YYLabel *seeMore = [YYLabel new];
    seeMore.attributedText = text;
    [seeMore sizeToFit];
    NSAttributedString *truncationToken = [NSAttributedString yy_attachmentStringWithContent:seeMore contentMode:UIViewContentModeCenter attachmentSize:text.size alignToFont:text.yy_font alignment:YYTextVerticalAlignmentCenter];
    self.descLabel.truncationToken = truncationToken;
}

// 收起文本
- (void)changeFoldStatus {
    self.model.descUnFold = NO;
    if (self.refreshBlock) {
        self.refreshBlock();
    }
}

// UI控件初始化代码...
@end
```

#### Controller层
```objc
@interface FMF10ConceptThemeViewController ()<UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray *dataArr;

@end

@implementation FMF10ConceptThemeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    [self loadData];
}

- (void)setupUI {
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
}

#pragma mark - UITableView
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMF10ConceptThemeCell *cell = [tableView reuseCellClass:[FMF10ConceptThemeCell class]];
    
    cell.model = self.dataArr[indexPath.row];
    __weak typeof(self) weakSelf = self;
    cell.refreshBlock = ^{
        [weakSelf.tableView reloadData];
    };
    
    return cell;
}

// 其他必要的UITableView代理方法...
```

### 注意事项
- 使用YYLabel的truncationToken添加展开按钮比使用自定义UIButton更加灵活
- 在模型中保存展开状态，确保列表滚动时能保持正确的展开/折叠状态
- 展开和收起操作需要触发列表的重新加载，可以通过Block回调到控制器
- 计算文本行数可以帮助判断是否需要展示收起按钮
- 更新约束时注意处理收起按钮的显隐状态

### 版本记录
| 版本 | 日期 | 修改说明 |
|-----|------|---------|
| 1.0 | 2023-10-01 | 初始版本 |

