# 多页面切换

### UI 设计图

<img src="images/SGPaging使用 1.png" alt="多页面切换" style="zoom:50%;" />

### 页面结构分析

在"自选资讯"页面，有一个 tab来控制切换"公告"和"研报"，这种页面通常使用两个子控制器。tab 栏是常见的多组文字，文字下方带指示条样式，一般使用 SGPagingView来展示。

### 组件说明

- **SGPageTitleView**: 顶部标签栏，用于显示和切换不同标签
- **SGPageContentCollectionView**: 内容滑动区域，配合标签栏实现左右滑动切换
- **UIViewController**: 作为子控制器，每个标签对应一个控制器

### 代码示例

#### Controller层
```objc
@interface FMOptionalNewsController () <SGPageContentCollectionViewDelegate, SGPageTitleViewDelegate>

@property (nonatomic, strong) NSArray *titleArray;
@property (nonatomic, strong) SGPageTitleView *pageTitleView;
@property (nonatomic, strong) SGPageContentCollectionView *pageContentCollectionView;

@end

@implementation FMOptionalNewsController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"自选资讯";
    self.view.backgroundColor = FMWhiteColor;
    
    [self setupPageView];
}

#pragma mark - SGPageTitleViewDelegate
- (void)pageTitleView:(SGPageTitleView *)pageTitleView selectedIndex:(NSInteger)selectedIndex{
    [self.pageContentCollectionView setPageContentCollectionViewCurrentIndex:selectedIndex];
}

#pragma mark - SGPageContentCollectionViewDelegate
- (void)pageContentCollectionView:(SGPageContentCollectionView *)pageContentCollectionView progress:(CGFloat)progress originalIndex:(NSInteger)originalIndex targetIndex:(NSInteger)targetIndex {
    [self.pageTitleView setPageTitleViewWithProgress:progress originalIndex:originalIndex targetIndex:targetIndex];
}

#pragma mark - Private
- (NSArray *)addChildVC {
    FMOptionalAnnouncementViewController *vc1 = [FMOptionalAnnouncementViewController new];
    [self addChildViewController:vc1];
    
    FMOptionalReportViewController *vc2 = [FMOptionalReportViewController new];
    [self addChildViewController:vc2];
    
    return self.childViewControllers;
}

-(void)setupPageView {
    [self.pageTitleView removeFromSuperview];
    [self.pageContentCollectionView removeFromSuperview];
    self.pageTitleView = nil;
    self.pageContentCollectionView = nil;

    SGPageTitleViewConfigure *configure = [SGPageTitleViewConfigure pageTitleViewConfigure];
    configure.titleColor = ColorWithHex(0x666666);
    configure.titleFont = FontWithSize(16.0);
    configure.titleSelectedColor = ColorWithHex(0x333333);
    configure.titleSelectedFont = BoldFontWithSize(18.0);
    configure.indicatorStyle = SGIndicatorStyleFixed;
    configure.indicatorColor = FMNavColor;
    configure.indicatorFixedWidth = 18;
    configure.indicatorHeight = 3;
    configure.indicatorCornerRadius = 1.5;
    configure.titleAdditionalWidth = 30;
    configure.equivalence = YES;
    configure.showBottomSeparator = YES;
    self.pageTitleView = [SGPageTitleView pageTitleViewWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 45) delegate:self titleNames:self.titleArray configure:configure];
    self.pageTitleView.backgroundColor = FMWhiteColor;
    [self.view addSubview:self.pageTitleView];
    self.pageContentCollectionView = [[SGPageContentCollectionView alloc] initWithFrame:CGRectMake(0, 45, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT-(UI_SAFEAREA_TOP_HEIGHT + 45 + UI_SAFEAREA_BOTTOM_HEIGHT)) parentVC:self childVCs:[self addChildVC]];
    self.pageContentCollectionView.delegatePageContentCollectionView = self;
    [self.view addSubview:self.pageContentCollectionView];
    // 处理侧滑返回失效
    [self.pageContentCollectionView.collectionView.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
}

- (NSArray *)titleArray {
    if (_titleArray == nil) {
        _titleArray = @[@"公告", @"研报"];
    }
    return _titleArray;
}

@end
```

### 其他样式示例

<img src="images/SGPaging使用 2.jpg" alt="SGPaging效果 2" style="zoom:80%;" />

Tab切换还有一种左至右依次按自身宽度排列的样式，可能只有1、2个tab，也可能有很多个。实现代码如下：

```objc
- (void)setupPageView {
    [self.pageTitleView removeFromSuperview];
    self.pageTitleView = nil;

    SGPageTitleViewConfigure *configure = [SGPageTitleViewConfigure pageTitleViewConfigure];
    configure.titleColor = ColorWithHex(0x666666);
    configure.titleFont = FontWithSize(16);
    configure.titleSelectedColor = ColorWithHex(0x333333);
    configure.titleSelectedFont = BoldFontWithSize(18);
    configure.indicatorStyle = SGIndicatorStyleFixed;
    configure.indicatorColor = FMNavColor;
    configure.indicatorFixedWidth = 18;
    configure.indicatorHeight = 3;
    configure.indicatorCornerRadius = 1.5;
    configure.titleAdditionalWidth = 30;
    configure.equivalence = NO; // 这里设置为NO，不等宽排列
    configure.showBottomSeparator = NO;
    self.pageTitleView = [SGPageTitleView pageTitleViewWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 0) delegate:self titleNames:self.titleArray configure:configure];
    self.pageTitleView.backgroundColor = FMWhiteColor;
    
    [self.selfStockGroupsView addSubview:self.pageTitleView];
    [self.pageTitleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(0);
        make.left.equalTo(8);
        make.top.equalTo(7.5);
        make.right.equalTo(-70);
    }];
}
```

### 注意事项
- 使用SGPagingView需要正确配置代理方法，确保标签切换和页面滑动的同步
- 处理侧滑返回手势与水平滑动页面手势的冲突
- 配置合适的指示器样式和标签字体，保持UI一致性
- 根据需求选择等宽或不等宽的标签排列方式

### 版本记录
| 版本 | 日期 | 修改说明 |
|-----|------|---------|
| 1.0 | 2023-10-01 | 初始版本 |
