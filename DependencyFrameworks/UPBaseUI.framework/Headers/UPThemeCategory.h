//
//  UPThemeCategory.h
//  UPBaseUI
//
//  Created by caoxk on 2022/8/5.
//

#import <Foundation/Foundation.h>
#import "UPThemeDefines.h"

NS_ASSUME_NONNULL_BEGIN

typedef void(^UPfontModeDidChangeBlock)(void);

//MARK: - UPTheme Category

@interface UIView (UPThemePublic)

/// 支持的字体放大模式
/// 优先级直接影响子类,如果父类关闭则子类开启也无效
/// 支持多种时使用或运算:UPFontModeGloabl | UPFontModeModuleHq
@property (assign, nonatomic) UPFontMode supportedFontMode;

/// fontMode更新完成,在这里做一些适配操作,用于系统控件
@property (copy, nonatomic) UPfontModeDidChangeBlock fontModeDidChangeBlock;

/// 将自身的fontMode强制应用到未设置fontMode的控件中
/// 优先级：手动设置fontMode > force > 默认（UPFontModeNormal）
/// 一次性设置,暂不支持forceDeliver之后又还原
- (void)forceDeliverFontModeToSubviews;

/// fontMode更新完成,在这里做一些适配操作,用于自定义View
/// 需要调用super,默认实现为调用fontModeDidChangeBlock
- (void)fontModeDidChange;

@end

@interface UILabel (UPThemePublic)

/// 精确指定放大模式下的字体,以满足一些需要微调的Label
@property (strong, nonatomic) UIFont *exactScaleFont;

/// 大字版是否自动调整width约束,默认NO,会在显示不全时调整约束
@property (assign, nonatomic) BOOL fixWidthWhenScaled;

/// 大字版是否自动调整height约束,默认NO,会在显示不全时调整约束
@property (assign, nonatomic) BOOL fixHeightWhenScaled;

@end

@interface CALayer (UPThemePublic)

@property (nonatomic, assign) UPFontMode supportedFontMode;

@end

/** ------------------ old ------------------**/

@interface CALayer (UPFontScale)

@property (nonatomic, assign) BOOL supportScale;

@end

@interface UILabel (UPFontScale)

+ (instancetype)labelWithSupportScale;

@end

typedef void(^UPSupportScaleBlock)(BOOL supportScale, UIView *view);

@interface UIView (UPFontScale)

+ (instancetype)viewWithSupportScaleBlock:(UPSupportScaleBlock)block;

@property (nonatomic, copy) UPSupportScaleBlock up_scaleBlock;

@property (nonatomic, assign) BOOL supportScale;

@end

NS_ASSUME_NONNULL_END
