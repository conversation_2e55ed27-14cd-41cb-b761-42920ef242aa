//
//  UPThemeResourceMacro.h
//  UPBaseUI
//
//  Created by sa<PERSON><PERSON> on 2019/1/4.
//  Copyright © 2019 UpChina. All rights reserved.
//

#ifndef UPThemeResourceMacro_h
#define UPThemeResourceMacro_h

#import <UPBaseUI/UPBundleResourceMacro.h>

#import <UPBaseUI/UPThemeColor.h>
#import <UPBaseUI/UPThemeImage.h>

#import <UPBaseUI/UPThemeManager.h>

// MARK: Color

#define UPTColor(name) ((UIColor *)[UPThemeColor colorWithM:(UPR_SELF_BUNDLE) n:(name)])

#define UPTColorInModule(name, module) ((UIColor *)[UPThemeColor colorWithM:(UPR_BUNDLE(module)) n:(name)])

// MARK: Image

#define UPTImg(name) ((UIImage *)[UPThemeImage imageWithM:(UPR_SELF_BUNDLE) n:(name) c:YES])

#define UPTImgInModule(name, module) ((UIImage *)[UPThemeImage imageWithM:(UPR_BUNDLE(module)) n:(name) c:YES])

#define UPTImgNoCache(name) ((UIImage *)[UPThemeImage imageWithM:(UPR_SELF_BUNDLE) n:(name) c:NO])

#define UPTImgNoCacheInModule(name, module) ((UIImage *)[UPThemeImage imageWithM:(UPR_BUNDLE(module)) n:(name) c:NO])

#endif /* UPThemeResourceMacro_h */
