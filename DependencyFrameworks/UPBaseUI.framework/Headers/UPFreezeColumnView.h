//
//  UPFreezeColumnView.h
//  UPBaseUI
//
//  Created by sammy<PERSON> on 2020/3/22.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class UPFreezeColumnView;

// MARK: UPFreezeColumnViewColumn

@interface UPFreezeColumnViewColumn : NSObject

@property(nonatomic, copy) NSString * name;
@property(nonatomic, assign) CGFloat width;
@property(nonatomic, assign) NSInteger tag;
@property(nonatomic, assign) BOOL freeze;
@property(nonatomic, assign) BOOL isLastColumn;

+ (instancetype)columnWithN:(NSString *)name w:(CGFloat)width t:(NSInteger)tag;

+ (instancetype)freezeColumnWithN:(NSString *)name w:(CGFloat)width t:(NSInteger)tag;

@end

// MARK: UPFreezeColumnViewCell

@interface UPFreezeColumnViewCell : UICollectionViewCell

@property(nonatomic, assign) BOOL isLastColumn;

@end

// MARK: UPFreezeColumnViewDelegate

@protocol UPFreezeColumnViewDelegate <NSObject>
@optional

- (NSInteger)numberOfRowsInFreezeColumnView:(UPFreezeColumnView *)freezeColumnView;

- (NSArray<UPFreezeColumnViewColumn *> *)columnsInFreezeColumnView:(UPFreezeColumnView *)freezeColumnView;

// Class必须继承自UPFreezeColumnViewCell
- (Class)freezeColumnView:(UPFreezeColumnView *)freezeColumnView
       headerCellClassFor:(UPFreezeColumnViewColumn *)column;

// Class必须继承自UPFreezeColumnViewCell
- (Class)freezeColumnView:(UPFreezeColumnView *)freezeColumnView
         dataCellClassFor:(UPFreezeColumnViewColumn *)column;

- (void)freezeColumnView:(UPFreezeColumnView *)freezeColumnView
        configHeaderCell:(UPFreezeColumnViewCell *)columnCell
                  column:(UPFreezeColumnViewColumn *)column;

- (void)freezeColumnView:(UPFreezeColumnView *)freezeColumnView
          configDataCell:(UPFreezeColumnViewCell *)dataCell
                  column:(UPFreezeColumnViewColumn *)column
              indexOfRow:(NSInteger)indexOfRow;

- (void)freezeColumnView:(UPFreezeColumnView *)freezeColumnView
     visibleRowDidChange:(NSRange)visibleRowRange;

- (void)freezeColumnView:(UPFreezeColumnView *)freezeColumnView
         headerDidSelect:(UPFreezeColumnViewColumn *)column;

- (void)freezeColumnView:(UPFreezeColumnView *)freezeColumnView
            rowDidSelect:(NSInteger)indexOfRow;

- (void)freezeColumnView:(UPFreezeColumnView *)freezeColumnView
               didScroll:(CGPoint)offset;

@end

@interface UPFreezeColumnView : UIView

@property(nonatomic, weak) id<UPFreezeColumnViewDelegate> delegate;

@property(nonatomic, strong, readonly) UIScrollView * scrollView;

@property(nonatomic, assign) CGPoint contentOffset;
@property(nonatomic, assign) CGSize contentSize;

@property(nonatomic, assign) BOOL showsScrollIndicator;

@property(nonatomic, assign) CGFloat headerHeight; // 表头高度
@property(nonatomic, assign) CGFloat rowHeight; // 行高度

@property(nonatomic, strong) UIColor * listBackgroundColor; // collectionView背景色

@property(nonatomic, strong) UIColor * rowBackgroundColor; // 常态背景色
@property(nonatomic, strong) UIColor * rowBackgroundColorSelected; // 选中背景色

@property(nonatomic, strong) UIColor * separatorColor; // 分割线颜色
@property(nonatomic, assign) UIEdgeInsets separatorInset; // 分割线位置
@property(nonatomic, assign) CGFloat separatorHeight; // 分割线高度
@property(nonatomic, assign) BOOL showsHeaderSeparator; // header下面是是否显示分割线, 默认NO
@property(nonatomic, assign) BOOL showsLastRowSeparator; // 最后一行是否显示分割线, 默认NO

- (void)reloadData;

@end

NS_ASSUME_NONNULL_END
