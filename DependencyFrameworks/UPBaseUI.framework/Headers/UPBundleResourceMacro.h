//
//  UPBundleResourceMacro.h
//  UPBaseUI
//
//  Created by sa<PERSON><PERSON> on 2019/1/4.
//  Copyright © 2019 UpChina. All rights reserved.
//

#ifndef UPBundleResourceMacro_h
#define UPBundleResourceMacro_h

#import <UPBaseUI/UPBundleResource.h>

// MARK: - Module name macro

#define UPR_BUNDLE_SUFFIX "Resource"

#define _UPR_STRING(x) #x
#define UPR_STRING(x) _UPR_STRING(x)

#define UPR_BUNDLE(name) name UPR_BUNDLE_SUFFIX

#ifdef UPR_MODULE
#define UPR_SELF_BUNDLE @UPR_STRING(UPR_MODULE)UPR_BUNDLE_SUFFIX
#else
#define UPR_SELF_BUNDLE nil
#endif

// MARK: - Helper macro

// MARK: String

#define UPRStr(name) ([UPBundleResource stringForName:(name) inBundle:(UPR_SELF_BUNDLE)])

#define UPRStrInModule(name, module) ([UPBundleResource stringForName:(name) inBundle:(UPR_BUNDLE(module))])

// MARK: Color

#define UPRColor(name) ([UPBundleResource colorForName:(name) inBundle:(UPR_SELF_BUNDLE)])

#define UPRColorInModule(name, module) ([UPBundleResource colorForName:(name) inBundle:(UPR_BUNDLE(module))])

// MARK: Data

#define UPRData(name) ([UPBundleResource dataForName:(name) inBundle:(UPR_SELF_BUNDLE) cacheable:YES])

#define UPRDataInModule(name, module) ([UPBundleResource dataForName:(name) inBundle:(UPR_BUNDLE(module)) cacheable:YES])

#define UPRDataNoCache(name) ([UPBundleResource dataForName:(name) inBundle:(UPR_SELF_BUNDLE) cacheable:NO])

#define UPRDataNoCacheInModule(name, module) ([UPBundleResource dataForName:(name) inBundle:(UPR_BUNDLE(module)) cacheable:NO])

// MARK: Dictionary

#define UPRDict(name) ([UPBundleResource dictionaryForName:(name) inBundle:(UPR_SELF_BUNDLE) cacheable:YES])

#define UPRDictInModule(name, module) ([UPBundleResource dictionaryForName:(name) inBundle:(UPR_BUNDLE(module)) cacheable:YES])

#define UPRDictNoCache(name) ([UPBundleResource dictionaryForName:(name) inBundle:(UPR_SELF_BUNDLE) cacheable:NO])

#define UPRDictNoCacheInModule(name, module) ([UPBundleResource dictionaryForName:(name) inBundle:(UPR_BUNDLE(module)) cacheable:NO])

// MARK: Array

#define UPRArray(name) ([UPBundleResource arrayForName:(name) inBundle:(UPR_SELF_BUNDLE) cacheable:YES])

#define UPRArrayInModule(name, module) ([UPBundleResource arrayForName:(name) inBundle:(UPR_BUNDLE(module)) cacheable:YES])

#define UPRArrayNoCache(name) ([UPBundleResource arrayForName:(name) inBundle:(UPR_SELF_BUNDLE) cacheable:NO])

#define UPRArrayNoCacheInModule(name, module) ([UPBundleResource arrayForName:(name) inBundle:(UPR_BUNDLE(module)) cacheable:NO])

// MARK: JSONObject

#define UPRJSONObject(name) ([UPBundleResource jsonObjectForName:(name) inBundle:(UPR_SELF_BUNDLE) cacheable:YES])

#define UPRJSONObjectInModule(name, module) ([UPBundleResource jsonObjectForName:(name) inBundle:(UPR_BUNDLE(module)) cacheable:YES])

#define UPRJSONObjectNoCache(name) ([UPBundleResource jsonObjectForName:(name) inBundle:(UPR_SELF_BUNDLE) cacheable:NO])

#define UPRJSONObjectNoCacheInModule(name, module) ([UPBundleResource jsonObjectForName:(name) inBundle:(UPR_BUNDLE(module)) cacheable:NO])

// MARK: JSONArray

#define UPRJSONArray(name) ([UPBundleResource jsonArrayForName:(name) inBundle:(UPR_SELF_BUNDLE) cacheable:YES])

#define UPRJSONArrayInModule(name, module) ([UPBundleResource jsonArrayForName:(name) inBundle:(UPR_BUNDLE(module)) cacheable:YES])

#define UPRJSONArrayNoCache(name) ([UPBundleResource jsonArrayForName:(name) inBundle:(UPR_SELF_BUNDLE) cacheable:NO])

#define UPRJSONArrayNoCacheInModule(name, module) ([UPBundleResource jsonArrayForName:(name) inBundle:(UPR_BUNDLE(module)) cacheable:NO])

#endif /* UPBundleResourceMacro_h */
