//
//  UPThemeManager.h
//  UPBaseUI
//
//  Created by sammy<PERSON> on 2020/3/25.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPThemeDefines.h"

NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXPORT NSString * const kUPThemeDidChangeNotification;
FOUNDATION_EXPORT NSString * const kUPFontModeDidChangeNotification;


@interface UPThemeManager : NSObject

/// 判断当前是否为黑色主题，潜规则，就是判断是不是"Dark"
+ (BOOL)isDarkTheme;

/// 改变主题
/// @param themeName 主题名("Dark"或nil)
+ (void)changeTheme:(nullable NSString *)themeName;

/// 获取当前字体放大模式
+ (UPFontMode)currentFontMode;

/// 当前是否处于正常模式(未开启任何一种字体放大模式)
+ (BOOL)isNormalFontMode;

/// 字体放大,默认精准模式,delta=4
/// @param mode 字体放大模式
+ (void)changeFontMode:(UPFontMode)mode;

/// 比例放大
/// @param mode 字体放大模式
/// @param factor 放大的比例
+ (void)changeFontMode:(UPFontMode)mode factor:(CGFloat)factor;

/// 精准放大
/// @param mode 字体放大模式
/// @param delta 需要扩大的字号
+ (void)changeFontMode:(UPFontMode)mode delta:(CGFloat)delta;

/// 获取字体放大模式时的因子
/// 正常模式时返回0
+ (CGFloat)getCurrentFontModeFactor;

/// 获取字体放大模式时的增量
/// 正常模式时返回0
+ (CGFloat)getCurrentFontModeDelta;

/// 判断控件是否支持当前的字体放大模式
/// 默认都支持全局模式
/// 只支持特定模块时,需要自行设置global+module
/// @param mode 控件支持的模式
+ (BOOL)isWidgetSupportCurrentMode:(UPFontMode)mode;

/// ![UPThemeManager isNormalFontMode] && [UPThemeManager isWidgetSupportCurrentMode:view.supportedFontMode];
/// 注意:如果是非UPFontModeGlobal,该判断可能失效,此时应该将相关逻辑写到fontModeDidChange中。
/// 比如行情大字版不是默认支持的,所以需要在被addSubview之后才会更新模块化控制的逻辑
/// @param widget 控件
+ (BOOL)isWidgetShouldScale:(UIView *)widget;

/// 可以直接通过该方法获取到被放大后的字体
/// @param font 原字体
+ (UIFont *)transFontWithCurrentFont:(UIFont *)font;

@end

NS_ASSUME_NONNULL_END
