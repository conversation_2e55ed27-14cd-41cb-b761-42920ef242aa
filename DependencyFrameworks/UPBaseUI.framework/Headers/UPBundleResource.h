//
//  UPBundleResource.h
//  UPBaseUI
//
//  Created by sa<PERSON><PERSON> on 2018/12/29.
//  Copyright © 2018 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>

#import <UPTAF/TAFJSONObject.h>

/**
 * 从Bundle里面获取各种资源
 *
 * 1.支持缓存, 字符串和颜色默认缓存
 * 2.支持国际化
 * 3.支持资源覆盖
 * 4.支持基本的资源匹配策略
 */
@interface UPBundleResource : NSObject

+(NSString *)stringForName:(NSString *)name inBundle:(NSString *)bundleName;

+(UIColor *)colorForName:(NSString *)name inBundle:(NSString *)bundleName;

+(UIImage *)imageForName:(NSString *)name inBundle:(NSString *)bundleName cacheable:(BOOL)cacheable;

+(NSData *)dataForName:(NSString *)name inBundle:(NSString *)bundleName cacheable:(BOOL)cacheable;

+(NSDictionary *)dictionaryForName:(NSString *)name inBundle:(NSString *)bundleName cacheable:(BOOL)cacheable;

+(NSArray *)arrayForName:(NSString *)name inBundle:(NSString *)bundleName cacheable:(BOOL)cacheable;

+(UPTAFJSONObject *)jsonObjectForName:(NSString *)name inBundle:(NSString *)bundleName cacheable:(BOOL)cacheable;

+(UPTAFJSONArray *)jsonArrayForName:(NSString *)name inBundle:(NSString *)bundleName cacheable:(BOOL)cacheable;

+(NSString *)overrideSuffix;

+(void)setOverrideSuffix:(NSString *)suffix;

+(void)clearCache;

+(void)setDebug:(BOOL)debuggable;

@end
