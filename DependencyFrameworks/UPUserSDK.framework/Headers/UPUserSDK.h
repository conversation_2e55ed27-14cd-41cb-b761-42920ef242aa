//
//  UPUserSDK.h
//  UPUserSDK
//
//  Created by <PERSON><PERSON> on 2017/4/6.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import <UIKit/UIKit.h>

// UserSpace
#import <UPUserSDK/UPUserManager.h>
#import <UPUserSDK/UPUserManagerDefine.h>

// Define
#import <UPUserSDK/UPUserSDKDefine.h>

// Model

#import <UPUserSDK/UPUserModel.h>
#import <UPUserSDK/UPUserInfoModel.h>
#import <UPUserSDK/UPUserPrivilegeModel.h>
#import <UPUserSDK/UPUserOAuthModel.h>
#import <UPUserSDK/UPUserTokenModel.h>


//! Project version number for UPUserSDK.
FOUNDATION_EXPORT double UPUserSDKVersionNumber;

//! Project version string for UPUserSDK.
FOUNDATION_EXPORT const unsigned char UPUserSDKVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <UPUserSDK/PublicHeader.h>


