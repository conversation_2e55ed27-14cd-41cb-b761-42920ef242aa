//
//  UPUserModel.h
//  UPBase
//
//  Created by <PERSON><PERSON> on 2017/3/23.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import "UPUserRiskAndContractModel.h"

@class CRMRight;

/*
 用户登录信息
 */
@interface UPUserModel : NSObject

@property(nonatomic, copy, readonly) NSString *uid;                    // 用户UID

@property(nonatomic, copy, readonly) NSString *cid;                    // 用户CID

@property(nonatomic, copy, readonly) NSString *hqrights;               // 权限

@property(nonatomic, copy, readonly) NSString *rd;                     // 密钥pwd字段（注意是MD5）加密的随机数,用于加密

@property(nonatomic, copy, readonly) NSString *loginTime;              // 登录时的系统时间戳

@property(nonatomic, copy, readonly) NSString *platform;               // 客户端类型

@property(nonatomic, copy, readonly) NSDictionary *rights;             // 用户权限  key:mid, value:end_date

@property(nonatomic, copy, readonly) NSString *token;                  // Token信息

@property(nonatomic, copy, readonly) NSString *refreshToken;           // 用来刷新用户登录态的接口

@property(nonatomic, assign, readonly) long long tokenRefreshTime;     // token上次刷新时间（本地时间戳）

@property(nonatomic, assign, readonly) long long tokenExpire;          // token有效期

@property(nonatomic, assign, readonly) long long refreshTokenExpire;   // refreshToken有效期

@property(nonatomic, copy, readonly) NSString *loginType;              // 登录类型

@property (nonatomic, strong, readonly) UPUserRiskAndContractModel *riskContractModel;  //风测合同信息

- (instancetype)initWithData:(NSString *)uid
                         cid:(NSString *)cid
                    hqRights:(NSString *)hqRights
                          rd:(NSString *)rd
                   loginTime:(NSString *)loginTime
                    platform:(NSString *)platform
                      rights:(NSDictionary *)rights
                       token:(NSString *)token
                refreshToken:(NSString *)refreshToken
            tokenRefreshTime:(long long)tokenRefreshTime
                 tokenExpire:(long long)tokenExpire
          refreshTokenExpire:(long long)refreshTokenExpire
                   loginType:(NSString *)loginType;

- (void)setRightsFromJson:(NSString *)rightString;

- (void)setRightsFromTaf:(NSArray <CRMRight *> *)stRights;

- (void)updateTokenInfo:(NSString *)token
       tokenRefreshTime:(long long)tokenRefreshTime
            tokenExpire:(long long)tokenExpire
     refreshTokenExpire:(long long)refreshTokenExpire;

- (void)updateHqRights:(NSString *)hqRights rd:(NSString *)rd;

- (void)updateRiskContractModel:(UPUserRiskAndContractModel *)model;

- (BOOL)obtainRight:(NSString *)rightId;

- (BOOL)obtainExpireRight:(NSString *)rightId;

- (long)getRightDays:(NSString *)rightId;

- (long long)getRightExpireDate:(NSString *)rightId;

@end
