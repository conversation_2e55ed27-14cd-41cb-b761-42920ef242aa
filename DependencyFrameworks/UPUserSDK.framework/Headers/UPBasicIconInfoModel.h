//
//  UPBasicIconInfoModel.h
//  UPUserSDK
//
//  Created by parkerxu on 2019/3/28.
//  Copyright © 2019年 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPBasicIconInfoModel : NSObject

@property (nonatomic, copy) NSString *iconPicName;  // 本地icon名称

@property (nonatomic, copy) NSString *iconTitle;    // icon 标题

@property (nonatomic, strong) NSString *imageURL;   // 图片url

@property (nonatomic, copy) NSString *targetUrl;    // 跳转地址

@property (nonatomic, copy) NSString *markUrl;     //角标图片地址

@property (nonatomic, copy) NSString *iconDescription;     //给优投顾使用的字段，对图标的描述

@end

NS_ASSUME_NONNULL_END
