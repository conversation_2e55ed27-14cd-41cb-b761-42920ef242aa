#import <Foundation/Foundation.h>


@interface UPOptionalGroupModel : NSObject <NSCopying>

/*  用户ID */
@property (nonatomic, copy) NSString *uid;
/* 分组id */
@property (nonatomic, assign) NSInteger groupId;
/* 分组名称 */
@property (nonatomic, copy) NSString *groupName;
/* 位置 */
@property (nonatomic, copy) NSString *position;
/* 1:未删除;0:删除 */
@property (nonatomic, assign) NSInteger status;
/* 删除时间 */
@property (nonatomic, assign) long long delete_time;
/* 修改时间(排序修改，自选股重新分组) */
@property (nonatomic, assign) long long update_time;
/* 增加时间 */
@property (nonatomic, assign) long long create_time;

@end
