//
// Created by j<PERSON><PERSON> on 2019/11/8.
// Copyright (c) 2019 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>


@interface UPOptionalConfig : NSObject

@property(nonatomic, assign) BOOL touristSyncEnable; // 游客自选同步开关
@property(nonatomic, assign) NSInteger startGroupId; // 自选分组ID区间开始
@property(nonatomic, assign) NSInteger endGroupId; // 自选分组ID区间结束
@property(nonatomic, assign) NSInteger maxGroupCount; // 最大分组数量

@end
