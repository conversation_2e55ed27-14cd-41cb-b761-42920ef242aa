//
//  UPUserRemarkResult.h
//  UPUserSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/4/21.
//  Copyright © 2021 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPUserRemarkInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface UPUserRemarkResult : NSObject

@property (nonatomic, strong) NSArray<UPUserRemarkInfo *> * remarkList;

@property (nonatomic, assign) NSInteger total;

@property (nonatomic, assign) long long time;                              // 毫秒时间戳

+ (instancetype)initWithRemarkScodesResult:(NSArray<PStockRemark *> *)remarks time:(long long)time;


@end

NS_ASSUME_NONNULL_END
