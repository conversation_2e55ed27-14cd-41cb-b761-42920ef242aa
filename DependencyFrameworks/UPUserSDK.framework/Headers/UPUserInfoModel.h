//
//  UPUserInfoModel.h
//  UPBase
//
//  Created by <PERSON><PERSON> on 2017/3/22.
//  Copyright © 2017年 UpChina. All rights reserved.
//

@class UPOpenInfo;
@class CRMOpenInfo;

@interface UPBusinessChannel : NSObject
@property (nonatomic, copy) NSString* business;
@property (nonatomic, copy) NSString* channel;
@end

/*
 用户个人信息model
 */
@interface UPUserInfoModel : NSObject <NSCopying>

@property(nonatomic, copy) NSString *cid;         // 用户cid

@property(nonatomic, copy) NSString *uid;         // 用户名

@property(nonatomic, copy) NSString *nickName;    // 昵称

@property(nonatomic, copy) NSString *sex;         // 性别（1-男，0-女）

@property(nonatomic, copy) NSString *phone;       // 手机（加密后）

@property(nonatomic, copy) NSString *email;       // 邮件（加密后）

@property(nonatomic, copy) NSString *birthday;    // 生日

@property(nonatomic, copy) NSString *stockAge;    // 股龄：1-1年一下，3-1~3年，5-3~5年，7-5年以上

@property(nonatomic, copy) NSString *investType;  // 投资品种，格式例如:A股、港股；中间用“、”隔开

@property(nonatomic, copy) NSString *province;    // 省份

@property(nonatomic, copy) NSString *city;        // 城市

@property(nonatomic, copy) NSString *remarks;     // 个人说明

@property(nonatomic, copy) NSString *headPic;     // 头像

@property(nonatomic, assign) int regTime;         // 注册时间, 单位秒
@property(nonatomic, copy) NSString *channel;         // 注册渠道

@property(nonatomic, strong) NSArray *openInfos;  // 三方绑定信息

@property(nonatomic, copy) NSString *oldOpenId;         // 旧qq

@property(nonatomic, strong) NSArray<UPBusinessChannel *> *businessChannels;  // 渠道列表

- (void)setOpenInfosFromTaf:(NSArray<CRMOpenInfo *> *)stOpenInfos;

-(UPOpenInfo *)getWeixinInfo;

-(UPOpenInfo *)getQQInfo;

-(UPOpenInfo *)getWBInfo;
@end
