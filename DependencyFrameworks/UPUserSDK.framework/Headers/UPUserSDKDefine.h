//
//  UPUserSDKDefine.h
//  UPUserSDK
//
//  Created by <PERSON><PERSON> on 2017/8/9.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#ifndef UPUserSDKDefine_h
#define UPUserSDKDefine_h

typedef NSUInteger UPUserClientType;

NS_ENUM(UPUserClientType) {
    UPUserClientType_PC = 1,
    UPUserClientType_APP = 2,
    UPUserClientType_Web = 3,
    UPUserClientType_H5 = 4,
    UPUserClientType_TEACH_APP = 5,
    UPUserClientType_ADVISOR_APP = 6,
    UPUserClientType_Other = 99
    };

typedef NSUInteger UPUserOrderClientType;
NS_ENUM(UPUserOrderClientType) {
    UPUserOrderClientType_GPT_APP = 1,
    UPUserOrderClientType_GPT_PC = 2,
    UPUserOrderClientType_GPT_JZB = 3,
    UPUserOrderClientType_YTG_APP = 4,
    UPUserOrderClientType_GDK_APP = 5
    };
    
typedef NSUInteger UPUserOrderStatus;
NS_ENUM(UPUserOrderStatus) {
    UPUserOrderStatus_newOrder = 180,
    UPUserOrderStatus_opened = 220,
    UPUserOrderStatus_canceled = 70,
    UPUserOrderStatus_inactiveOrder = 80,
    UPUserOrderStatus_refunded = 90
    };

#define kUPRiskSuccess 0         //已成功进行风测
#define kUPRiskFailure -1        //请求失败
#define kUPRiskServerError -103  //风测服务异常
#define kUPRiskNoSign -107       //用户未签名
#define kUPRiskExpire -109       //风测过期


// 用户登录成功
extern NSString *const UPNotifyUserDidLogin;

// 用户个人信息更新成功
extern NSString *const UPNotifyUserInfoUpdated;

// 用户退出登录
extern NSString *const UPNotifyUserDidLogout;

// 用户Token过期up
extern NSString *const UPNotifyUserTokenExpired;

// 用户Token改变
extern NSString *const UPNotifyUserTokenChanged;

// 自选数据发生改变
extern NSString *const UPNotifyOptionalDataUpdated;

// 自选同步结果通知
extern NSString *const UPNotifyOptionalSyncComplete;

#define ENV_SSO ![UPTAFManager isTestEnv]

/*
 * 我的 upchina pro 接口地址
 * */
#define UPURLMinePROAPI (NSString *)(ENV_SSO ? @"https://app.upchinaproduct.com/" : @"http://app.test.upchina.com/")

/*
 * 我的 SSO 接口地址
 * */
#define UPURLMineSSOAPI (NSString *)(ENV_SSO ? @"https://ssoapi.upchinaproduct.com/" : @"http://ssoapi.test.whup.com/")

/*
 * 我的 CRM 接口地址
 * */
#define UPURLMineCRMAPI (NSString *)(ENV_SSO ? @"https://r2.upchinaproduct.com/" : @"http://crm.test.whup.com/")

/*
 * 我的 特权 接口地址
 * */
#define UPURLMinePrivilegeAPI (NSString *)(ENV_SSO ? @"https://r2.upchinaproduct.com/" : @"http://r2.test.upchina.com/")

/*
 * 风测 接口地址
 * */
#define UPURLRiskTestAPI (NSString *)(ENV_SSO ? @"https://prx.upchina.com/json/crm_riskeval/GetRiskEvaluationResult" : @"https://prx.test.upchina.com/json/crm_riskeval/GetRiskEvaluationResult")

/*
 * 修改密码 接口地址
 * */
#define UPURLModifyPasswordAPI (NSString *)(ENV_SSO ? @"https://app.upchinaproduct.com/" : @"http://upos.test.whup.com/")

#define UPUserAdvisorAPI (NSString *)(ENV_SSO ? @"https://api.uptougu.com/" : @"http://etg-api.test.whup.com/")

#define UPUserWebLoginAPI (NSString *)(ENV_SSO ? @"https://r2.upchina.com/login/scgi/" : @"http://userweb.test.whup.com/login/scgi/")

#define UPUserOpenLoginAPI (NSString *)(ENV_SSO ? @"https://tlogin.upchina.com/" : @"http://tlogin.test.upchina.com/")

#endif /* UPUserSDKDefine_h */
