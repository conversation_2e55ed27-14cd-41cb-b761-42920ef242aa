//
//  UPUserRemarkInfo.h
//  UPUserSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/4/21.
//  Copyright © 2021 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
@class PStockRemark;

NS_ASSUME_NONNULL_BEGIN

@interface UPUserRemarkInfo : NSObject

@property (nonatomic, strong) NSString *remarkid;
@property (nonatomic, strong) NSString *remarkInfo;
@property (nonatomic, assign) long long createtime;
@property (nonatomic, assign) long long updatetime;
@property (nonatomic, strong) NSString *username;
@property (nonatomic, assign) int marketid;
@property (nonatomic, strong) NSString *scode;
@property (nonatomic, assign) int deleted;               //是否被删除 1代表被删除

+ (instancetype)initWithRemarkInfo:(PStockRemark *)remarkInfo;

@end

NS_ASSUME_NONNULL_END
