//
// Created by <PERSON><PERSON> on 2017/6/10.
// Copyright (c) 2017 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPOptionalModel.h"
#import "UPOptionalConfig.h"
#import "UPOptionalGroupModel.h"

typedef NS_ENUM(NSUInteger, UPOptionalSortDirection) {
    UPOptionalSortDirectionBefore,
    UPOptionalSortDirectionAfter,
    UPOptionalSortDirectionBetween
};

typedef NS_ENUM(NSUInteger, UPOptionalAddStatus) {
    UPOptionalAddStatusSuccess,     // 自选股添加成功
    UPOptionalAddStatusLimit,       // 自选股已达上限，添加失败
    UPOptionalAddStatusFail,        // 自选股添加失败
    UPOptionalAddStatusNameEmpty,       // 自选分组名称为空
    UPOptionalAddStatusGroupExist,  // 自选已经存在
};

typedef void (^optionalSyncHandler)(NSArray <UPOptionalModel *> *array);

typedef void (^addOptionalGroupHandler)(UPOptionalAddStatus status, NSInteger groupId);

typedef void(^UPCombineOptionalHandler)(UPOptionalAddStatus status);

typedef NSString *(^UPRefreshTokenHandler)(void);

@interface UPOptionalManager : NSObject

/*
 * 请求自选个数限制 加载缓存
 */
+ (void)start:(UPOptionalConfig *)config;

/*
 * 设置用户UID、token
 */
+ (void)setUID:(NSString *)uid token:(NSString *)token;

/*
 * 添加自选股 name code setCode category 必填
 * */
+ (UPOptionalAddStatus)addOptional:(UPOptionalModel *)model;

/*
 * 批量添加自选
 * */
+ (UPOptionalAddStatus)addOptionals:(NSArray *)modelArray;

/*
* 添加自选到多个分组，不在分组列表的这只自选删除
* */
+ (UPOptionalAddStatus)addOptional:(NSInteger)setCode code:(NSString *)code name:(NSString *)name groupIdList:(NSArray<NSNumber *> *)groupIdList;

/*
 * 批量删除自选
 * */
+ (BOOL)removeOptionalWithArray:(NSArray <UPOptionalModel *> *)array;

/*
 * 批量删除自选，删除所有分组的 同一只股票
 */
+ (BOOL)removeOptionalsFromAllGroupWithArray:(NSArray<UPOptionalModel *> *)array;

/*
 * 清楚未登录状态下的自选数据;
 * */
+ (BOOL)clearNoUserOptionalData;

/*
 所有非删除状态的默认分组自选数据

 @return array
 */
+ (NSArray <UPOptionalModel *> *)currentOptionalData;

/*
 所有非删除状态的自选数据

 @return array
 */
+ (NSArray <UPOptionalModel *> *)allOptionalData;

/*
 通过setCode，code在数据库中查找自选记录

 @param code 股票代码
 @param setCode 市场代码
 @return 自选记录
 */
+ (UPOptionalModel *)getOptionalWithCode:(NSString *)code setCode:(NSInteger)setCode;

/*
通过setCode，code groupId在数据库中查找自选记录

@param code 股票代码
@param setCode 市场代码
@return 自选记录
*/
+ (UPOptionalModel *)getOptionalWithCode:(NSString *)code setCode:(NSInteger)setCode groupId:(NSInteger)groupId;


/*
 自选股排序
 
 @param code 股票代码
 @param setCode 市场代码
 @param direction 排序方向
 @param aPosition 第一个position
 @param bPosition 第二个position 只有direction为UPOptionalSortDirectionBetween时才需要传
 @return positionString
 */
+ (NSString *)sortOptionalWithCode:(NSString *)code setCode:(NSInteger)setCode direction:(UPOptionalSortDirection)direction aPosition:(NSString *)aPosition bPosition:(NSString *)bPosition;

/*
 自选股排序
 
 @param code 股票代码
 @param setCode 市场代码
 @param groupId 自选分组
 @param direction 排序方向
 @param aPosition 第一个position
 @param bPosition 第二个position 只有direction为UPOptionalSortDirectionBetween时才需要传
 @return positionString
 */
+ (NSString *)sortOptionalWithCode:(NSString *)code setCode:(NSInteger)setCode groupId:(NSInteger)groupId direction:(UPOptionalSortDirection)direction aPosition:(NSString *)aPosition bPosition:(NSString *)bPosition;

/*
 自选股分组排序

 @param groupId 自选分组
 @param direction 排序方向
 @param aPosition 第一个position
 @param bPosition 第二个position 只有direction为UPOptionalSortDirectionBetween时才需要传
 @return positionString
 */
+ (NSString *)sortOptionalGroupWithGroupId:(NSInteger)groupId direction:(UPOptionalSortDirection)direction aPosition:(NSString *)aPosition bPosition:(NSString *)bPosition;

/*
 与服务器同步自选数据

 @param handler handler
 */
+ (void)syncOptional:(optionalSyncHandler)handler;

/*
 添加需要更新的自选数据 改数据会在调用syncOptional是提交给服务器

 @param array array
 */
+ (void)updateOptionalPosition:(NSArray <UPOptionalModel *> *)array;

/*
 * 更新自选 name category 字段
 * */
+ (void)updateOptionalInfo:(NSArray <UPOptionalModel *>*)array;

/*
 * 是否需要更新 name category 字段
 * */
+ (BOOL)needUpdateOptionalInfo;

/*
 * 是否需要合并自选股
 * */
+ (BOOL)needCombineOptional;


/*
所有非删除状态的指定分组自选数据 包括本地数据和服务器数据
@return array
*/
+ (NSArray<UPOptionalModel *> *)getCurrentOptionalDataByGroupId:(NSInteger)groupId;

/*
所有非删除状态的分组数据 包括本地数据和服务器数据
@return array
*/
+ (NSArray<UPOptionalGroupModel *> *)getCurrentOptionalGroupData;

/// 根据id获取分组数组
+ (UPOptionalGroupModel *)getOptionalGroudWithGroupId:(NSInteger)groupId;

/*
 通过setCode，code查找自选所在的分组列表

 @param code 股票代码
 @param setCode 市场代码
 @return 分组列表
 */
+ (NSArray<UPOptionalGroupModel *> *)getGroupListWithCode:(NSString *)code setCode:(NSInteger)setCode;

/*
添加自选分组
*/
+ (void)addOptionalGroup:(NSString *)groupName handler:(addOptionalGroupHandler)handler;

/*
删除自选分组
*/
+ (BOOL)removeOptionalGroupWithArray:(NSArray <UPOptionalGroupModel *> *)groupArray deleteOptionals:(BOOL)deleteOptionals;

/*
更新自选分组名称
*/
+ (UPOptionalAddStatus)updateOptionalGroupName:(UPOptionalGroupModel *)group;

/*
更新自选分组排序
*/
+ (BOOL)updateOptionalGroupPosition:(NSArray<UPOptionalGroupModel *> *)array;

/*
 * 合并自选股
 * @param handler handler
 * */
+ (void)combineOptional:(UPCombineOptionalHandler)handler;

+ (BOOL)saveAlarmWithSetCode:(NSInteger)setCode code:(NSString *)code uid:(NSString *)uid;

+ (BOOL)deleteAlarmWithSetCode:(NSInteger)setCode code:(NSString *)code uid:(NSString *)uid;

+ (NSDictionary <NSString *, UPOptionalModel *> *)getAlarmWithUid:(NSString *)uid;

@end
