//
//  UPUserManager.h
//  UPUserSDK
//
//  Created by Nic on 2017/6/6.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import "UPUserManagerDefine.h"

@class UPUserPrivilegeModel;
@class UPUserOAuthModel;
@class UPUserTokenModel;
@class UPUserModel;
@class UPUserInfoModel;
@class UPOpenInfo;

/**
 * 管理用户相关服务 (登录，注册，更换密码等)
 */
@interface UPUserManager : NSObject

/**
 * 用户uid
 */
+ (NSString *)uid;

/**
 * 用户昵称 如果用户有昵称返回昵称 否则返回uid
 */
+ (NSString *)nickName;

/**
 * 用户基础信息
 */
+ (UPUserModel *)baseInfo;

/**
 * 用户个人信息model
 */
+ (UPUserInfoModel *)detailInfo;

/**
 * 用户token，sign信息
 */
+ (UPUserTokenModel *)userToken;

/**
 * 用户登录态
 */
+ (BOOL)isUserLogin;

/**
 * 用户特权信息 用于我的页面展示
 */
+ (NSArray<UPUserPrivilegeModel *> *)privileges;

/**
 * 上次登录成功的用户id
 */
+ (NSString *)previousLoginUID;

/**
 * 是否是拥有权限
 */
+ (BOOL)hasUserRight:(NSString *)rightId;

/**
* 购买过但是已过期的权限
*/
+ (BOOL)hasExpiredUserRight:(NSString *)rightId;

/**
 * 获取权限天数
 */
+ (long)getPrivilegeDays:(NSString *)rightId;

/**
 * 获取权限结束时间,返回结束日期的时间戳
 */
+ (long long)getPrivilegeEndDate:(NSString *)rightId;

// MARK: - Network Request

/**
 * 是否曾经登录过
 */
+ (BOOL)hasLocalLoginInfo;

/**
 * 刷新用户Token
 * 错误码:
 * -1: Token过期
 */
+ (void)refreshUserToken:(UPUserCallback)callback;

/**
 * 自动登录
 */
+ (void)autoLogin:(NSInteger)clientType;

/**
 * 登录
 */
+ (void)loginWithUID:(NSString *)uid password:(NSString *)password handler:(userRequestHandler)handler;

/**
 * 登录
 */
+ (void)loginWithUID:(NSString *)uid password:(NSString *)password isMD5:(BOOL)isMD5 handler:(userRequestHandler)handler;

/**
 * 第三方登录
 */
+ (void)loginWithPlatformInfo:(UPUserOAuthModel *)oauthModel handler:(userRequestHandler)handler;

/**
 * 绑定三方账号
 */
+ (void)bindOpen:(UPUserOAuthModel *)oauthModel uid:(NSString *)uid handler:(userRequestHandler)handler;

/**
 * 解除三方账号绑定
 */
+ (void)unbindOpen:(UPUserOAuthModel *)oAuthModel uid:(NSString *)uid handler:(userRequestHandler)handler;

/**
 * 解除三方账号绑定 -  通过token
 */
+ (void)unbindOpenByCode:(NSString *)code codeId:(NSString *)codeId thirdPlat:(NSString*)thirdPlat handler:(userRequestHandler)handler;

/**
 * 请求用户个人信息
 */
+ (void)requestDetailInfo:(userRequestHandler)handler;

/**
 * 退出登录
 */
+ (void)logout:(userRequestHandler)handler;

/**
 * 修改用户头像
 */
+ (void)modifyHeadPic:(UIImage *)image callback:(UPUserCallback(NSString *))callback;

/**
 * 修改用户昵称
 */
+ (void)modifyUserNickname:(NSString *)name handler:(userRequestHandler)handler;

/**
 * 请求验证码
 * @param phone 手机号码, 已登录用户可以不传手机号码, 默认使用当前登录的手机号
 * @param callback 返回验证码对应的 CodeId
 *
 * 错误码:
 * -1: 入参错误，手机号非法，guid或者ip非法
 * -100: 服务端异常
 * 透传验证码服务如下错误码
 * -4: 验证码发送失败
 * -5: 手机号超出业务限制
 * -7: IP超出限制
 * -8：guid超出限制
 * -16: 短信验证码下发太快
 */
+ (void)requestSMSCode:(NSString *)phone callback:(UPUserCallback(NSString *))callback;

/**
 * 用于注册的获取验证码接口
 */
+ (void)requestRegisterCodeWithPhone:(NSString *)phoneNumber handler:(userRequestHandler)handler;

/**
 * 通过手机号注册
 */
+ (void)registerWithPhone:(NSString *)phone password:(NSString *)password handler:(userRequestHandler)handler;

/**
 * 验证验证码
 *
 * @param code 验证码
 * @param phoneNumber 电话号码
 * @param codeId 验证码对应ID
 *
 * 错误码:
 * -1: 验证码验证失败,未下发短信验证码
 * -2: 手机号有误
 * -3: 验证码有误
 * -6： 验证码验证超过限制次数
 * -9: IP变更，校验失败
 * -10: 用户唯一身份标识变更，校验失败
 * -14: 短信验证码校验失败
 * -100: 服务繁忙
 */
+ (void)verifyCode:(NSString *)code phoneNumber:(NSString *)phoneNumber codeId:(NSString *)codeId callback:(UPUserCallback)callback;

/**
 * 通过token验证验证码
 *
 * @param code 验证码
 * @param codeId 验证码对应ID

 */
+ (void)verifyCodeByToken:(NSString*)code codeId:(NSString *)codeId callback:(UPUserCallback)callback;
/**
 * 首次绑定手机号
 *
 * @param mobile 手机号
 * @param smsCode 手机验证码
 * @param codeId 手机验证码对应ID
 *
 * 错误码:
 * -1: 参数有误、手机号非法、短信验证码非法
 * -2: 用户已经绑定了手机号
 * -3: 验证码有误
 * -6: 手机号、邮箱等已经被其它用户注册
 * -9: IP变更，校验失败
 * -10: 用户唯一身份标识变更，校验失败
 * -14: 短信验证码校验失败
 * -100: 服务繁忙
 * -200: token无效，需要重新登陆
 */
+ (void)bindNewPhone:(NSString *)mobile smsCode:(NSString *)smsCode codeId:(NSString *)codeId callback:(UPUserCallback)callback;

/**
 * 解绑手机号
 *
 * @param smsCode 手机验证码
 * @param codeId 手机验证码对应ID
 *
 * 错误码:
 * -1: 参数有误、token无效，手机号非法、短信验证码非法、用户密码解密失败等
 * -2: 用户没有绑定手机号
 * -3: 验证码有误
 * -4: 用户密码错误
 * -6： 验证码验证超过限制次数
 * -9: IP变更，校验失败
 * -10: 用户唯一身份标识变更，校验失败
 * -14: 短信验证码校验失败
 * -100: 服务繁忙
 */
+ (void)unbindPhone:(NSString *)smsCode codeId:(NSString *)codeId callback:(UPUserCallback)callback;

/**
 * 修改绑定手机号-通过验证码
 *
 * @param oldCode 旧手机验证码
 * @param oldCodeId 旧手机验证码对应ID
 * @param newMobile 新手机号
 * @param newCode 新手机号验证码
 * @param newCodeId 新手机号验证码对应ID
 *
 * 错误码:
 * -1: 参数有误、token无效，手机号非法、短信验证码非法
 * -2: 用户没有绑定手机号
 * -3: 验证码有误
 * -4: 密码错误
 * -6: 手机号、邮箱等已经被其它用户注册
 * -9: IP变更，校验失败
 * -10: 用户唯一身份标识变更，校验失败
 * -14: 短信验证码校验失败
 * -100: 服务繁忙
 */
+ (void)modifyBindPhone:(NSString *)oldCode oldCodeId:(NSString *)oldCodeId newMobile:(NSString *)newMobile newCode:(NSString *)newCode newCodeId:(NSString *)newCodeId callback:(UPUserCallback)callback;

/**
 * 修改绑定手机号-通过密码
 *
 * @param password 用户密码
 * @param newMobile 新手机号
 * @param newCode 新手机号验证码
 * @param newCodeId 新手机号验证码对应ID
 *
 * 错误码:
 * -1: 参数有误、token无效，手机号非法、短信验证码非法
 * -2: 用户没有绑定手机号
 * -3: 验证码有误
 * -4: 密码错误
 * -6： 验证码验证超过限制次数
 * -9: IP变更，校验失败
 * -10: 用户唯一身份标识变更，校验失败
 * -14: 短信验证码校验失败
 * -100: 服务繁忙
 */
+ (void)modifyBindPhoneByPass:(NSString *)password newMobile:(NSString *)newMobile newCode:(NSString *)newCode newCodeId:(NSString *)newCodeId callback:(UPUserCallback)callback;

/**
 * 获取用户已绑定的手机号(后4位)
 */
+ (void)requestUserBindPhoneWithUid:(NSString *)uid handler:(userRequestHandler)handler;

/**
 * 重置用户密码
 */
+ (void)resetPassword:(NSString *)password phone:(NSString *)phone code:(NSString *)code codeId:(NSString *)codeId handler:(userRequestHandler)handler;

/**
 * 修改用户密码
 */
+ (void)modifyPassword:(NSString *)password originPassword:(NSString *)originPassword handler:(userRequestHandler)handler;

/**
 * 我的特权 投顾特权
 */
+ (void)requestPrivileges:(userRequestHandler)handler;

/**
 * 我的特权(不请求投顾接口)
 */
+ (void)requestPrivilegesWithoutAdvisor:(userRequestHandler)handler;

/**
 * 请求首页图标列表
 */
+ (void)requestBasicIconInfo:(iconInfoRequestHandler)handler;

/**
 * 更新用户权限
 */
+ (void)updatePrivileges;

/**
 * 更新用户风测合同状态
 */
+ (void)updateUserRiskContract;

/**
 * 请求订单数
 */
+ (void)requestUserOrderCount:(NSString *)uid status:(NSArray *)status clientType:(NSInteger)type handler:(upOrderCountHandler)handler;

/**
 * 请求风险评测 retCode 1:保守型 2:谨慎型 3:稳健型 4:积极型 5:激进型
 */
+ (void)requestRiskWithUID:(NSString *)uid handler:(userRequestHandler)handler;

/**
 * 是否关注微信公众号 retCode 0：未关注；1：已关注；2：未知
 */
+ (void)isFollowWeChatPublic:(NSString *)uid appId:(NSString *)appId handler:(userRequestHandler)handler;

/**
 * 判断用户权限是否已经刷新
 */
+ (BOOL)isUserRightInit;

/**
 * 判断是否是游客登录
 */
+ (BOOL)isTouristLogin;

/**
 * 发送短信验证码，验证码登录时使用
 */
+ (void)sendCodeWithMobile:(NSString *)mobile handler:(sendCodeRequestHandler)handler;

/**
 * 手机验证码登录
 */
+ (void)mobileLogin:(NSString *)mobile password:(NSString *)password code:(NSString *)code codeId:(NSString *)codeId handler:(userRequestHandler)handler;

/**
 * 手机号码一键登录
 */
+ (void)simLogin:(NSString *)accessToken password:(NSString *)password handler:(userRequestHandler)handler;

/**
 * 设置用户偏好
 */
+ (void)setInvestPreference:(UPUserInvestPreferenceType)investPreference token:(NSString *)token handler:(userRequestHandler)handler;


/**
 * 添加备注信息
 *
 * @param code 股票代码
 * @param setCode 股票市场码
 * @param remarkInfo 备注信息
 *
 * 错误码:
 * -1000: 非法请求
 * -1001: 参数错误
 * -3000: 系统错误
 * -3001: 服务器繁忙
 * -9999：未知
 */
+ (void)addRemark:(NSString *)code setCode:(NSInteger)setCode remarkInfo:(NSString *)remarkInfo callback:(UPUserCallback)callback;

/**
 * 修改备注信息
 *
 * @param remarkid 备注id
 * @param remarkInfo 备注信息
 *
 * 错误码:
 * -1000: 非法请求
 * -1001: 参数错误
 * -3000: 系统错误
 * -3001: 服务器繁忙
 * -9999：未知
 */
+ (void)modifyRemark:(NSString *)remarkid remarkInfo:(NSString *)remarkInfo callback:(UPUserCallback)callback;

/**
 * 删除备注信息
 *
 * @param remarkid 备注id
 *
 * 错误码:
 * -1000: 非法请求
 * -1001: 参数错误
 * -3000: 系统错误
 * -3001: 服务器繁忙
 * -9999：未知
 */
+ (void)deleteRemark:(NSString *)remarkid callback:(UPUserCallback)callback;

/**
 * 查询个股备注
 *
 * @param code 股票代码
 * @param setCode 股票市场码
 *
 * 错误码:
 * -1000: 非法请求
 * -1001: 参数错误
 * -3000: 系统错误
 * -3001: 服务器繁忙
 * -9999：未知
 */
+ (void)getRemarkByStock:(NSString *)code setCode:(NSInteger)setCode callback:(UPUserCallback)callback;

/**
 * 查询所有备注
 *
 * @param offset 偏移量
 * @param size 数量
 *
 * 错误码:
 * -1000: 非法请求
 * -1001: 参数错误
 * -3000: 系统错误
 * -3001: 服务器繁忙
 * -9999：未知
 */
+ (void)getAllRemark:(NSInteger)offset size:(NSInteger)size callback:(UPUserCallback)callback;

/**
 * 根据股票列表查询备注
 *
 * @param codeList 股票代码数组
 * @param setCodeList 股票市场码数组（NSNumber类型）
 * @param simpleData 是否简版数据
 * @param startTime  开始时间(毫秒)时间戳
 * 错误码:
 * -1000: 非法请求
 * -1001: 参数错误
 * -3000: 系统错误
 * -3001: 服务器繁忙
 * -9999：未知
 */
+ (void)getRemarkListByScodes:(NSArray<NSString *> *)codeList setCodeList:(NSArray<NSNumber *> *)setCodeList isSimpleData:(BOOL)simpleData startTime:(long long)startTime callback:(UPUserCallback)callback;

+ (void)checkOpenRegistered:(UPUserOAuthModel *)oauthModel callback:(UPUserCallback)callback;

+ (void)mobileLoginAndBind:(UPUserOAuthModel *)oauthModel mobile:(NSString *)mobile code:(NSString *)code codeId:(NSString *)codeId handler:(userRequestHandler)handler;

/**
 * TODO 提交用户账号注销
 * iRet状态码说明：
 *      1：用户账号待注销中
 *      0：提交成功
 *      -1：调用失败
 *      -2：用户账号不存在
 */
+ (void)submitUserAccountCancelCallback:(userRequestHandler)handler;

/**
 * TODO 查询用户账号注销状态
 * iRet状态码说明：
 *      1：用户账号待注销中
 *      2：用户账号已恢复
 *      3：用户账号已注销
 *      4：用户账号未提交注销
 *      -1：调用失败
 */
+ (void)queryUserAccountCancelStatusCallback:(UPUserCallback)callback;

/**
 * TODO 恢复用户账号的注销
 * iRet状态
 *      0：调用成功
 *      -1：调用失败
 */
+ (void)recoverUserAccountCancelCallback:(userRequestHandler)handler;


// 检测用户是否注册
+ (void)checkPhoneIsRegister:(NSString*)phone code:(NSString*)code codeId:(NSString*)codeId callback:(UPUserCallback)callback;

/**
* 设置三方用户信息
*/
+ (void)setThirdUser:(UPUserModel *)user userInfo:(UPUserInfoModel *)userInfo;

/// 提交用户账号注销
+ (void)submitUserAccountCancelBySmsCode:(NSString *)code codeId:(NSString *)codeId callback:(userRequestHandler)callback;

@end
