//
//  UPHybridPlugin.h
//  HybridFramework
//
//  Created by sa<PERSON><PERSON> on 2017/5/27.
//  Copyright © 2017年 UPChina. All rights reserved.
//

#ifndef UPHybridPlugin_h
#define UPHybridPlugin_h

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

@interface UPHybridPlugin : NSObject

@property (nonatomic, readonly, copy) NSString * pluginName;

-(instancetype)initWithName:(NSString *)name;

-(BOOL)execute:(NSString *)callbackId action:(NSString *)action args:(NSDictionary *)args;

-(void)sendSuccessResult:(NSString *)callbackId data:(NSDictionary *)data;

-(void)sendErrorResult:(NSString *)callbackId message:(NSString *)message;

-(void)sendEvent:(NSString *)action data:(NSDictionary *)data;

-(void)presentViewController:(UIViewController *)controller;

-(void)onInit;

-(void)viewAppear;

-(void)viewDisappear;

@end

#endif /* UPHybridPlugin_h */
