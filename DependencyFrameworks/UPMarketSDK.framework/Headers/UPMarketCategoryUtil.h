//
//  UPMarketCategoryUtil.h
//  UPMarketSDK
//
//  Created by j<PERSON><PERSON> on 2019/5/27.
//  Copyright © 2019年 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarketDefine.h"


@interface UPMarketCategoryUtil : NSObject

/**
* 是否属于沪深市场
* @param setCode  市场代码
*/
+ (BOOL)isHSMarket:(UPMarketSetCode)setCode;

/**
* 是否属于美股市场
* @param setCode  市场代码
*/
+ (BOOL)isUSMarket:(UPMarketSetCode)setCode;

/**
* 是否属于沪深A股
* @param category  股票类别
*/
+ (BOOL)isHSA:(UPMarketStockCategory)category;

/**
* 是否属于沪深B股
* @param category  股票类别
*/
+ (BOOL)isHSB:(UPMarketStockCategory)category;

/**
* 是否属于沪深AB股
* @param category  股票类别
*/
+ (BOOL)isHSAB:(UPMarketStockCategory)category;

/**
* 是否属于沪深指数
* @param setCode  市场代码
* @param category  股票类别
*/
+ (BOOL)isHSIndex:(UPMarketSetCode)setCode category:(UPMarketStockCategory)category;

/**
* 是否属于板块
* @param category  股票类别
*/
+ (BOOL)isBlock:(UPMarketStockCategory)category;

/**
* 是否属于港股通
* @param category  股票类别
*/
+ (BOOL)isGGT:(UPMarketStockCategory)category;

/**
* 是否属于陆股通
 @param setCode 市场代码
 @param code 股票代码
*/
+ (BOOL)isLGT:(NSInteger)setCode code:(NSString *)code;

/**
 * 是否是科创板
 * @param origCategory  股票原始类别
 */
+ (BOOL)isKCB:(NSUInteger)origCategory;

/**
 * 是否是创业板
 * @param origCategory  股票原始类别
 */
+ (BOOL)isCYB:(NSUInteger)origCategory;

/**
 * 是否是注册制创业板
 * @param subCategory  股票子类别
 */
+ (BOOL)isCYBReg:(UPMarketStockSubCategory)subCategory;

/**
 * 是否国债逆回购
 * @param origCategory  股票原始类别
 */
+ (BOOL)isZQNHG:(NSUInteger)origCategory;

/**
* 是否是深圳中小企业板
* @param origCategory  股票原始类别
*/
+ (BOOL)isZXB:(NSUInteger)origCategory;

/**
* 是否是定向可转债
* @param origCategory  股票原始类别
*/
+ (BOOL)isDirectiveKZZ:(NSUInteger)origCategory;

/**
* 是否是北交所股票
* @param setCode  市场代码
*/
+ (BOOL)isBJMarket:(UPMarketSetCode)setCode;

/**
* 是否是北交所要约股票
* @param setCode  市场代码
* @param origCategory  原始类别
*/
+ (BOOL)isBJYY:(UPMarketSetCode)setCode origCategory:(NSUInteger)origCategory;

/**
* 是否是北交所发行股票
* @param setCode  市场代码
* @param origCategory  原始类别
*/
+ (BOOL)isBJFX:(UPMarketSetCode)setCode origCategory:(NSUInteger)origCategory;

@end
