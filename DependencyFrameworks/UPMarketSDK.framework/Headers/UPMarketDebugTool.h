//
//  UPMarketDebugTool.h
//  UPMarketSDK
//
//  Created by tamry on 2020/6/29.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarketAddressModel.h"

@interface UPMarketDebugTool : NSObject

/** basichq服务对应的servantName */
@property (nonatomic, copy) NSString *debugHqServantName;
/** 行情WUP服务连接的地址 */
@property (nonatomic, copy) NSString *debugWUPAddress;

@property (atomic, strong) NSString *userId;

/** 强制使用兜底地址,用来测试兜底地址逻辑 */
@property (nonatomic, assign) BOOL forceUseDefaultAddress;

+ (instancetype)sharedInstance;

/**
 * 设置行情测试主站
 */
- (BOOL)setDebugAddress:(NSString *)address addressType:(UPMarketAddressType)addressType;

/**
 * 获取行情测试主站
 */
- (UPMarketAddressModel *)getDebugAddress:(UPMarketAddressType)addressType;

/**
 * 清空所有指定的行情主站 - 自动选择
 */
- (void)clearAllSettingAddress;

/**
 * 清空指定类型的行情主站 - 自动选择
 */
- (void)clearSettingAddress:(UPMarketL2Type)l2Type;

- (void)clearSettingAddress:(UPMarketL2Type)l2Type notify:(BOOL)notify;
/**
 * 设置行情测试主站
 */
- (BOOL)setSettingAddress:(UPMarketAddressModel *)addressModel;

/**
 * 获取设置中指定的行情主站
 */
- (UPMarketAddressModel *)getSettingAddress:(UPMarketL2Type)l2Type;

/**
 * 当前主站是否为设置的主站
 */
- (BOOL)isSettingAddress:(UPMarketAddressModel *)address;
/**
 * 获取当前主站地址
 */
- (NSString *)getHostAddress:(UPMarketAddressType)addressType;

/**
 * 清除码表数据
 */
- (BOOL)clearAllCode;

@end
