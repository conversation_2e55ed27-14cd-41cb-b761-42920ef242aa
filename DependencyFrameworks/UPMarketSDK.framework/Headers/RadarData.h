// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `RadarData.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>

enum {
    RadarData_E_STARS_LEVEL_E_STARS_NONE = 0,
    RadarData_E_STARS_LEVEL_E_STARS_LEVEL1 = 1,
    RadarData_E_STARS_LEVEL_E_STARS_LEVEL2 = 2,
    RadarData_E_STARS_LEVEL_E_STARS_LEVEL3 = 3,
    RadarData_E_STARS_LEVEL_E_STARS_LEVEL4 = 4
};
#define RadarData_E_STARS_LEVEL NSInteger


enum {
    RadarData_E_STRENGTH_CHANGE_NO_CHANGE = 1,
    RadarData_E_STRENGTH_CHANGE_UP_CHANGE = 2,
    RadarData_E_STRENGTH_CHANGE_DOWN_CHANGE = 3
};
#define RadarData_E_STRENGTH_CHANGE NSInteger


enum {
    RadarData_E_RADAR_TYPE_E_RADAR_BUY = 1,
    RadarData_E_RADAR_TYPE_E_RADAR_SELL = 2
};
#define RadarData_E_RADAR_TYPE NSInteger

@interface RadarDataTemperature : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt32 jce_iUpdateTime;
@property (nonatomic, assign) JceDouble jce_dblStrength;
@property (nonatomic, assign) JceInt64 jce_lStrengthBuy;
@property (nonatomic, assign) JceInt64 jce_lStrengthSell;
@property (nonatomic, assign) JceDouble jce_dblLatent;
@property (nonatomic, assign) JceInt64 jce_lLatentBuy;
@property (nonatomic, assign) JceInt64 jce_lLatentSell;
@end

@interface RadarDataRadarQt : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iStockID;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, assign) JceDouble jce_dblStrength;
@property (nonatomic, assign) RadarData_E_STARS_LEVEL jce_eStarsLevel;
@property (nonatomic, assign) JceInt64 jce_lStrengthBuy;
@property (nonatomic, assign) JceInt64 jce_lStrengthSell;
@property (nonatomic, assign) RadarData_E_STRENGTH_CHANGE jce_eChgFlag;
@property (nonatomic, assign) JceDouble jce_dNowPrice;
@property (nonatomic, assign) JceDouble jce_dPrevPrice;
@end

@interface RadarDataRadarChg : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iStockID;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, assign) RadarData_E_RADAR_TYPE jce_eType;
@property (nonatomic, assign) RadarData_E_STARS_LEVEL jce_eStarsLevel;
@property (nonatomic, assign) JceInt64 jce_lStrengthBuy;
@property (nonatomic, assign) JceInt64 jce_lStrengthSell;
@property (nonatomic, assign) RadarData_E_STRENGTH_CHANGE jce_eChgFlag;
@property (nonatomic, assign) JceDouble jce_dNowPrice;
@property (nonatomic, assign) JceDouble jce_dPrevPrice;
@end

@interface RadarDataRadarQtList : UPTAFJceObject
@property (nonatomic, strong) NSArray<RadarDataRadarQt*>* jce_vRadarQt;
@end

@interface RadarDataRadarRtMin : UPTAFJceObject
@property (nonatomic, assign) JceUInt32 jce_uiStockID;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, assign) JceDouble jce_dblRtStrength;
@property (nonatomic, assign) JceInt64 jce_lRtStrengthBuy;
@property (nonatomic, assign) JceInt64 jce_lRtStrengthSell;
@end

@interface RadarDataRadarRtMinList : UPTAFJceObject
@property (nonatomic, assign) JceBool jce_bClear;
@property (nonatomic, assign) JceUInt32 jce_uiStockID;
@property (nonatomic, strong) NSArray<RadarDataRadarRtMin*>* jce_vData;
@end

@interface RadarDataRadarRtMinBatchList : UPTAFJceObject
@property (nonatomic, strong) NSArray<RadarDataRadarRtMinList*>* jce_vStock;
@end

@interface RadarDataTempList : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt32 jce_iFlag;
@property (nonatomic, strong) NSArray<RadarDataTemperature*>* jce_vtTemperature;
@end



