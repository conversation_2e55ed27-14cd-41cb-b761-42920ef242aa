//
// Created by Nic on 2017/8/5.
// Copyright (c) 2017 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarketDefine.h"

@class HQSysHServerConfig;


/**
 * 行情地址类型
 */
typedef NSUInteger UPMarketAddressType;
NS_ENUM(UPMarketAddressType) {
    UPMarketAddressTypeHQ = 0,    // 行情主站
    UPMarketAddressTypeIndex      // 指标主站
};

static const int kUPMarketAddressFailScore = 1000;

/**
 * 行情地址数据结构
 */
@interface UPMarketAddressModel : NSObject <NSCopying>

@property (nonatomic, strong) NSString *ip;                         // 行情主站IP
@property (nonatomic, assign) NSInteger port;                       // 端口
@property (nonatomic, strong) NSString *displayName;                // 展示名称
@property (nonatomic, strong) NSString *servantName;                // 行情服务名称
@property (nonatomic, assign) UPMarketL2Type l2Type;                // L2主站类型
@property (nonatomic, assign) BOOL isLocal;                         // 是否本地兜底地址
@property (nonatomic, assign) NSInteger score;                      // 主站打分,分数越低主站优先级越高
@property (nonatomic, assign) UPMarketAddressType addressType;      // 主站地址类型, 指标主站/行情主站
@property (nonatomic, assign) int delayMillis;                      // 主站测速延时
@property (nonatomic, assign) int maxCount;                         // 主站支持最大连接数
@property (nonatomic, assign) int currentCount;                     // 主站当前连接数
@property (nonatomic, assign) int testResult;                       // 上一次测速结果， 0：未开始 1：成功 -1：失败
@property (nonatomic, copy) NSOrderedSet<NSNumber *> *l1Markets;    // 当前主站支持的L1市场列表
@property (nonatomic, copy) NSOrderedSet<NSNumber *> *l2Markets;    // 当前主站支持的L2市场列表
@property (nonatomic, assign) int stationType;                      // 地址支持市场的唯一key

// 根据主站设置字段创建主站数据结构 - 内部使用
+ (instancetype)modelWithConfigDic:(NSDictionary *)dic addressType:(UPMarketAddressType)addressType;

@end
