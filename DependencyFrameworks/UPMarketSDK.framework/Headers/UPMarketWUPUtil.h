//
// Created by j<PERSON><PERSON> on 2018/5/17.
// Copyright (c) 2018 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarketDefine.h"
#import "Factor.h"
#import "StockAnomaly.h"
#import "Common.h"

@class HQSysClientInfo;
@class HQSysHeaderInfo;
@class IndicatorSysIndexHeaderInfo;
@class HQSysHTypeSubType;

@interface UPMarketWUPUtil : NSObject

+ (HQSysClientInfo *)newClientInfo;

+ (HQSysHeaderInfo *)newHeaderInfo;

+ (HQSysHeaderInfo *)newHeaderInfo:(UPMarketSetCode)setCode;

+ (UPMarketTradeStatus)getStockStatus:(short)status;

+ (UPMarketStockCategory)getCategoryWithType:(short)setCode origType:(short)origCategory origSubType:(short)subType;

+ (UPMarketStockCategory)getCategoryWithType2:(short)setCode origType:(short)type origSubType:(short)subType;

+ (UPMarketStockSubCategory)getStockSubCategory:(short)setCode category:(UPMarketStockCategory)category origType:(short)origType origSubType:(int)origSubType;

+ (NSInteger)getHqPushFlag:(UPMarketPushFlag)flag;

+ (int)getIndexPushFlag:(UPMarketPushFlag)flag;

+ (short)getBusinessType:(UPMarketBlockType)type setCode:(UPMarketSetCode)setCode;

+ (short)getMoneyRankSortColumn:(UPMarketSortColumn)column;

+ (short)getHqSortColumn:(UPMarketSortColumn)column;

+ (short)getRangeSortColumn:(UPMarketSortColumn)column;

+ (short)getAHSortColumn:(UPMarketSortColumn)colun;

+ (short)getHqSortOrder:(UPMarketSortOrder)order;

+ (UPMarketTradeStatus)getTradeStatus:(short)status;

// 获取内盘交易状态,添加新的setcode时需要判断
+ (UPMarketTradeStatus)getTradeStatusForHS:(short)status;
// 获取外盘交易状态添加新的setcode时需要判断
+ (UPMarketTradeStatus)getMarketStatus:(short)status;

+ (short)getHqLevel2PoolType:(UPMarketLevel2PoolType)type;

+ (short)getKlineType:(UPMarketKlineType)type;

+ (int)getMoneyType:(UPMarketMoneyType)type;

+ (short)getHqDDEType:(UPMarketDDEType)type;

+ (short)getIndexType:(UPMarketIndexType)type;

+ (NSInteger)getIndicatorPeriodType:(int)type;

+ (NSInteger)getIndexPeriodType:(int)type;

+ (UPMarketCoinType)parseCoinType:(short)type;

+ (int)getBusinessMarket:(UPMarketBlockType)type setCode:(UPMarketSetCode)setCode;

+ (NSString *)getBlockCode:(int)type;

+ (NSUInteger)getHqDataTypeWithBlockType:(UPMarketBlockType)type level:(UPMarketDataLevel)level;

+ (UPMarketOrderStatus)parseOrderStatus:(short)status;

+ (int)getIndexSortOrder:(UPMarketSortOrder)order;

+ (NSString *)getIndexSortColumn:(UPMarketSortColumn)column;

+ (NSDictionary *)getIndexQueryCondition:(UPMarketIndexType)type subType:(NSUInteger)subType;

+ (HQSys_E_FACTOR_TYPE)getFactorType:(UPMarketStockFactorType)type;

+ (NSInteger)buildTafSubjectChangeType:(UPMarketSubjectChangeType)changeType;

+ (IndicatorSysIndexHeaderInfo *)newIndexHeadInfo;

+ (HQSysFHeaderInfo *)newFactorHeaderInfo;

+ (UPMarketSubjectChangeType)parseSubjectChangeType:(NSInteger)type;

+ (BOOL)isCustomType:(UPMarketBlockType)type;

+ (int)getCustomType:(UPMarketBlockType)type;

+ (NSInteger)getDDERankSortColumn:(UPMarketSortColumn)column;

+ (JceInt32)getIndexDataType:(UPMarketIndexType)type;

+ (UPMarketSetCode)getOptionSetCode:(UPMarketSetCode)code;

+ (BOOL)isOptionSetCode:(UPMarketSetCode)setCode;

+ (UPMarketSetCode)getOptionUnderlyingSetCode:(UPMarketSetCode)code;

+ (UPMarketActBSFlag)getActBSFlag:(JceInt8)bsFlag;

+ (BOOL)isIndexCFGReq:(NSString *)code;

+ (UPMarketBlockType)getIndexCFGBusinessType:(NSString *)code;

+ (HQExtend_ANOMALY_TYPE)getStockChangeType:(UPMarketStockChangeType)changeType;

+ (UPMarketStockChangeType)getUPStockChangeType:(HQExtend_ANOMALY_TYPE)serverType;

+ (HQSys_E_STOCK_ORDER_TYPE)getTAFDXJLType:(UPMarketDXJLType)upType;

+ (UPMarketDXJLType)getUPDXJLType:(HQSys_E_STOCK_ORDER_TYPE)tafType;

+ (UPMarketHKBalanceDir)getHKBalanceDir:(JceInt8)direction;

+ (NSArray<HQSysHTypeSubType *> *)getSubType:(UPMarketBlockType)type;

+ (HQSys_E_AM_STK_TYPE)getAMType:(UPMarketAMType)amType;

+ (HQSys_E_AH_PREMIUM_TYPE)getPremiumType:(UPMarketPremiumType)type;

+ (NSNumber *)getZDFBType:(NSNumber *)number;

+ (NSNumber *)transZDFBType:(NSNumber *)number;
@end
