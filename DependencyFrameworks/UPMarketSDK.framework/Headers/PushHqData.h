// **********************************************************************
// This file was generated by a TAF parser!
// TAF version 5.3.0 by TAF Team.
// Generated from `PushHqData.jce'
// **********************************************************************

#import <UPTAF/JceObject.h>

enum {
    IC_BLOCKCHANGE_TYPE_E_TYPE_BKALL = 0,
    IC_BLOCKCHANGE_TYPE_E_TYPE_BKUP = 1,
    IC_BLOCKCHANGE_TYPE_E_TYPE_BKDOWN = 2,
    IC_BLOCKCHANGE_TYPE_E_TYPE_BKRAPIDLYUP = 3,
    IC_<PERSON>L<PERSON><PERSON><PERSON>ANGE_TYPE_E_TYPE_BKQUICKLYBACKUP = 4,
    IC_BLOC<PERSON>CHANGE_TYPE_E_TYPE_BKQUICKLYDOWN = 5,
    IC_BLOCKCHANGE_TYPE_E_TYPE_BKGGRAPIDLYUP = 6
};
#define IC_BLOCKCHANGE_TYPE NSInteger

@interface ICSShortLineStrategyNew : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_m_uMsgType;
@property (nonatomic, assign) JceInt32 jce_m_nTime;
@property (nonatomic, assign) JceInt32 jce_m_nSerialId;
@property (nonatomic, assign) JceInt32 jce_m_dwStockID;
@property (nonatomic, strong) NSString* jce_m_strMsg;
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@end

@interface ICSPushShortLineData : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSNumber*, NSArray<ICSShortLineStrategyNew*>*>* jce_mData;
@end

@interface ICSStocksInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceFloat jce_fRise;
@end

@interface ICSBlockChange : UPTAFJceObject
@property (nonatomic, assign) JceInt16 jce_shtMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, assign) IC_BLOCKCHANGE_TYPE jce_eType;
@property (nonatomic, assign) JceFloat jce_fRiseSpeed;
@property (nonatomic, assign) JceFloat jce_fRise;
@property (nonatomic, assign) JceFloat jce_fPre5MinRise;
@property (nonatomic, assign) JceFloat jce_fPre30MinRise;
@property (nonatomic, assign) JceFloat jce_fRiseSpeed30;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, strong) NSArray<ICSStocksInfo*>* jce_vLeadStocks;
@property (nonatomic, assign) JceInt32 jce_iBlockType;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) JceInt64 jce_lTimestamp;
@property (nonatomic, strong) NSString* jce_sCopywriting;
@end

@interface ICSPushBlockChangeData : UPTAFJceObject
@property (nonatomic, strong) NSArray<ICSBlockChange*>* jce_vData;
@end

@interface ICSPushBlockChangeDataNew : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSNumber*, NSArray<ICSBlockChange*>*>* jce_mData;
@end

@interface ICSLeadBlkInfo : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, assign) JceInt32 jce_iBlockType;
@property (nonatomic, assign) JceInt32 jce_iDate;
@property (nonatomic, assign) IC_BLOCKCHANGE_TYPE jce_eType;
@end

@interface ICSPushLeadBlkInfo : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSNumber*, NSArray<ICSLeadBlkInfo*>*>* jce_mData;
@end

@interface ICSQtRec : UPTAFJceObject
@property (nonatomic, assign) JceInt32 jce_iMarket;
@property (nonatomic, strong) NSString* jce_sCode;
@property (nonatomic, strong) NSString* jce_sName;
@property (nonatomic, assign) JceInt32 jce_iTime;
@property (nonatomic, assign) JceDouble jce_fPreClose;
@property (nonatomic, assign) JceDouble jce_fOpen;
@property (nonatomic, assign) JceDouble jce_fHigh;
@property (nonatomic, assign) JceDouble jce_fLow;
@property (nonatomic, assign) JceDouble jce_fPrice;
@property (nonatomic, assign) JceDouble jce_fPercent;
@property (nonatomic, strong) NSString* jce_sStatus;
@end

@interface ICSPushQtRec : UPTAFJceObject
@property (nonatomic, strong) NSDictionary<NSString*, ICSQtRec*>* jce_mData;
@end



