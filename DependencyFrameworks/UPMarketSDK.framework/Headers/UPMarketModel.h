//
//  UPMarketModel.h
//  UPMarketSDK
//
//  Created by hubup<PERSON> on 2017/1/19.
//  Copyright © 2017年 upchina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <CoreGraphics/CoreGraphics.h>
#import <UPTAF/UPTAF.h>
#import "UPMarketDefine.h"

@class UPHqStockHq;
@class UPMarketXSBYYStockData;
@class UPMarketXSBFXStockData;
@class UPMarketKZZData;
@class UPHqRTMinData;
// MARK: - UPHqStock

@interface UPHqStock : NSObject

@property(nonatomic, assign) short shtSetcode;                            // 股票市场
@property(nonatomic, strong) NSString *sCode;                             // 股票代码
@property(nonatomic, strong) NSString *sName;                             // 股票名称

@end

// MARK: - UPHqStockUnique 股票基础数据，含市场码和股票码

@interface UPHqStockUnique : NSObject <NSCopying>

@property(nonatomic, assign) UPMarketSetCode setCode;                     // 股票市场
@property(nonatomic, copy) NSString *code;                                // 股票代码

/**
 * 构建股票基础数据
 *
 * @param setCode  市场码 如深圳市场 UPMarketSetCodeSZ
 * @param code 股票代码 如 万科A:'000002'
 */
- (instancetype)initWithCode:(UPMarketSetCode)setCode code:(NSString *)code;

/**
 * 返回股票的Hash值- 内部使用
 */
- (int)hashCode;

@end

@interface UPHqStockHqExtraData : NSObject

@property(nonatomic, strong) NSArray *buyOrderPrice;                      // 叫买价      NSNumber
@property(nonatomic, strong) NSArray *buyOrderVol;                        // 叫买盘：股数 NSNumber
@property(nonatomic, strong) NSArray *sellOrderPrice;                     // 叫卖价      NSNumber
@property(nonatomic, strong) NSArray *sellOrderVol;                       // 叫卖盘：股数 NSNumber
@property(nonatomic, strong) NSArray *buyerNum;                           // 买家个数    NSNumber
@property(nonatomic, strong) NSArray *sellerNum;                          // 卖家个数    NSNumber
@property(nonatomic, assign) double buyAvgPrice;                          // 委买均价
@property(nonatomic, assign) double sellAvgPrice;                         // 委卖均价
@property(nonatomic, assign) double buyPriceNum;                          // 委买价位单数
@property(nonatomic, assign) double sellPriceNum;                         // 委卖价位单数
@property(nonatomic, assign) double buyTotalVol;                          // 委买总单量
@property(nonatomic, assign) double sellTotalVol;                         // 委卖总单量
@property(nonatomic, assign) double committee;                            // 委比
@property(nonatomic, assign) long long commitSum;                         // 委托总量
@property(nonatomic, assign) long long commitDiff;                        // 委托差
@property(nonatomic, assign) double afterDealAmount;                      // 盘后成交额
@property(nonatomic, assign) long long afterDealVol;                      // 盘后成交量
@property(nonatomic, assign) int gzhgAvgBP;                               // 国债回购均涨跌BP
@property(nonatomic, assign) int gzhgBP;                                  // 国债回购涨跌BP
@property(nonatomic, assign) double returnRateIn10Day;                    // 10天回报率
@property(nonatomic, assign) double maxPrice52Week;                       // 52周最高价
@property(nonatomic, assign) double minPrice52Week;                       // 52周最低价
@property(nonatomic, assign) double maxPriceHistory;                      // 历史最高价
@property(nonatomic, assign) double minPriceHistory;                      // 历史最低价
@property (nonatomic, assign) double refPrice;                            // 参考价
@property (nonatomic, assign) double refPriceStart;                       // 参考价开始区间
@property (nonatomic, assign) double refPriceEnd;                         // 参考价结束区间
@property (nonatomic, assign) long long matchVol;                         // 匹配量
@property (nonatomic, assign) long long unmatchVol;                       // 未匹配量
@property (nonatomic, assign) double day5ChgRatio;                        // 5日涨跌幅
@property (nonatomic, assign) double day10ChgRatio;                       // 10日涨跌幅
@property (nonatomic, assign) double day20ChgRatio;                       // 20日涨跌幅
@property (nonatomic, assign) double monthChgRatio;                       // 月涨跌幅
@property (nonatomic, assign) double seasonChgRatio;                      // 季涨跌幅
@property (nonatomic, assign) double yearChgRatio;                        // 年涨跌幅
@property (nonatomic, assign) double thisYearChgRatio;                    // 年初至今涨跌幅
@property (nonatomic, assign) UPMarketActBSFlag actBSFlag;                // 竞价方向
@property (nonatomic, assign) double lastChangeValue;                     // 年初至今涨跌幅
@property (nonatomic, assign) BOOL blockTrade;                            // 大宗交易
@property (nonatomic, assign) double totalChg;                            // 累计涨跌幅
@property (nonatomic, assign) double iOPV;                            // ETF基金 IOPV（内盘基金个股）ETF基金溢价率计算规则：当日价格-IOPV）/ IOPV*100% （IOPV有行情后台提供）

@end

// MARK: - UPHqStockHqMoneyFlowData

@interface UPHqStockHqMoneyFlowData : NSObject

@property(nonatomic, assign) double allNetIn;                             // 资金净流入
@property(nonatomic, assign) double day3NetIn;                            // 3日资金净流入
@property(nonatomic, assign) double day5NetIn;                            // 5日资金净流入
@property(nonatomic, assign) double day10NetIn;                           // 10日资金净流入
@property(nonatomic, assign) double day20NetIn;                           // 20日资金净流入
@property(nonatomic, assign) double mainNetIn;                            // 主力资金净流入
@property(nonatomic, assign) double mainNetRatio;                         // 主力资金净占比
@property(nonatomic, assign) double day3MainNetIn;                        // 3日主力资金净流入
@property(nonatomic, assign) double day3MainNetRatio;                     // 3日主力资金净占比
@property(nonatomic, assign) double day5MainNetIn;                        // 5日主力资金净流入
@property(nonatomic, assign) double day5MainNetRatio;                     // 5日主力资金净占比
@property(nonatomic, assign) double min3MainNetIn;                        // 3分钟主力资金净流入
@property(nonatomic, assign) double min3MainNetRatio;                     // 3分钟主力资金净占比
@property(nonatomic, assign) double min5MainNetIn;                        // 5分钟主力资金净流入
@property(nonatomic, assign) double min5MainNetRatio;                     // 5分钟主力资金净占比
@property(nonatomic, assign) double min10MainNetIn;                       // 10分钟主力资金净流入
@property(nonatomic, assign) double min10MainNetRatio;                    // 10分钟主力资金净占比
@property(nonatomic, assign) double min30MainNetIn;                       // 30分钟主力资金净流入
@property(nonatomic, assign) double min30MainNetRatio;                    // 30分钟主力资金净占比
@property(nonatomic, assign) double min60MainNetIn;                       // 60分钟主力资金净流入
@property(nonatomic, assign) double min60MainNetRatio;                    // 60分钟主力资金净占比

@end

@interface UPMarketBlockInfo : NSObject

@property(nonatomic, assign) int setCode;                       // 市场代码
@property(nonatomic, copy) NSString *blockCode;                 // 板块码
@property(nonatomic, copy) NSString *blockName;                 // 板块名称
@property(nonatomic, assign)UPMarketStockCategory blockType;    // 板块类型

@end

// MARK: - UPMarketXSBRelationStockInfo 新三板关联发行要约股票信息
@interface UPMarketRelationStockInfo : NSObject

@property(nonatomic, assign) int setCode;                               // 市场代码
@property(nonatomic, copy) NSString *code;                              // 股票代码
@property(nonatomic, copy) NSString *name;                              // 股票名称
@property(nonatomic, assign) UPMarketRelationStockType relationType;    // 关联类型
@property(nonatomic, assign) UPMarketStockCategory category;            // 股票类别
@property(nonatomic, assign) UPMarketStockSubCategory subCategory;      // 股票子类别
@property(nonatomic, assign) NSUInteger origCategory;                   // 股票原始类别
@property(nonatomic, strong)UPMarketXSBYYStockData *yyStockData;        // 要约信息
@property(nonatomic, strong)UPMarketXSBFXStockData *fxStockData;        // 发行信息
@property(nonatomic, strong)UPMarketKZZData *kzzData;                   // 可转债信息
@end


// MARK: - UPMarketXSBData

@interface UPMarketXSBData : NSObject

@property (nonatomic, assign) UPMarketXSBZRType zrType;                     // 转让类型
@property (nonatomic, assign) UPMarketXSBZRStatus zrStatus;                 // 转让状态
@property (nonatomic, assign) UPMarketXSBFCType fcType;                     // 分层信息
@property (nonatomic, assign) NSInteger securityType;                       // 安全类型
@property (nonatomic, assign) UPMarketXSBTPStatus tpStatus;                 // 停牌状态
@property (nonatomic, assign) UPMarketXSBCQCXStatus cqcxStatus;             // 除权除息状态
@property (nonatomic, assign) UPMarketXSBRight diffRight;                   // 表决权差异
@property (nonatomic, assign) NSInteger marketMakeCount;                    // 做市商数量
@end


// MARK: - UPMarketZQExData

@interface UPMarketZQExData : NSObject

@property(nonatomic, assign) double incomeRatePerYear;                      // 年华收益率
@property(nonatomic, assign) double incomePer10w;                           // 10万元收益(单位:元)
@property(nonatomic, assign) double incomePer1k;                            // 1千元收益(单位:元)
@property(nonatomic, assign) short days;                                    // 产品期数
@property(nonatomic, assign) short zkDays;                                  // 占款天数
@property(nonatomic, assign) int jxStartDate;                               // 计息开始日期(格式:YYYYMMDD)
@property(nonatomic, assign) int jxEndDate;                                 // 计息就结束日期(格式:YYYYMMDD)
@property(nonatomic, assign) int todayBuyDate;                              // 今日购买日期(格式:YYYYMMDD)
@property(nonatomic, assign) int zjAvailableDate;                           // 资金可用日期(格式:YYYYMMDD)
@property(nonatomic, assign) int zjFetchableDate;                           // 资金可取日期(格式:YYYYMMDD)
@property(nonatomic, assign) double buyBackPrice;                           // 购回价格
@property(nonatomic, assign) double zqPremiumRatio;                         // 溢价率
@property(nonatomic, assign) double gzjz;                                   // 转股价值
@property(nonatomic, assign) double bdPrice;                                // 正股价值

@end

// MARK: - UPMarketOptionTItem

@interface UPMarketOptionTItem : NSObject

@property(nonatomic, assign) double exercisePrice;                          // 行权价格
@property(nonatomic, strong) UPHqStockHq *callOptionData;                   // 认购期权
@property(nonatomic, strong) UPHqStockHq *putOptionData;                    // 认沽期权

@end

// MARK: - UPMarketOptionTData

@interface UPMarketOptionTData : NSObject

@property(nonatomic, assign) int underlyingMarket;                          // 标的市场
@property(nonatomic, strong) NSString *underlyingCode;                      // 标的代码
@property(nonatomic, strong) NSString *contractType;                        // 合约类型
@property(nonatomic, assign) int endDate;                                   // 结束日期
@property(nonatomic, assign) int leftDays;                                  // 剩余日期
@property(nonatomic, strong) NSArray<UPMarketOptionTItem *> *optionList;    // 期权列表

@end

// MARK: - UPMarketOptionBaseData

@interface UPMarketOptionBaseData : NSObject

@property(nonatomic, assign) short setCode;                                 // 股票市场
@property(nonatomic, strong) NSString *code;                                // 股票代码
@property(nonatomic, strong) NSString *name;                                // 股票名称
@property(nonatomic, assign) int underlyingMarket;                          // 标的市场
@property(nonatomic, strong) NSString *underlyingCode;                      // 标的代码
@property(nonatomic, strong) NSString *underlyingName;                      // 标的名称
@property(nonatomic, assign) UPMarketOptionCPType callOrPut;                // 认购认沽类型
@property(nonatomic, assign) UPMarketOptionType optionType;                 // 期权类型
@property(nonatomic, assign) int contractMutiplierUnit;                     // 经过除权除息后后的合约单位
@property(nonatomic, assign) double exercisePrice;                          // 行权价
@property(nonatomic, assign) int endDate;                                   // 最后交易日
@property(nonatomic, assign) int exerciseDate;                              // 行权日
@property(nonatomic, assign) double preClosePrice;                          // 前收盘价
@property(nonatomic, assign) double preSettlPrice;                          // 前结算价
@property(nonatomic, assign) double limitUpPrice;                           // 涨停价
@property(nonatomic, assign) double limitDownPrice;                         // 跌停价
@property(nonatomic, assign) int roundLot;                                  // 整手数
@property(nonatomic, assign) double tickSize;                               // 最小报价单位
@property(nonatomic, assign) short tradeFlag;                               // 限制卖出开仓和买入开仓标识 （ 0：可开仓 1:限制卖出开仓和买入开仓）
@property(nonatomic, assign) short tradePhase;                              // 连续停盘标识 (0: 未连续停牌，1：连续停牌)
@property(nonatomic, assign) short expireStatus;                            // 临近到期日标识 (0:未临近到期日，1：距离到期日不足5个交易日)
@property(nonatomic, assign) short changeStatus;                            // 合约调整标识 (0:最近未做调整，1：最近5个交易日内合约发生过调整)
@property(nonatomic, assign) int leftDays;                                  // 剩余天数
@property(nonatomic, strong) NSString *contractType;                        // 所属T型报价合约类型

@end

// MARK: - UPMarketHKExData
@interface UPMarketHKExData : NSObject

@property(nonatomic, assign) double balancePrice;                            // 参考平衡价格
@property(nonatomic, assign) long long balanceVol;                           // 参考平衡量
@property(nonatomic, assign) double referencePrice;                          // 参考价格
@property(nonatomic, assign) double lowLimitPrice;                           // 最低价格限制
@property(nonatomic, assign) double highLimitPrice;                          // 最高价格限制
@property(nonatomic, assign) UPMarketHKBalanceDir direction;                 // 参考平衡价方向
@property(nonatomic, assign) long long imbalanceVol;                         // 参考平衡价的不平衡数量
@property(nonatomic, assign) double buyLowLimitPrice;                        // 买入最低价格限制
@property(nonatomic, assign) double buyHighLimitPrice;                       // 买入最高价格限制
@property(nonatomic, assign) double sellLowLimitPrice;                       // 卖出最低价格限制
@property(nonatomic, assign) double sellHighLimitPrice;                      // 卖出最高价格限制

@end

// MARK: - UPHqStockBaseHq

@interface UPHqStockBaseHq : NSObject

@property(nonatomic, assign) short setCode;                                 // 股票市场
@property(nonatomic, strong) NSString *code;                                // 股票代码
@property(nonatomic, strong) NSString *name;                                // 股票名称
@property(nonatomic, strong) NSString *longName;                            // 股票长名称
@property(nonatomic, assign) short precise;                                 // 股票精度
@property(nonatomic, assign) double nowPrice;                               // 现价
@property(nonatomic, assign) UPMarketTradeStatus tradeStatus;               // 交易状态
@property(nonatomic, assign) double netValue;                               // 净值，即每股净资产
@property(nonatomic, assign) UPMarketStockCategory category;                // 股票类别
@property(nonatomic, assign) UPMarketStockSubCategory subCategory;          // 股票子类别
@property(nonatomic, assign) NSUInteger origCategory;                       // 股票原始类别
@property(nonatomic, assign) NSUInteger origSubCategory;                    // 股票原始子类别
@property(nonatomic, assign) double yClosePrice;                            // 昨日收盘价
@property(nonatomic, strong) NSArray <UPMarketBlockInfo *> *blockInfoArray; // 对应板块信息
@property(nonatomic, strong) NSDictionary *stockTagDic;                     // 股票动态标签集合，类型和描述
@property(nonatomic, assign) BOOL isZT;                                     // 是否涨停
@property(nonatomic, assign) int boardDays;                                 // 连板天数
// MARK: - 静态数据

@property(nonatomic, assign) double peRatio;                                // 市盈率 = = 股票价格 / 上年每股税后利润
@property(nonatomic, assign) double pbRatio;                                // 市净率 = 每股市价 / 每股净资产
@property(nonatomic, assign) double totalStocks;                            // 总股本
@property(nonatomic, assign) UPMarketCoinType coinType;                     // 货种
@property(nonatomic, assign) double perStockIncome;                         // 每股收益
@property(nonatomic, assign) double circulationStocks;                      // 流通股
@property(nonatomic, assign) double totalMarketValue;                       // 总市值
@property(nonatomic, assign) double circulationMarketValue;                 // 流通值
@property(nonatomic, assign) int upNDay;                                   // 连涨天数或者连跌天数
@property(nonatomic, assign) int thisYearUpTotalDay;                       // 今年累计总涨天数
@property(nonatomic, assign) BOOL hasMarginMark;                            // 是否有融资标识
@property(nonatomic, assign) BOOL hasHKMarginMark;                          // 是否有港股融资标识
@property(nonatomic, assign) BOOL hasSecuritiesMark;                        // 是否有融券标识
@property(nonatomic, assign) UPMarketStockLabelType tagType;                // 个股标签 @Deprecated，使用tagTypes
@property(nonatomic, strong) NSArray *tagTypes;                             // 个股标签 - 所有
@property(nonatomic, assign) BOOL diffRight;                                // 同股同权标识: YES:同股同权 NO:同股不同权
@property(nonatomic, assign) BOOL isCDR;                                    // 是否CDR
@property(nonatomic, assign) BOOL isGDR;                                    // 是否GDR
@property(nonatomic, assign) BOOL isIpoPrime;                               // 是否上市初期
@property(nonatomic, assign) BOOL ipoFlag;                                  // 新股上市标识
@property(nonatomic, assign) int ipoDate;                                   // 上市日期
@property(nonatomic, assign) double ipoPrice;                               // 发行价格
@property(nonatomic, assign) double ipoStocks;                              // 发行股本
@property(nonatomic, assign) double divideTTM;                              // 股息 TTM （最近12个月股息总额/当日总股本）
@property(nonatomic, assign) double divideLFY;                              // 股息 LFY（上一财年股息总额/当日总股本）
@property(nonatomic, assign) double divideRateTTM;                          // 股息率 TTM （最近12个月股息总额/当日总市值）
@property(nonatomic, assign) double divideRateLFY;                          // 股息率 LFY（上一财年股息总额/当日总市值）
@property(nonatomic, assign) int everyHand;                                 // 每手的股数
@property(nonatomic, assign) double peRatioStatic;                          // 静态市盈率
@property(nonatomic, assign) double peRatioTTM;                             // TTM市盈率
@property(nonatomic, assign) double zdLimit;                                // 涨跌限制 - e.g. 0.2表示涨跌幅限制为20%
@property(nonatomic, assign) BOOL isDeficit;                                // 是否亏损
@property(nonatomic, assign) BOOL isProControl;                             // 是否协议控制
@property(nonatomic, assign) BOOL isSupportCYBReg;                          // 是否支持注册制创业板
@property(nonatomic, strong) NSArray <UPMarketRelationStockInfo *> *relationStockArray; // 关联股票
@property(nonatomic, assign) double zczb;                                   // 注册资本
@property(nonatomic, strong) NSString *hyBlockCode;                         // 行业板块代码
@property(nonatomic, strong) NSString *hyBlockName;                         // 行业板块名称
@property(nonatomic, assign) BOOL isStFlag;                                 // 是否ST股票
@property(nonatomic, assign) int endDate;                                   // 结束日期（可转债到期日）
@property(nonatomic, copy) NSString *relatedBlockId;                        // 获取指数成分股关联的板块ID,比如新三板做市指数的成分股对应的板块id：2060005412
@property(nonatomic, assign) double perStockNetAsset;                       // 每股净资产
@property(nonatomic, assign) double netAssetPRatio;                         // 净资产收益率
@property(nonatomic, assign) double revenueGrowthRatio3Y;                   // 3年营收增长率
@property(nonatomic, assign) double netProfitGrowthRatio3Y;                 // 3年净利润增长率
@property(nonatomic, assign) BOOL hkVCM;                                    // 是否参与市场波动调节机制
@property(nonatomic, assign) BOOL hkCAS;                                    // 参与收市竞价标识
@property(nonatomic, assign) BOOL hkPOS;                                    // 参与开市竞价标识
@property(nonatomic, assign) double hkOpenLowLimitPrice;                    // 开盘最低价格限制
@property(nonatomic, assign) double hkdOpenHighLimitPrice;                  // 开盘最高价格限制
/// 港股报价单位规则，0：未知，1：Part A，3：Part B，4：Inline Warrant，5：Part D
@property (nonatomic, assign) int hkshtSpreadTableCode;
/// 证券最小交易单位
@property (nonatomic, assign) int hkiLotSize;
/// 是否允许卖空
@property (nonatomic, assign) BOOL hkbShortSell;
/// 是否中央结算
@property (nonatomic, assign) BOOL hkbCCASS;
/// 是否为虚拟证券
@property (nonatomic, assign) BOOL hkbDummy;
/// 是否有印花税
@property (nonatomic, assign) BOOL hkbStampDuty;

///////////////////债券特有数据///////////////////
/// 是否有融资标志
@property (nonatomic, assign) BOOL hkbEFN;
/// 应计利息，3位小数
@property (nonatomic, assign) double hkdAccruedInterest;
/// 债券票面利率，3位小数
@property (nonatomic, assign) double hkdCouponRate;

///////////////////权证和结构性产品特有数据///////////////////
/// 结构性产品转换率，3位小数
@property (nonatomic, assign) double hkdConversionRatio;
/// 行权价，若strikePrice2不为0，则表示较低行权价，3位小数
@property (nonatomic, assign) double hkdStrikePrice1;
/// 较高行权价，若只有一个行权价时，该值为0，3位小数
@property (nonatomic, assign) double hkdStrikePrice2;
/// 到期日期，YYYYMMDD
@property (nonatomic, assign) int hkiMaturityDate;
/// 认购认沽/牛熊证标识，若为衍生权证（C：认购，P：认沽，O：其它），若为ELI或CBBC（C：牛证，P：熊证/Rang）
@property (nonatomic, assign) int hkcallPutFlag;
/// 权证样式，A：美式，E：欧式，<空白>：其它
@property (nonatomic, assign) int hkstyle;
/// 权证类型，N：普通，X：奇异，'0'：不可用
@property (nonatomic, assign) int hkwarrantType;
/// 牛熊证报价，为0时不可用
@property (nonatomic, assign) int hkiCallPrice;
/// 牛熊证报价小数位数，callPrice为0时不可用
@property (nonatomic, assign) int hkshtDecimalInCallPrice;
/// 权利，为0时不可用
@property (nonatomic, assign) int hkiEntitlement;
/// 权利小数位数，entitlement为0时不可用
@property (nonatomic, assign) int hkshtDecimalInEntitlement;
/// 每份权利中权证数量，entitlement为0时不可用
@property (nonatomic, assign) int hkiWarrantPerEntitlementNum;
/// 退市整理首日
@property(nonatomic, assign) BOOL isTSFlag;
/// 除权除息状态
@property (nonatomic, assign) UPMarketCQCXStatus cqcxStatus;
/// 做市商数量
@property (nonatomic, assign) NSInteger marketMakeCount;
/// 回售标志，该证券是否处于回售期：false-否，true-是
@property(nonatomic, assign) BOOL isSellBack;
/// 转股标志，该证券是否允许转股：false-禁止，true-允许
@property(nonatomic, assign) BOOL isTransferShare;
/// 转股价格
@property(nonatomic, assign) double convStockPrice ;
/// 可转债的转股截止日
@property(nonatomic, assign)  int convStockEndDate ;
/// 北交所和新三板交易类型
@property(nonatomic, assign)  UPMarketBJXSBTradeType tradeType;
/// 可转债
@property(nonatomic, assign)  double sellBackPrice;
/// 可转债到期赎回价
@property(nonatomic, assign)  double redemptionPrice;
/// 可转债规模
@property(nonatomic, assign)  double issueAmount;
/// 可转债剩余规模
@property(nonatomic, assign)  double remainAmount;
/// 可转债评级
@property(nonatomic, copy)  NSString *bondRating;
/// 是否为债券现券
@property(nonatomic, assign)  BOOL isZQXQ;
/// 交易精度
@property(nonatomic, assign)  int tradePrecise;
/// 是否B转H股
@property(nonatomic, assign) BOOL isBHStock;
/// 强赎触发价
@property (assign, nonatomic) double forceRedemptionTriggerPrice;
/// 是否注册制
@property (assign, nonatomic) BOOL isRegister;
/// 价格档位
@property (assign, nonatomic) double minPriceChange;
/// 回售触发价
@property (assign, nonatomic) double sellBackTriggerPrice;
@end

// MARK: - UPHqOptStockPrePostInfo (自选行情美股盘前盘后数据)
@interface UPHqOptStockPrePostInfo : NSObject
@property (nonatomic, assign) int status; // 1:盘前 2:盘后
@property (nonatomic, assign) double nowPrice;
@property (nonatomic, assign) double changeRadio;
@property (nonatomic, assign) double ChangeValue;
@end


// MARK: - UPHqStockHq

@interface UPHqStockHq : UPHqStockBaseHq

- (instancetype)initWithSetCode:(UPMarketSetCode)setCode code:(NSString *)code;

/// 开盘价
@property(nonatomic, assign) double openPrice;
/// 最高价
@property(nonatomic, assign) double highPrice;
/// 最低价
@property(nonatomic, assign) double lowPrice;
/// 涨跌金额 = (最新价－昨收价)
@property(nonatomic, assign) double changeValue;
/// 涨跌幅 = (最新价－昨收价) / 昨收价 * 100%
@property(nonatomic, assign) double changeRatio;
/// 成交量，也称总手
@property(nonatomic, assign) long long dealVol;
/// 现手
@property(nonatomic, assign) long long nowVol;
/// 现手买卖方向 0-外盘（卖） 1-内盘（买）
@property(nonatomic, assign) int nowVolBSFlag;
/// 成交额
@property(nonatomic, assign) double dealAmount;
/// 持仓量（期货）
@property(nonatomic, assign) long long holdVol;
/// 昨日结算价
@property(nonatomic, assign) double ySettlementPrice;
/// 均价
@property(nonatomic, assign) double avgPrice;
/// 涨停价
@property(nonatomic, assign) double limitUpPrice;
/// 跌停价
@property(nonatomic, assign) double limitDownPrice;
/// 换手率 = 某一段时期内的成交量 / 发行总股数 * 100%
@property(nonatomic, assign) double turnoverRate;
/// 量比 = 成交量 / (五日均量 * 开盘分钟数)
@property(nonatomic, assign) double volRatio;
/// 涨速
@property(nonatomic, assign) double upSpeed;
/// 涨跌停标志：1正常交易 2涨停 3跌停
@property(nonatomic, assign) char limitUpDownMark;
/// 交易日YYYYMMDD
@property(nonatomic, assign) int tradeDate;
/// 交易时间HHMMSS
@property(nonatomic, assign) int tradeTime;
/// 零点以来的分钟数
@property(nonatomic, assign) int minutes;
/// 内盘
@property(nonatomic, assign) long long insideVol;
/// 外盘
@property(nonatomic, assign) long long outsideVol;
/// 港股牛熊证类型
@property(nonatomic, assign) UPMarketHKWarrantType warrantType;
/// 开盘成交额
@property(nonatomic, assign) double openAmount;
/// 开盘成交量
@property(nonatomic, assign) double openVolume;
/// 美股盘前盘后数据
@property (strong, nonatomic) UPHqOptStockPrePostInfo *prePostInfo;

//板块类股票相关信息
/// 板块ID
@property(nonatomic, strong) NSString *blockId;
/// 上涨家数 (深沪市场独有)
@property(nonatomic, assign) int riseCount;
/// 下跌家数 (深沪市场独有)
@property(nonatomic, assign) int fallCount;
/// 持平家数 (深沪市场独有)
@property(nonatomic, assign) int equalCount;
/// 涨停家数
@property(nonatomic, assign) int ztCount;
/// 领涨股名称
@property(nonatomic, strong) NSString *headName;
/// 领涨股市场
@property(nonatomic, assign) short headSetCode;
/// 领涨股代码
@property(nonatomic, strong) NSString *headCode;
/// 领涨股现价
@property(nonatomic, assign) double headNowPrice;
/// 领涨股涨跌额
@property(nonatomic, assign) double headChangeValue;
/// 领涨股涨跌幅
@property(nonatomic, assign) double headChangeRatio;
/// 振幅 = (最高价 - 最低价) / 昨收价 * 100%
@property(nonatomic, assign) double swingRatio;
/// 连续上涨天数
@property(nonatomic, assign) NSUInteger upDays;
/// 股票个数
@property(nonatomic, assign) NSUInteger stockNum;
/// 仓差
@property(nonatomic, assign) double openInterestDiff;
/// 内在价值
@property(nonatomic, assign) double intrinsicValue;
/// 溢价率
@property(nonatomic, assign) double premiumRatio;
/// 隐含波动率
@property(nonatomic, assign) double impliedVolatility;
/// 杠杆率
@property(nonatomic, assign) double leverageRatio;

/// 扩展数据
@property(nonatomic, strong) UPHqStockHqExtraData *extraData;
/// 资金流数据
@property(nonatomic, strong) UPHqStockHqMoneyFlowData *moneyFlowData;
/// 新三板数据
@property(nonatomic, strong) UPMarketXSBData* xsbData;
/// 债券特有数据
@property(nonatomic, strong) UPMarketZQExData* zqExData;
/// 个股期权数据
@property(nonatomic, strong) UPMarketOptionBaseData *optionData;
/// 简版当日分时数据--自选接口返回
@property(nonatomic, strong) UPHqRTMinData *minuteData;
/// 港股扩展数据
@property(nonatomic, strong) UPMarketHKExData *hkExInfo;

@end

// MARK: - UPHqRTMinData

@interface UPHqMinuteItem : NSObject

@property(nonatomic, assign) short minutes;                               // 零点以来的分钟数
@property(nonatomic, assign) double nowPrice;                             // 现价,现指数
@property(nonatomic, assign) double avgPrice;                             // 均价,动态结算价(期货),不含加权的指数
@property(nonatomic, assign) unsigned int nowVol;                         // 现手,现成交金额(100元)
@property(nonatomic, assign) unsigned int buyVol;                         // 买量,上涨家数
@property(nonatomic, assign) unsigned int sellVol;                        // 卖量,下跌家数
@property(nonatomic, assign) double dealAmount;                           // 成交额
@property(nonatomic, assign) unsigned int holdVol;                        // 持仓量(期货)
@property(nonatomic, assign) BOOL isAfterData;                            // 是否盘后交易
@property(nonatomic, assign) int date;                                    // 日期
@property(nonatomic, assign) double lead;                                    // 领先指标

@end

@interface UPHqRTMinData : NSObject

@property(nonatomic, assign) long date;                                   // 日期（用于日线，格式：YYYYMMDD）
@property(nonatomic, assign) long endDate;                                // 结束日期,对于跨天的处理（格式：YYYYMMDD）
@property(nonatomic, assign) BOOL isDateChanged;                          // 是否跨天
@property(nonatomic, assign) double yClosePrice;                          // 昨收价
@property(nonatomic, strong) NSArray <UPHqMinuteItem *> *minuteArray;     // 分时列表

@end


// MARK: - UPHqTransData

@interface UPHqTransData : NSObject

@property(nonatomic, assign) long long time;                              // 时间 (格式：HHMMSS)
@property(nonatomic, assign) double price;                                // 价位
@property(nonatomic, assign) long long dealVol;                           // 成交量
@property(nonatomic, assign) short inOutFlag;                             // 内外盘标志，0中性盘，1内盘(主卖)，2外盘(主买)
@property(nonatomic, assign) short tradeType;                             // 成交类型，港股类型：0-自动对盘，4-开市前成交盘，22-非自动对盘，100-同一证券商自动对盘，101-同一证券商非自动对盘，102-碎股交易，103-竞价交易，104-海外交易
@property(nonatomic, assign) long buyNo,sellNo;                           // 买单号，卖单号
@property(nonatomic, copy) NSString *index;                               //上证云索引

@end

// MARK: - UPHqAnalyData

@interface UPHqAnalyData : NSObject

@property(nonatomic, assign) int date;                                    // 日期YYYYMMDD(日线)
@property(nonatomic, assign) short minutes;                               // 零点以来的分钟数
@property(nonatomic, assign) double openPrice;                            // 单位开盘价
@property(nonatomic, assign) double highPrice;                            // 单位最高价
@property(nonatomic, assign) double lowPrice;                             // 单位最低价
@property(nonatomic, assign) double closePrice;                           // 单位收盘价
@property(nonatomic, assign) double dealAmount;                           // 单位成交金额(日线/指数)
@property(nonatomic, assign) long long dealVol;                           // 成交量，也称总手（股数）
@property(nonatomic, assign) double settlementPrice;                      // 今日结算价
@property(nonatomic, assign) long long afterDealVol;                      // 盘后成交量
@property(nonatomic, assign) double afterDealAmount;                      // 盘后成交额
@property(nonatomic, assign) long long afterDealNum;                      // 盘后成交笔数
@property(nonatomic, assign) float turnoverRate;                          // 换手率
@property(nonatomic, assign) float swingRatio;                            // 振幅

@end

// MARK: - UPHqQXData

@interface UPHqQXChgData : NSObject

@property (nonatomic, assign) long changeDate;                            // 变更日期
@property (nonatomic, assign) double cashDividend;                        // 每10股派现
@property (nonatomic, assign) double bonusShare;                          // 每10股送股
@property (nonatomic, assign) double capitalShare;                        // 每10股转增
@property (nonatomic, assign) double allotPct;                            // 10配几
@property (nonatomic, assign) double allotPrice;                          // 配股价
@property (nonatomic, assign) double issuePrice;                          // 增发价格
@property (nonatomic, assign) double issueShareNum;                       // 发行数量

@end

@interface UPHqQXData : NSObject

@property (nonatomic, assign) UPMarketSetCode setCode;                              // 市场代码
@property (nonatomic, strong) NSString *code;                                       // 股票代码
@property (nonatomic, strong) NSDictionary<NSNumber *, UPHqQXChgData *> *chgData;   // 权息变动数据 (key:日期, value:UPHqQXChgData)

@end

// MARK: - UPHqTickData

@interface UPHqTickData : NSObject

@property(nonatomic, assign) short minutes;                                // 零点以来的分钟数
@property(nonatomic, assign) int time;                                    // hhmmss时间
@property(nonatomic, assign) double nowPrice;                              // 现价
@property(nonatomic, assign) unsigned int nowVol;                          // 现手
@property(nonatomic, assign) short inOutFlag;                              // 买卖标志（股票：0-买，1-卖）（期货：0-双开，1-多开，2-空开，3-双平，4-多平，5-空平，6-多换，7-空换）

@end

//MARK: - HQSysHSouthMoneyFlow

@interface UPHqSouthMoneyFlow : NSObject
@property (nonatomic, assign) int date;
@property (nonatomic, assign) long long time;
@property (nonatomic, assign) double totalAmt;
@property (nonatomic, assign) double balance;

@end

//MARK: - UPHqCapitalFlowData

@interface UPHqCapitalFlowData : NSObject

@property(nonatomic, assign) double superIn;                               // 超大宗入（>=50万股或者100万元）
@property(nonatomic, assign) double superOut;                              // 超大宗出
@property(nonatomic, assign) double bigIn;                                 // 大宗入（>=10万股或者20万元）
@property(nonatomic, assign) double bigOut;                                // 大宗出
@property(nonatomic, assign) double midIn;                                 // 中单入（>=2万股或者4万元）
@property(nonatomic, assign) double midOut;                                // 中单出
@property(nonatomic, assign) double smallIn;                               // 小单入（其他）
@property(nonatomic, assign) double smallOut;                              // 小单出

@end

// MARK: - UPHqOrderQueue

@interface UPHqOrderItem : NSObject

@property(nonatomic, assign) long long volume;                              // 委托量
@property(nonatomic, assign) UPMarketOrderStatus status;                    // 委托状态

@end

@interface UPHqOrderQueueData : NSObject

@property(nonatomic, assign) long long time;                                // 时间 (格式：HHMMSS)
@property(nonatomic, assign) double price;                                  // 价位
@property(nonatomic, assign) long long volume;                              // 委托总量
@property(nonatomic, assign) long long num;                                 // 委托总单数
@property(nonatomic, assign) short buySellFlag;                             // 买卖标志 (1-买，2-卖)
@property(nonatomic, strong) NSArray<UPHqOrderItem *> *orderArray;          // 委托单列表

@end

// MARK: - UPHqOrderPrice

@interface UPHqPriceItem : NSObject

@property(nonatomic, assign) double price;                                  // 价位
@property(nonatomic, assign) long long totalVol;                            // 总量
@property(nonatomic, assign) long long bigVol;                              // 大单量

@end

// MARK: - UPHqPriceOrderData

@interface UPHqPriceOrderData : NSObject

@property(nonatomic, strong) NSArray <UPHqPriceItem *> *buyPriceArray;      // 买入价位列表
@property(nonatomic, strong) NSArray <UPHqPriceItem *> *sellPriceArray;     // 卖出价位列表
@property(nonatomic, strong) UPHqPriceItem * supportItem;                   // 支撑位
@property(nonatomic, strong) UPHqPriceItem * resistanceItem;                // 阻力位

@end


// MARK: - UPMarketPriceVolData
@interface UPMarketPriceVolItem : NSObject
@property(nonatomic, assign) double price;                  // 价格
@property(nonatomic, assign) long long volme;               // 量(股数)
@property(nonatomic, assign) long long buyVolme,sellVolme;  // 买卖量(股数)
@end

// MARK: - UPMarketPriceVolStats
@interface UPMarketPriceVolStats : NSObject
@property (nonatomic, assign) double avgPrice;              // 平均成交价
@property (nonatomic, assign) long long tradeNum;           // 总成交笔数
@property (nonatomic, assign) long long volme;              // 总成交量
@property (nonatomic, assign) long long buyVol,sellVol;     // 主买（股）; 主卖（股）
@end

@interface UPMarketPriceVolData : NSObject
@property(nonatomic, strong) NSArray <UPMarketPriceVolItem *>* priceVolArray;                   // 价量分布列表
@property(nonatomic, strong) UPMarketPriceVolItem *maxPriceVolItem;                             // 成交量最高价量
@property (nonatomic, strong) UPMarketPriceVolStats *transStats;                                // 统计数据
@property (nonatomic, strong) NSDictionary<NSNumber *, UPMarketPriceVolItem *> *priceItemDic;   // 价格->成交统计

@end

// MARK: - UPMarketChipData

@interface UPMarketChipPriceItem : NSObject
@property(nonatomic, assign) double price;               // 价格
@property(nonatomic, assign) double volme;               // 量(股数)
@end

@interface UPMarketChipItem: NSObject

@property(nonatomic, assign) double avgCost;                                        // 平均成本
@property(nonatomic, assign) double profitRatio;                                    // 获利比例
@property(nonatomic, assign) double concentrationRatio;                             // 集中度
@property(nonatomic, assign) double supportPrice;                                   // 支撑位
@property(nonatomic, assign) double pressurePrice;                                  // 压力位
@property(nonatomic, strong) NSArray <UPMarketChipPriceItem *>* priceVolArray;       // 筹码分布

@end

@interface UPMarketChipSimpleData : NSObject

@property (nonatomic, strong) NSArray<UPMarketChipPriceItem *> *priceVolArray;       // 筹码分布

@end

@interface UPMarketChipData: NSObject

@property(nonatomic, assign) int date;                                              // 日期
@property(nonatomic, assign) double closePrice;                                     // 收盘价(现价)
@property(nonatomic, strong) UPMarketChipItem *mainChip;                            // 主力筹码
@property(nonatomic, strong) UPMarketChipItem *allChip;                             // 全部筹码
@property (nonatomic, strong) UPMarketChipSimpleData *gainData;                     // 盈利筹码
@property (nonatomic, strong) UPMarketChipSimpleData *lossData;                     // 套牢筹码
@property (nonatomic, assign) float beginRegion90;                                  // 90% 成本起点
@property (nonatomic, assign) float endRegion90;                                    // 90% 成本终点
@property (nonatomic, assign) float beginRegion70;                                  // 70% 成本起点
@property (nonatomic, assign) float endRegion70;                                    // 70% 成本终点
@property (nonatomic, assign) float concenRatio90;                                  // 90% 集中度
@property (nonatomic, assign) float concenRatio70;                                  // 70% 集中度

@end

// MARK: - UPMarketCodeMatchInfo

@interface UPMarketCodeMatchInfo : NSObject

@property(nonatomic, assign) UPMarketCodeMatchType matchType;               // 匹配类型
@property(nonatomic, assign) NSInteger matchStart;                          // 匹配起始位置
@property(nonatomic, assign) NSInteger matchEnd;                            // 匹配结束位置
@property(nonatomic, assign) NSInteger matchPercent;                        // 匹配程度：避免使用float影响效率，取值范围0~100
@property(nonatomic, assign) short setCode;                                 // 股票市场
@property(nonatomic, strong) NSString *code;                                // 股票代码
@property(nonatomic, strong) NSString *name;                                // 股票名称
@property(nonatomic, assign) UPMarketStockCategory category;                // 股票类别
@property(nonatomic, assign) NSUInteger origCategory;                       // 股票原始类别
@property(nonatomic, assign) int everyHand;                                 // 每手的股数
@property(nonatomic, assign) short precise;                                 // 股票精度
@property(nonatomic, assign) UPMarketTradeStatus status;                    // 股票状态
@property(nonatomic, assign) UPMarketStockSubCategory subCategory;          // 股票子类别
@property(nonatomic, assign) NSUInteger origSubCategory;                    // 股票原始子类别
@property(nonatomic, assign) UPMarketNameFlag nameFlag;                     // 名称标识,用来标识长名称,现用名,曾用名

@end

// MARK: - UPMarketSignalInfo

@interface UPMarketSignalInfo : NSObject

@property(nonatomic, assign) UPMarketSetCode setCode;                       // 市场码
@property(nonatomic, strong) NSString *code;                                // 股票代码
@property(nonatomic, strong) NSString *name;                                // 股票名称
@property(nonatomic, assign) double nowPrice;                               // 现价
@property(nonatomic, assign) double changeRatio;                            // 涨跌幅 = 涨跌额 / 昨收价 * 100%
@property(nonatomic, assign) UPMarketStockCategory category;                // 股票类别

@end

// MARK: - UPMarketLevel2PoolUnitData

@interface UPMarketLevel2PoolUnitData : NSObject

@property(nonatomic, assign) int dealCount;                                 // 次数
@property(nonatomic, assign) long long dealVol;                             // 总量
@property(nonatomic, assign) double dealAmount;                             // 总额

@end

// MARK: - UPMarketLevel2Pool

@interface UPMarketLevel2PoolInfo : NSObject

@property(nonatomic, assign) UPMarketSetCode setCode;                       // 市场码
@property(nonatomic, strong) NSString *code;                                // 股票代码
@property(nonatomic, strong) NSString *name;                                // 股票名称
@property(nonatomic, assign) double nowPrice;                               // 现价
@property(nonatomic, assign) double changeRatio;                            // 涨跌幅 = 涨跌额 / 昨收价 * 100%
@property(nonatomic, strong) UPMarketLevel2PoolUnitData *buyUnitData;       // 买单信息
@property(nonatomic, strong) UPMarketLevel2PoolUnitData *sellUnitData;      // 卖单信息

@end

// MARK: - UPMarketDDE

@interface UPMarketDDEInfo : NSObject

@property(nonatomic, assign) long long time;                                // 分时 - 零点以来的分钟数；K线 - 日期：秒时间戳
@property(nonatomic, assign) long long date;                                // 分时 - 零点以来的分钟数；K线 - 日期：秒时间戳
@property(nonatomic, strong) UPHqCapitalFlowData *amountItem;               // 成交额信息
@property(nonatomic, strong) UPHqCapitalFlowData *volItem;                  // 成交量信息
@property(nonatomic, strong) UPHqCapitalFlowData *numItem;                  // 成交单数信息

@end

// MARK: - UPMarketIndexWXCP

@interface UPMarketIndexWXCP : NSObject

@property(nonatomic, assign) NSInteger starLevel;                           // 五星级别
@property(nonatomic, assign) BOOL hasB;                                     // 是否出现买点
@property(nonatomic, assign) BOOL hasS;                                     // 是否出现卖点

@end

// MARK: - UPMarketIndexCPXHD

@interface UPMarketIndexCPXHD : NSObject

@property(nonatomic, assign) double lowLine;                                // 低位线
@property(nonatomic, assign) double highLine;                               // 高位线
@property(nonatomic, assign) double yellowLight;                            // 黄灯等待
@property(nonatomic, assign) double redLight;                               // 红灯看涨
@property(nonatomic, assign) double greenLight;                             // 绿灯看跌
@property(nonatomic, assign) double dxBuy;                                  // 短线买入
@property(nonatomic, assign) double dxSell;                                 // 短线卖出
@property(nonatomic, assign) double var1;                                   // 绘图辅助变量
@property(nonatomic, assign) double qs;                                     // 趋势
@property(nonatomic, assign) double qsdx;                                   // 趋势底线

@end

// MARK: - UPMarketIndexDJWTD

@interface UPMarketIndexDJWTD : NSObject

@property(nonatomic, assign) double tj;                                     // 天际
@property(nonatomic, assign) double hd;                                     // 海底
@property(nonatomic, assign) double mmjc;                                   // 买卖决策①
@property(nonatomic, assign) double hclp2;                                  // 红持绿抛②
@property(nonatomic, assign) double hclp3;                                  // 红持绿抛③

@end

// MARK: - UPMarketIndexXSBDW

@interface UPMarketIndexXSBDW : NSObject

@property(nonatomic, assign) BOOL hasB;                                     // 是否出现买点
@property(nonatomic, assign) BOOL hasS;                                     // 是否出现卖点

@end

// MARK: - UPMarketIndexZLT0

@interface UPMarketIndexZLT0 : NSObject

@property(nonatomic, assign) double up;                                     // 上
@property(nonatomic, assign) double down;                                   // 下
@property(nonatomic, assign) BOOL hasB;                                     // 是否出现买点
@property(nonatomic, assign) BOOL hasS;                                     // 是否出现卖点

@end

// MARK: - UPMarketIndexQKJCY

@interface UPMarketIndexQKJCY : NSObject

@property(nonatomic, assign) BOOL hasB;                                     // 是否出现买点
@property(nonatomic, assign) BOOL hasS;                                     // 是否出现卖点
@property(nonatomic, assign) int starLevel;                                 // 五星级别

@end

// MARK: - UPMarketIndexZJDL

@interface UPMarketIndexZJDL : NSObject

@property(nonatomic, assign) double value;                                  // 动力综合值
@property(nonatomic, assign) double strong;                                 // 资金动力强
@property(nonatomic, assign) double middle;                                 // 资金动力中
@property(nonatomic, assign) double weak;                                   // 资金动力中

@end

// MARK: - UPMarketIndexXSRTTP

@interface UPMarketIndexXSRTTP : NSObject

@property(nonatomic, assign) BOOL hasPBQY;                                   // 是否出现平步青云信号
@property(nonatomic, assign) BOOL hasQTCH;                                   // 是否出现潜艇出海信号

@end

// MARK: - UPMarketIndexDPYLZC

@interface UPMarketIndexDPYLZC : NSObject

@property(nonatomic, assign) double ylw;                                   // 压力位
@property(nonatomic, assign) double zcw;                                   // 支撑位

@end

// MARK: - UPMarketIndexJZXL
@interface UPMarketIndexJZXL : NSObject

@property(nonatomic, assign) BOOL hasB;                                  // 是否出现低九买入信号
@property(nonatomic, assign) BOOL hasS;                                  // 是否出现高九卖出信号
@property(nonatomic, assign) int dayNum;                                 // 出现天数

@end

// MARK: - UPMarketIndexDDLDJ
@interface UPMarketIndexDDLDJ : NSObject
@property(nonatomic, assign) double segment;                                // 段
@property(nonatomic, assign) double wave;                                   // 波
@property(nonatomic, assign) BOOL hasCD;                                    // 抄底信号
@property(nonatomic, assign) BOOL hasTD;                                    // 逃顶信号

@end

// MARK: - UPMarketIndexDDTJ
@interface UPMarketIndexDDTJ : NSObject

@property(nonatomic, assign) BOOL normalB;                                  // 普通B点
@property(nonatomic, assign) BOOL selectB;                                  // 精选B点

@end

// MARK: - UPMarketIndexDDCM
@interface UPMarketIndexDDCM : NSObject

@property(nonatomic, assign) double buyChip;                                // 买入筹码
@property(nonatomic, assign) double sellChip;                               // 卖出筹码
@property(nonatomic, assign) BOOL many;                                     // 多
@property(nonatomic, assign) BOOL empty;                                    // 空
@property(nonatomic, assign) double balanceCtrl;                            // 平衡控盘
@property(nonatomic, assign) double zlqc;                                   // 主力抢筹
@property(nonatomic, assign) double psqc;                                   // 抛售抢筹

@end

// MARK: - UPMarketIndexDDDN
@interface UPMarketIndexDDDN : NSObject

@property(nonatomic, assign) double redWaveTrend;                           // 红波趋势
@property(nonatomic, assign) double blueWaveTrend;                          // 蓝波趋势
@property(nonatomic, assign) double up;                                     // 升
@property(nonatomic, assign) double down;                                   // 降

@end


// MARK: - UPMarketIndexXBLD
@interface UPMarketIndexXBLD : NSObject

@property(nonatomic, assign) double xb;                                     // 谐波
@property(nonatomic, assign) double xbcd;                                   // 谐波抄底
@property(nonatomic, assign) int lineType;                                  // 类型

@end

// MARK: - UPMarketIndexFJLD
@interface UPMarketIndexFJLD : NSObject

@property(nonatomic, assign) double yj;                                     // 预警
@property(nonatomic, assign) double cp;                                     // 操盘
@property(nonatomic, assign) double qs;                                     // 趋势
@property(nonatomic, assign) double hjk;                                    // 黄金坑
@property(nonatomic, assign) BOOL hasB;                                     // 买入信号

@end

// MARK: - UPMarketIndexZLZD
@interface UPMarketIndexZLZD : NSObject

@property(nonatomic, assign) int zlzd;                                     // 主力阵地
@property(nonatomic, assign) double jm;                                    // 净买
@property(nonatomic, assign) double zdzjxc;                                // 主动追价吸筹
@property(nonatomic, assign) double jsdydx;                                // 借势打压低吸

@end

// MARK: - UPMarketIndexMainDDLD

@interface UPMarketIndexMainDDLD : NSObject

@property(nonatomic, assign) double gp;                                    // 高抛
@property(nonatomic, assign) double dx;                                    // 低吸
@property(nonatomic, assign) BOOL gpPoint;                                 // 高抛点
@property(nonatomic, assign) BOOL dxPoint;                                 // 低吸点

@end

// MARK: - UPMarketIndexLTJJBS
@interface UPMarketIndexLTJJBS : NSObject

@property(nonatomic, assign) BOOL hasB;                                     // B点
@property(nonatomic, assign) BOOL hasS;                                     // S点
@property(nonatomic, assign) int star;                                      // 星级

@end

// MARK: - UPMarketIndexHHBBS
@interface UPMarketIndexHHBBS : NSObject

@property(nonatomic, assign) BOOL hasB;                                     // B点
@property(nonatomic, assign) BOOL hasS;                                     // S点

@end

// MARK: - UPMarketIndexZTKLine
@interface UPMarketIndexZTKLine : NSObject

@property(nonatomic, assign) BOOL hasB;                                     // B点
@property(nonatomic, assign) BOOL hasS;                                     // S点
@property(nonatomic, assign) UPMarketZTKLineType kLineType;                 // 涨停K线类型

@end

// MARK: - UPMarketIndexBDKX
@interface UPMarketIndexBDKX : NSObject

@property(nonatomic, assign) double value;                                  // 波段值

@end

// MARK: - UPMarketIndexInfo

@interface UPMarketIndexInfo : NSObject

@property(nonatomic, assign) NSInteger date;                                // 日期（格式：YYYYMMDD）
@property(nonatomic, assign) short minutes;                                 // 零点以来的分钟数
@property(nonatomic, strong) UPMarketIndexWXCP *wxcp;                       // 五星操盘数据
@property(nonatomic, strong) UPMarketIndexCPXHD *cpxhd;                     // 操盘信号灯数据
@property(nonatomic, strong) UPMarketIndexDJWTD *djwtd;                     // 点金王通道数据
@property(nonatomic, strong) UPMarketIndexXSBDW *xsbdw;                     // 晓胜波段王数据
@property(nonatomic, strong) UPMarketIndexZLT0 *zlt0;                       // 主力T+0数据
@property(nonatomic, strong) UPMarketIndexQKJCY *qkjcy;                     // 乾坤决策眼数据
@property(nonatomic, strong) UPMarketIndexZJDL *zjdl;                       // 资金动力
@property(nonatomic, strong) UPMarketIndexXSRTTP *xsrttp;                   // 晓胜分时图谱
@property(nonatomic, strong) UPMarketIndexDPYLZC *dpylzc;                   // 大盘K线压力支撑指标
@property(nonatomic, strong) UPMarketIndexJZXL *jzxl;                       // 九转序列指标
@property(nonatomic, strong) UPMarketIndexDDLDJ *ddldj;                     // 顶底雷达机
@property(nonatomic, strong) UPMarketIndexDDTJ *ddtj;                       // 短打突击
@property(nonatomic, strong) UPMarketIndexDDCM *ddcm;                       // 短打筹码
@property(nonatomic, strong) UPMarketIndexDDDN *dddn;                       // 短打动能
@property(nonatomic, strong) UPMarketIndexXBLD *xbld;                       // 谐波雷达
@property(nonatomic, strong) UPMarketIndexFJLD *fjld;                       // 伏击雷达
@property(nonatomic, strong) UPMarketIndexZLZD *zlzd;                       // 主力阵地
@property(nonatomic, strong) UPMarketIndexMainDDLD *mainDDLD;               // 主图顶底雷达
@property(nonatomic, strong) UPMarketIndexLTJJBS *ltjj;                     // 龙头狙击BS点
@property(nonatomic, strong) UPMarketIndexHHBBS *hhb;                       // 红黄白BS点
@property(nonatomic, strong) UPMarketIndexZTKLine *ztKLine;                 // 涨停K线
@property(nonatomic, strong) UPMarketIndexBDKX *bdkx;                       // 波段K线
@property (copy, nonatomic) NSDictionary *otherData;                        // 其他类型数据
@end

@interface UPMarketIndexZTJBData : NSObject

@property(nonatomic, assign) NSInteger rxsj;                                // 入选时间：零点以来的分钟数
@property(nonatomic, assign) double rxjg;                                   // 入选价格
@property(nonatomic, assign) double drzf;                                   // 当日涨幅
@property(nonatomic, assign) double crzf;                                   // 次日涨幅
@property(nonatomic, assign) UPMarketZTJBStatus status;                     // 涨停阶段

@end

// MARK: - UPMarketIndexWXGP

@interface UPMarketIndexWXGP : NSObject

@property(nonatomic, assign) double maxRise;                                 // 最大涨幅
@property(nonatomic, assign) double nowRise;                                 // 当日涨幅
@property(nonatomic, assign) int signalContinue;                             // 信号持续时长

@end

// MARK: - UPMarketIndexRGTJ

@interface UPMarketIndexRGTJ : NSObject
@property(nonatomic, assign) double nowPrice;                                // 现价
@property(nonatomic, assign) double nowRise;                                 // 当日涨幅
@property(nonatomic, assign) UPMarketHotStockType hotStockType;              // 热股类型
@property(nonatomic, copy) NSString *tag;                                    // 热股描述

@end

// MARK: - UPMarketIndexHYSZ

@interface UPMarketIndexHYSZ : NSObject
@property(nonatomic, assign) double nowPrice;                                // 现价
@property(nonatomic, assign) double nowRise;                                 // 涨幅
@property(nonatomic, assign) int xl;                                         // 蓄力
@property(nonatomic, assign) int qd;                                         // 强度
@property(nonatomic, assign) double xg;                                      // 效果
@end

// MARK: - UPMarketIndexZJXF

@interface UPMarketIndexZJXF : NSObject
@property(nonatomic, assign) double nowPrice;                                // 现价
@property(nonatomic, assign) double nowRise;                                 // 涨幅
@property(nonatomic, assign) double zjqd;                                    // 资金强度
@property(nonatomic, assign) double zlbl;                                    // 主力比例
@property(nonatomic, assign) double shbl;                                    // 散户比例
@end


// MARK: - UPMarketIndexSDTJ

@interface UPMarketIndexSDTJ : NSObject
@property(nonatomic, assign) double nowPrice;                                // 现价
@property(nonatomic, assign) double nowRise;                                 // 涨幅
@property(nonatomic, assign) int time;                                       // 提示时间
@property(nonatomic, assign) int signalStatus;                               // 信号状态：1-拉升预警/跳水预警， 2 火箭发射/高台跳水
@property(nonatomic, assign) int signalCount;                                // 提示次数
@property(nonatomic, assign) int speed;                                      // 突击速度
@property(nonatomic, assign) int cjqd;                                       // 成交强度
@property(nonatomic, assign) int tjnl;                                       // 突击能力
@property(nonatomic, assign) int tjzl;                                       // 突击阻力
@end


// MARK: - UPMarketIndexCLFY

@interface UPMarketIndexCLFY : NSObject
@property(nonatomic, assign) UPMarketIndexSubType signalType;                 // 信号类型
@property(nonatomic, assign) int signalStatus;                                // 信号状态：1表示未确立，2表示已确立
@property(nonatomic, assign) double nowRise;                                  // 涨幅
@property(nonatomic, assign) double yClose;                                   // 昨收
@property(nonatomic, assign) double todayMaxPrice;                            // 当日最高价
@property(nonatomic, assign) double todayMaxRise;                             // 当日最高涨幅
@property(nonatomic, assign) int time;                                        // 入选时间：HHMMSS
@end

// MARK: - UPMarketIndexZJWW
@interface UPMarketIndexZJWW : NSObject
@property(nonatomic, assign) double nowPrice;                                // 现价
@property(nonatomic, assign) double nowRise;                                 // 当日涨幅
@property(nonatomic, assign) double maxRise;                                 // 最大涨幅

@end

// MARK: - UPMarketIndexZTXN
@interface UPMarketIndexZTXN : NSObject
@property(nonatomic, assign) double nowPrice;                                // 现价
@property(nonatomic, assign) double nowRise;                                 // 当日涨幅
@property(nonatomic, assign) double maxRise;                                 // 最大涨幅

@end

// MARK: - UPMarketIndexZJB
@interface UPMarketIndexZJB : NSObject

@property(nonatomic, assign) double rxjg;                                // 入选价格
@property(nonatomic, assign) double maxRiseFiveDays;                     // 五日最高涨幅
@property(nonatomic, assign) UPMarketQNStockType stockPoolType;          // 股池类型

@end

// MARK: - UPMarketIndexZJBB
@interface UPMarketIndexZJBB : NSObject

@property(nonatomic, assign) int ztDate;                     // 涨停日期

@end

// MARK: - UPMarketIndexDBDX
@interface UPMarketIndexDBDX : NSObject

@property(nonatomic, assign) double gzjg;                           // 关注价格
@property(nonatomic, assign) double maxRiseFiveDays;                // 五日最高涨幅
@property(nonatomic, assign) double nowRise;                        // 最新涨幅

@end

// MARK: - UPMarketIndexLTJJ
@interface UPMarketIndexLTJJ : NSObject

@property(nonatomic, assign) int signalDate;                        // 入选日期
@property(nonatomic, assign) int holdDayNum;                        // 持仓天数
@property(nonatomic, assign) double signalPrice;                    // 入选价格
@property(nonatomic, assign) double rangeRise;                      // 区间涨幅

@end

// MARK: - UPMarketIndexHHB
@interface UPMarketIndexHHB : NSObject

@property(nonatomic, assign) BOOL inFlag;                           // 新调入
@property(nonatomic, assign) BOOL outFlag;                          // 新调出
@property(nonatomic, assign) int signalDate;                        // 入选日期
@property(nonatomic, assign) int holdDayNum;                        // 持仓天数
@property(nonatomic, assign) double signalPrice;                    // 入选价格
@property(nonatomic, assign) double rangeRise;                      // 区间涨幅
@property(nonatomic, assign) double gjb;                             // 攻击波
@property(nonatomic, assign) double htb;                             // 回头波
@property(nonatomic, assign) int tag;                                // 买点标签，1：趋势加速；2：弱转强（黄白）；3：弱转强（黄红）

@end

// MARK: - UPMarketIndexCDJJ
@interface UPMarketIndexCDJJ: NSObject
@property(nonatomic, assign) int signalDate;                        // 入选日期
@property(nonatomic, assign) double signalPrice;                    // 入选价格
@property(nonatomic, assign) double maxRise;                        // 最高涨幅
@property(nonatomic, copy) NSString *reason;                        // 入选理由
@end


@interface UPMarketIndexStockData : NSObject

@property(nonatomic, assign) NSInteger date;                                // 日期（格式：YYYYMMDD）
@property(nonatomic, assign) NSInteger time;                                // 时间
@property(nonatomic, assign) UPMarketSetCode setCode;                       // 市场代码
@property(nonatomic, strong) NSString *code;                                // 股票代码
@property(nonatomic, strong) NSString *name;                                // 股票名称
@property(nonatomic, assign) UPMarketIndexType type;                        // 指标类型
@property(nonatomic, strong) UPMarketIndexZTJBData *ztjbData;               // 涨停尖兵数据
@property(nonatomic, strong) UPMarketIndexWXGP *wxgp;                       // 五星股票
@property(nonatomic, strong) UPMarketIndexRGTJ *rgtj;                       // 热股推荐
@property(nonatomic, strong) UPMarketIndexHYSZ *hysz;                       // 慧眼识庄:主力攻击or主力撤退
@property(nonatomic, strong) UPMarketIndexZJXF *zjxf;                       // 资金先锋:黄金交叉or死亡交叉
@property(nonatomic, strong) UPMarketIndexSDTJ *sdtj;                       // 闪电突击:拉升or跳水
@property(nonatomic, strong) UPMarketIndexCLFY *clfy;                       // 策略风云:多赢一号/多赢二号/黄金眼/盘口杀
@property(nonatomic, strong) UPMarketIndexZJWW *zjww;                       // 资金为王
@property(nonatomic, strong) UPMarketIndexZTXN *ztxn;                       // 涨停蓄能
@property(nonatomic, strong) UPMarketIndexZJB *zjb;                         // 战绩榜
@property(nonatomic, strong) UPMarketIndexZJBB *zjbb;                       // 战绩播报
@property(nonatomic, strong) UPMarketIndexDBDX *dbdx;                       // 打板低吸
@property(nonatomic, strong) UPMarketIndexLTJJ *ltjj;                       // 龙头狙击
@property(nonatomic, strong) UPMarketIndexHHB *hhb;                         // 红黄白涨停因子
@property(nonatomic, strong) UPMarketIndexCDJJ *cdjj;                       // 超短掘金
@property(nonatomic, copy) NSDictionary *otherData;                        // 其他类型数据

@end

// MARK: - UPMarketFlowTrend

@interface UPMarketFlowTrendInfo : NSObject

@property(nonatomic, assign) double mainMoneyInlow;                         // 主力资金净流入
@property(nonatomic, assign) double mainMoneyRatio;                         // 主力资金净占比
@property(nonatomic, assign) double retailMoneyInlow;                       // 散户资金净流入
@property(nonatomic, assign) double retailMoneyRatio;                       // 散户资金净占比
@property(nonatomic, assign) double superLargeInlow;                        // 超大单资金净流入
@property(nonatomic, assign) double superLargeRatio;                        // 超大单资金净占比
@property(nonatomic, assign) double largeInlow;                             // 大单资金净流入
@property(nonatomic, assign) double largeRatio;                             // 大单资金净占比
@property(nonatomic, assign) double middleInlow;                            // 中单资金净流入
@property(nonatomic, assign) double middleRatio;                            // 中单资金净占比
@property(nonatomic, assign) double smallInlow;                             // 小单资金净流入
@property(nonatomic, assign) double smallRatio;                             // 小单资金净占比

@end

// MARK: - UPMarketFlowRank

@interface UPMarketFlowRankData : UPHqStockBaseHq
@property(nonatomic, assign) double changeRatio;                          // 涨跌幅
@property(nonatomic, assign) double changePrice;                          // 涨跌值

@property(nonatomic, strong) UPMarketFlowTrendInfo *flowTrendOne;           // 当日资金流向
@property(nonatomic, strong) UPMarketFlowTrendInfo *flowTrendThree;         // 三日资金流向
@property(nonatomic, strong) UPMarketFlowTrendInfo *flowTrendFive;          // 五日资金流向
@property(nonatomic, strong) UPMarketFlowTrendInfo *flowTrendTen;           // 十日资金流向
@property(nonatomic, strong) UPMarketFlowTrendInfo *flowTrendMinThree;      // 3分钟资金流向
@property(nonatomic, strong) UPMarketFlowTrendInfo *flowTrendMinFive;       // 5分钟资金流向
@property(nonatomic, strong) UPMarketFlowTrendInfo *flowTrendMin10;         // 10分钟资金流向
@property(nonatomic, strong) UPMarketFlowTrendInfo *flowTrendMin30;         // 30分钟资金流向
@property(nonatomic, strong) UPMarketFlowTrendInfo *flowTrendMin60;         // 60分钟资金流向

@end

// MARK: - UPMarketStatus

@interface UPMarketStatusData : NSObject

@property(nonatomic, assign) long long time;                                // 时间 (格式：YYYYMMDDHHMMSS)
@property(nonatomic, assign) UPMarketTradeStatus tradeStatus;               // 交易状态

@end


// MARK: - UPMarketBrokerQueue

@interface UPMarketBrokerItem : NSObject

@property(nonatomic, strong) NSString *id;                                  // 经纪代号
@property(nonatomic, strong) NSString *engName;                             // 英文名
@property(nonatomic, strong) NSString *engSimpleName;                       // 英文名简称
@property(nonatomic, strong) NSString *cnName;                              // 中文名
@property(nonatomic, strong) NSString *cnSimpleName;                        // 中文名简称

@end

@interface UPMarketBrokerQueueData : NSObject

@property(nonatomic, assign) UPMarketSetCode setCode;                       // 市场代码
@property(nonatomic, strong) NSString *code;                                // 股票代码
@property(nonatomic, strong) NSDictionary *buyDic;                          // 买盘经纪
@property(nonatomic, strong) NSDictionary *sellDic;                         // 卖盘经纪

@end


// MARK: - UPMarketAHStockItem

@interface UPMarketAHStockItem : NSObject

@property(nonatomic, assign) UPMarketSetCode setCode;
@property(nonatomic, strong) NSString *code;                                // 股票代码
@property(nonatomic, strong) NSString *name;                                // 股票名称
@property(nonatomic, assign) double nowPrice;                               // 现价
@property(nonatomic, assign) double changeRatio;                            // 涨跌幅 = 涨跌额 / 昨收价 * 100%
@property(nonatomic, assign) double changeValue;                            // 涨跌值
@property (assign, nonatomic) int tradeDate;
@property (assign, nonatomic) int tradeTime;

@end

// MARK: - UPMarketAHStockData

@interface UPMarketAHStockData : NSObject

@property(nonatomic, assign) double premiumRate;                            // 溢价率
@property(nonatomic, assign) double conversionPrice;                        // ADR换算价
@property(nonatomic, assign) double conversionChgValue;                     // ADR换算涨跌值
@property(nonatomic, assign) double conversionChgRatio;                     // ADR换算涨跌幅
@property(nonatomic, assign) NSInteger precise;                             // 精度
@property(nonatomic, strong) UPMarketAHStockItem *aItem;                    // A股信息
@property(nonatomic, strong) UPMarketAHStockItem *hItem;                    // H股、美股信息

@end

// MARK: - UPMarketRelatedAHStockBatchData

@interface UPMarketRelatedAHStockBatchData : NSObject

@property(nonatomic, assign) double premiumRate;

@property(nonatomic, strong) UPMarketAHStockItem *item;

@property(nonatomic, assign) NSUInteger precise;

@end

// MARK: - UPMarketMoneyRankTopData

@interface UPMarketMoneyRankTopData : UPHqStockBaseHq

@property(nonatomic, assign) double rankValue;                              // 净流入/净流出值

@end

// MARK: - 股票因子库

@interface UPMarketLHBData : NSObject

@property(nonatomic, assign) int tradeDate;         // 交易日 YYYYMMDD

@property(nonatomic, assign) int reasonCode;        // 上榜原因代码

@property(nonatomic, assign) double dealAmount;     // 成交金额 万元

@property(nonatomic, assign) double dealVol;        // 成交数量 万股

@property(nonatomic, strong) NSString *detailUrl;   // 详情URL

@end

@interface UPMarketFZTData : NSObject

@property(nonatomic, assign) int tradeDate;             // 交易日 YYYYMMDD

@property(nonatomic, assign) int firstCloseBoardTime;   // 首次封板时间

@property(nonatomic, assign) int lastCloseBoardTime;    // 最后封板时间

@property(nonatomic, assign) int openBoardNum;          // 涨停开板数

@property(nonatomic, strong) NSString *closeBoardReason;// 涨停原因

@end

@interface UPMarketStockFactorData : NSObject

@property(nonatomic, strong) NSDictionary *lhbDataDic;              // 龙虎榜数据Dictionary

@property(nonatomic, strong) NSDictionary *fztDataDic;              // 封涨停（游资）数据Dictionary

@property(nonatomic, strong) NSDictionary *bxDataDic;              // 北向资金 数据Dictionary

@end

// MARK: - 股池统计数据

@interface UPMarketStockPoolStatData : NSObject

@property(nonatomic, assign) UPMarketIndexType type;        // 类型
@property(nonatomic, assign) double avgIncome;              // 平均收益
@property(nonatomic, assign) double successRate;            // 成功率
@property(nonatomic, assign) double risk;                   // 风险
@property(nonatomic, assign) double income;                 // 收益

@end


// MARK: - UPMarketSubjectChange

@interface UPMarketSubjectChangeListData : NSObject

@property(nonatomic, assign) UPMarketSetCode setCode;                       // 市场
@property(nonatomic, strong) NSString *code;                                // 代码
@property(nonatomic, strong) NSString *name;                                // 名称
@property(nonatomic, strong) NSString *time;                                // 触发时刻格式：HHMMSS
@property(nonatomic, strong) NSString *date;                                // 日期 YYYYMMDD
@property(nonatomic, strong) NSString *changeDesc;                          // 异动描述
@property(nonatomic, assign) UPMarketSubjectChangeType changeType;          // 类型
@property(nonatomic, assign) CGFloat changeRatio;                           // 涨幅
@property(nonatomic, strong) NSArray <UPHqStockHq *> *leadStocks;            //板块领涨股

@end

@interface UPMarketLeadSubjectData : NSObject

@property(nonatomic, assign) UPMarketSetCode setCode;
@property(nonatomic, strong) NSString *code;
@property(nonatomic, strong) NSString *name;
@property(nonatomic, assign) NSInteger time;
@property(nonatomic, strong) NSString *date;                                // 日期 YYYYMMDD
@property(nonatomic, assign) UPMarketSubjectChangeType changeType;          // 类型

@end

// MARK: - DDERank
@interface UPMarketDDERankData: UPHqStockBaseHq
@property(nonatomic, assign) double changeRatio;                          // 涨跌幅 = (最新价－昨收价) / 昨收价 * 100%
@property(nonatomic, assign) double turnoverRate;                         // 换手率 = 某一段时期内的成交量 / 发行总股数 * 100%
@property(nonatomic, assign) double ddx;                                   // DDX
@property(nonatomic, assign) double ddy;                                   // DDY
@property(nonatomic, assign) double ddz;                                   // DDZ
@property(nonatomic, assign) double ddf;                                   // DDF
@property(nonatomic, assign) double ddx5Day;                               // 5日DDX
@property(nonatomic, assign) double ddY5Day;                               // 5日DDY
@property(nonatomic, assign) double ddx60Day;                              // 60日DDX
@property(nonatomic, assign) double ddy60Day;                              // 60日DDY
@end

// MARK: - XSBYYStock

@interface UPMarketXSBYYStockData: NSObject
@property(nonatomic, assign) UPMarketSetCode setCode;                       // 股票市场
@property(nonatomic, copy) NSString *code;                                  // 股票代码
@property(nonatomic, copy) NSString *name;                                  // 股票名称
@property(nonatomic, copy) NSString *bdCode;                                // 标的代码
@property(nonatomic, assign) double bdPrice;                                // 标的股票价格，如果为0则显示--
@property(nonatomic, assign) UPMarketYYType type;                           // 类型，1：收购；2：回购
@property(nonatomic, assign) double price;                                  // 价格
@property(nonatomic, assign) UPMarketYYStatus status;                       // 状态，1：要约申报；2：终止执行
@property(nonatomic, assign) int startDate;                                 // 开始日期
@property(nonatomic, assign) int endDate;                                   // 截至日期
@property(nonatomic, assign) int volume;                                    // 数量
@property(nonatomic, assign) double yjl;                                    // 溢价率
@end


// MARK: - XSBFXStock
@interface UPMarketXSBFXStockData: NSObject
@property(nonatomic, assign) UPMarketSetCode setCode;                       // 股票市场
@property(nonatomic, copy) NSString *code;                                  // 股票代码
@property(nonatomic, copy) NSString *name;                                  // 股票名称
@property(nonatomic, copy) NSString *bdCode;                                // 标的代码
@property(nonatomic, assign) UPMarketFXType type;                           // 类型，1：询价；2：申购
@property(nonatomic, assign) double price;                                  // 发行价格
@property(nonatomic, assign) double lowPrice;                               // 价格下限
@property(nonatomic, assign) double highPrice;                              // 价格上限
@property(nonatomic, assign) int startDate;                                 // 开始日期
@property(nonatomic, assign) int endDate;                                   // 截至日期
@property(nonatomic, assign) double minVol;                                 // 最小数量（手）
@property(nonatomic, assign) double maxVol;                                 // 最大数量（手）
@property(nonatomic, assign) double volume;                                 // 发行量
@property(nonatomic, assign) UPMarketXSBFXMethod fxMethod;                  // 发行方式
@end

// MARK: - StockChange
@interface UPMarketStockChangeData: NSObject

@property(nonatomic, assign)UPMarketStockChangeType changeType;             // 异动类型
@property(nonatomic, assign)int type;                                       // 新高类型 （例：60日新高 填 60， 200日新高填200）
@property(nonatomic, strong)UPHqStockHq *stockHq;                           // 个股数据
@property (assign, nonatomic) int date;
@property (assign, nonatomic) int time;
@end

// MARK: - DXJL
@interface UPMarketDXJLStockData: UPHqStockBaseHq
@property(nonatomic, assign)double changeRatio;             // 涨跌幅
@property(nonatomic, assign)int orderTime;                  // 挂单时间
@property(nonatomic, assign)UPMarketDXJLType orderType;     // 挂单类型
@property(nonatomic, assign)double orderHands;              // 挂单手数
@property(nonatomic, assign)double orderNum;                // 挂单笔数
@property(nonatomic, assign)double orderPrice;              // 挂单价

@end

// MARK: - Currency

@interface UPCurrencyInfo : NSObject

@property (copy, nonatomic) NSString *currencyCode;          // 货币代码

@property (copy, nonatomic) NSString *currencyName;          // 货币名称

@end

// MARK: - KZZData
@interface UPMarketKZZData: NSObject
@property(nonatomic, assign) double zgPrice;        // 转股价
@property(nonatomic, assign) long startDate;        // 开始日期
@property(nonatomic, assign) long endDate;          // 结束日期
@end

// MARK: - AuctionData - 行情快照
@interface UPMarketAuctionItem : NSObject
@property(nonatomic, assign) int minutes;           // 零点以来分钟数
@property(nonatomic, assign) int time;              // HHMMSS
@property(nonatomic, assign) double nowPrice;       // 现价
@property(nonatomic, assign) long long matchVol;    // 虚拟匹配量
@property(nonatomic, assign) long long unMatchVol;  // 虚拟未匹配量
@property(nonatomic, assign) int unMatchBSFlag;     // 虚拟未匹配量是在买档还是卖档 0 - 无虚拟未匹配量  1 - 虚拟未匹配量在买档  2- 虚拟未匹配量在卖档
@end

@interface UPMarketAuctionData : NSObject
@property(nonatomic, assign) BOOL isRemain;         // 是否还有数据
@property(nonatomic, strong) NSArray<UPMarketAuctionItem *> *auctionItems;  // 行情快照列表
@end

@interface UPMarketSNFundData : NSObject

@property (nonatomic, assign) double net;
@property (nonatomic, assign) double balance;
@property (nonatomic, assign) double total;
@property (nonatomic, assign) UPSNDataType shtType;

@end

@interface UPMarketSNNetData : NSObject

@property (nonatomic, assign) int time;
@property (nonatomic, assign) double szNet;
@property (nonatomic, assign) double shNet;
@property (nonatomic, assign) double totalNet;

@end

@interface UPMarketZDFBData : NSObject
@property (nonatomic, copy) NSDictionary<NSNumber*, NSNumber*> *dicZTFB;    //分布数据 key:区间左值 value:个数 如[6%,8%)区间的key为6
@property (nonatomic, assign) int tradeDate ;                               //日期
@property (nonatomic, assign)  int zChg5Num ;                               //涨跌幅>5% 的股票数量，实时
@property (nonatomic, assign)  int dChg5Num;                                //涨跌幅<-5% 的股票数量，实时
@property (nonatomic, assign)  int zTNum;                                   //涨停股数量
@property (nonatomic, assign)  int dTNum;                                   //跌停股数量
@property (nonatomic, assign)  int suspendNum;                              //停牌数量
@property (nonatomic, assign)  int riseNum ;                                //上涨数量
@property (nonatomic, assign)  int fallNum ;                                //下跌数量
@property (nonatomic, assign)  int flatNum ;                                //平盘数量(上涨 =0.00%)
@end

@interface UPMarketDxjlUnit : NSObject
/// 次数
@property (nonatomic, assign) NSInteger count;
/// 总量
@property (nonatomic, assign) double vol;
/// 总额
@property (nonatomic, assign) double amt;

@end

@interface UPMarketHSZFYCom : NSObject
/// 股票市场
@property (nonatomic, assign) int setCode;
/// 股票代码
@property (nonatomic, copy) NSString *code;
/// 股票名称
@property (nonatomic, copy)  NSString *name;
/// 现价
@property (nonatomic, assign) double price;
/// 涨跌幅
@property (nonatomic, assign) float chg;
/// 买单信息
@property (nonatomic, strong) UPMarketDxjlUnit *buyUnit;
/// 卖单信息
@property (nonatomic, strong) UPMarketDxjlUnit *sellUnit;

@end

@interface UPMarketStockVolInfo : NSObject
/// 交易日期
@property (nonatomic, assign) int date;
/// 持股量   单位：股
@property (nonatomic, assign) long long volume;
/// 占流通A股比 单位：%
@property (nonatomic, assign) float ratio;
/// 持股市值(持股量*收盘价) 单位:万元;长江北向为当日所有个股持股总市值
@property (nonatomic, assign) double holdValue;
/// 当日收盘价
@property (nonatomic, assign) double closePrice;

@end

@interface UPMarketStockMoneyFlow : NSObject
@property (nonatomic, assign) int date;
@property (nonatomic, assign) double money;
@property (nonatomic, assign) double moneyAcc;
@property (nonatomic, assign) double closePrice; 
@end

@interface UPMarketStockBlock : NSObject

@property(nonatomic, assign) UPMarketSetCode setCode;                       // 市场码
@property(nonatomic, strong) NSString *code;                                // 股票代码
@property(nonatomic, strong) NSString *name;                                // 股票名称
@property (nonatomic, assign) float chgRatio;                                    // 涨跌幅

@end

@interface UPMarketRangeStats : NSObject

@property (nonatomic, assign) int precise;
@property(nonatomic, assign) UPMarketSetCode setCode;                       // 市场码
@property(nonatomic, strong) NSString *code;                                // 股票代码
@property(nonatomic, strong) NSString *name;                                // 股票名称
@property (nonatomic, assign) float chg;                                    // 涨跌幅
@property(nonatomic, strong) NSArray <UPMarketStockBlock*> *stockBlock;
@property (nonatomic, assign) float mainMoneyInflow;
@property (nonatomic, assign) float mainMoneyOutflow;
@property (nonatomic, assign) float mainMoneyflow;
@property (nonatomic, assign) float amount;
@property (nonatomic, assign) float circulationMarketValue;
@property (nonatomic, assign) float turnoverRate;
@property (nonatomic, assign) int inFlowDays;
@property (nonatomic, assign) int outFlowDays;
@property (nonatomic, assign) float superOrder;
@property (nonatomic, assign) float bigOrder;
@property (nonatomic, assign) float midOrder;
@property (nonatomic, assign) float smallOrder;

@end


