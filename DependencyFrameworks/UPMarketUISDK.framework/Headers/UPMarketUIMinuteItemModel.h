//
//  UPMarketUIMinuteItemModel.h
//  UPMarketUISDK
//
//  Created by fang on 2020/2/5.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPMarketUIVolModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface UPMarketUIMinuteItemModel : NSObject

@property (nonatomic, strong) UPHqMinuteItem *minData;
/*
 * 个股数据
 * */
@property (nonatomic, strong) UPHqStockHq *stockHq;

@property (nonatomic, assign) BOOL isNewDay;

@property (nonatomic, assign) NSInteger date;

@property (nonatomic, assign) NSInteger minutes;

@property (nonatomic, assign) BOOL isAfterData;

@property (nonatomic, assign) long nowVol;

@property (nonatomic, assign) CGFloat dealAmount;

@property (nonatomic, assign) CGFloat nowPrice;

@property (nonatomic, assign) CGFloat avgPrice;

@property (nonatomic, assign) CGFloat yClosePrice;

@property (nonatomic, assign) CGFloat lead;

/**
 *  日期
 */
@property (nonatomic, copy) NSString *dayDesc;
/**
 *  成交量
 */
@property (nonatomic, strong) UPMarketUIVolModel *volModel;
/*
 * 构造方法
 * */
- (instancetype)initWithMiniteData:(UPHqMinuteItem *)minuteItem;

@end

NS_ASSUME_NONNULL_END
