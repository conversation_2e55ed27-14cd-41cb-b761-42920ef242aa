//
//  UPMarketUIKlineModel.h
//  UPMarketUISDK
//
//  Created by fang on 2020/3/25.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class UPMarketUIKLineBSModel;

@interface UPMarketUIKlineModel : NSObject

@property (nonatomic, strong) UPHqAnalyData *klineData;

@property (nonatomic, assign) NSInteger index;

@property (nonatomic, assign) NSInteger date;

@property (nonatomic, assign) NSInteger minutes;

@property (nonatomic, assign) long dealVol;

@property (nonatomic, assign) CGFloat dealAmount;

@property (nonatomic, assign) long afterDealVol;

@property (nonatomic, assign) CGFloat afterDealAmount;

@property (nonatomic, assign) CGFloat openPrice;

@property (nonatomic, assign) CGFloat closePrice;

@property (nonatomic, assign) CGFloat yClosePrice;

@property (nonatomic, assign) CGFloat ySettlementPrice;

@property (nonatomic, assign) double highPrice;

@property (nonatomic, assign) double lowPrice;

@property (nonatomic, assign) double ma1,ma2,ma3,ma4,ma5,ma6,ma7,ma8;

@property (nonatomic, assign) BOOL hasGap;

@property (nonatomic, assign) CGFloat gapStartPrice;

@property (nonatomic, assign) CGFloat gapEndPrice;

@property (nonatomic, assign) CGFloat turnoverRate;

@property (nonatomic, assign) CGFloat swingRatio;

@property (nonatomic, strong) NSArray<UPHqQXChgData *> *qxChgData;

@property (nonatomic, strong) UPMarketUIKLineBSModel *bsModel;
@property (nonatomic, strong) UPMarketUIKLineBSModel *rzrqBSModel;

- (instancetype)initWithKlineData:(UPHqAnalyData *)klineData;

@end

NS_ASSUME_NONNULL_END
