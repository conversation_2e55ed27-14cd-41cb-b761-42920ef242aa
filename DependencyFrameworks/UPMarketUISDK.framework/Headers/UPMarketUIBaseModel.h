//
//  UPMarketUIBaseModel.h
//  UPMarketUISDK
//
//  Created by sammy<PERSON> on 2020/4/19.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

UIKIT_EXTERN NSString* const kUPMarketUIBaseModelNotificationKey;

// 主要包含一只股票的最基本属性, 不要随意新增字段
// 主要用于UI初始化, 实际代码中,动态更新的数据还是需要依靠stockHq
@interface UPMarketUIBaseModel : NSObject

@property(nonatomic, assign, readonly) UPMarketSetCode stockSetCode; // 股票市场
@property(nonatomic, copy, readonly) NSString * stockCode; // 股票代码
@property(nonatomic, assign, readonly) UPMarketStockCategory stockCategory; // 股票品种
@property(nonatomic, assign, readonly) NSUInteger stockOrigCategory; // 股票品种
@property(nonatomic, assign, readonly) UPMarketStockSubCategory stockSubCategory; // 股票品种
@property(nonatomic, assign, readonly) NSUInteger stockOrigSubCategory; // 股票品种
@property (nonatomic, assign, readonly) BOOL isUpdated;
@property(nonatomic, copy, readonly) NSString * stockName; // 股票名字
@property(nonatomic, assign, readonly) UPMarketTradeStatus stockTradeStatus; // 股票状态

@property(nonatomic, strong, readonly) UPHqStockHq *stockHq; // 股票行情

+(instancetype)modelWithSetCode:(UPMarketSetCode)stockSetCode code:(NSString *)stockCode;

- (void)updateStockModelWithStockHq:(UPHqStockHq *)stockHq;

@end

NS_ASSUME_NONNULL_END
