//
//  UPMarketUIView.h
//  UPMarketUISDK
//
//  Created by fang on 2020/3/23.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UPMarketUISDK/UPMarketUISDK.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPMarketUIView : UPMarketUIBaseStockView <UPMarketUIBaseStockLayerDelegate>

@property (nonatomic, assign) NSInteger indexID;

@property (nonatomic, assign, readonly) BOOL isKline;

@property (nonatomic, strong) UPMarketUIBaseStockLayer *stockLayer;

@property (nonatomic, strong) UPHqStockHq *stockHq;

@property (nonatomic, copy) NSString *fetcherKey;

@property (nonatomic, assign) NSInteger wantNum;

@property (nonatomic, copy) NSDictionary *settingDic;

@property (assign, nonatomic) BOOL shouldHideQX;

- (instancetype)initWithIndexID:(NSInteger)indexID isKline:(BOOL)isKline;

- (instancetype)initWithIndexID:(NSInteger)indexID isKline:(BOOL)isKline classLayer:(Class)classLayer;

- (instancetype)initWithIndexID:(NSInteger)indexID isKline:(BOOL)isKline settingDic:(NSDictionary *)settingDic;

- (instancetype)initWithIndexID:(NSInteger)indexID isKline:(BOOL)isKline settingDic:(NSDictionary *)settingDic classLayer:(Class)classLayer;

- (void)stopScroll;

@end

NS_ASSUME_NONNULL_END
