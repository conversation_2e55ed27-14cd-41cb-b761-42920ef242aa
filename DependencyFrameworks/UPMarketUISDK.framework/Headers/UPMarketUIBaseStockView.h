//
//  UPMarketUIBaseStockView.h
//  UPMarketUISDK
//
//  Created by sammy<PERSON> on 2020/2/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 集合竞价状态 具体查看 UPMarketUIMinuteJHJJState（默认智能开启UPMarketUIMinuteSmartOn）
FOUNDATION_EXPORT NSString * const kUPMarketUIMinuteJHJJStateKey;

/// 分时数据请求时参数dateNumber 传入则查询历史分时数据（默认为当天分时数据）
FOUNDATION_EXPORT NSString * const kUPMarketUIMinuteDateKey;

/// K线复权模式 具体查看 UPMarketFQMode （默认不复权UPMarketFQModeNone）
FOUNDATION_EXPORT NSString * const kUPMarketUIKlineCFQStateKey;

/// K线显示样式，空心/实心 具体查看UPMarketUIKLinePattern（默认空心UPMarketUIKLinePatternHollow）
FOUNDATION_EXPORT NSString * const kUPMarketUIKlinePatternKey;

/// K线是否显示缺口（默认不显示）
FOUNDATION_EXPORT NSString * const kUPMarketUIKlineShowGapKey;


@class UPMarketUIBaseStockView;

@protocol UPMarketUIBaseStockViewDelegate <NSObject>

@optional
- (void)onClickStockView:(UPMarketUIBaseStockView *)stockView;

- (void)stockDataUpdated:(UPMarketUIBaseStockView *)stockView data:(NSDictionary *)data;

@end


@interface UPMarketUIBaseStockView : UIView

@property (nonatomic, strong) UPMarketUIBaseModel *stockModel;

@property (nonatomic, weak) id<UPMarketUIBaseStockViewDelegate> stockViewDelegate;

@property(nonatomic, readonly, strong) NSDictionary<NSString *, UPMarketUIBaseStockDataFetcher *> * stockDataFetcherDict;
@property(nonatomic, readonly, strong) NSArray<UPMarketUIBaseStockLayer *> * stockLayerList;

@property (nonatomic, assign) CGPoint panBeginPoint;

- (void)stockViewStart NS_REQUIRES_SUPER;

- (void)stockViewStop NS_REQUIRES_SUPER;

- (void)addStockDataFetcher:(UPMarketUIBaseStockDataFetcher *)fetcher forDataKey:(NSString *)key;

- (void)removeStockDataFetcherForKey:(NSString *)key;

- (void)removeAllStockDataFetcher;

- (void)addStockMaskFetcher:(UPMarketUIBaseStockDataFetcher *)fetcher forDataKey:(NSString *)key;

- (void)removeStockMaskFetcherForKey:(NSString *)key;

- (void)addStockLayer:(UPMarketUIBaseStockLayer *)layer forDataKeys:(NSSet<NSString *> *)keys;

- (void)removeStockLayer:(UPMarketUIBaseStockLayer *)layer;

- (void)enableGestures:(UPMarketUIBaseStockViewGesture)gestures;
- (void)enableGestures:(UPMarketUIBaseStockViewGesture)gestures withRecognizer:(nullable NSDictionary<NSNumber * ,UIGestureRecognizer *> *)recognizers;
- (void)disableGestures:(UPMarketUIBaseStockViewGesture)gesture;

- (void)onGestureTap:(UIGestureRecognizerState)state withStockLayer:(UPMarketUIBaseStockLayer *)layer;

- (void)onGestureLongPress:(UIGestureRecognizerState)state withStockLayer:(UPMarketUIBaseStockLayer *)layer;

- (void)onGesturePan:(UIGestureRecognizerState)state withStockLayer:(UPMarketUIBaseStockLayer *)layer;

- (void)onGestureSwipe:(UIGestureRecognizerState)state withStockLayer:(UPMarketUIBaseStockLayer *)layer;

- (void)onGesturePinch:(UIGestureRecognizerState)state withStockLayer:(UPMarketUIBaseStockLayer *)layer;

- (void)clearData;

- (void)viewWillAppear;
@end

@interface UPMarketUIBaseStockView (UPMarketUIDebugging)

@property(nonatomic, copy, nullable) NSString *upmarketui_debugKey;

@end

NS_ASSUME_NONNULL_END
