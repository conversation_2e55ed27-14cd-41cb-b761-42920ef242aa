//
//  UPMarketUIStockCrosshairLayer.h
//  UPMarketUISDK
//
//  Created by sa<PERSON><PERSON> on 2020/2/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/// 用于完成长按时十字丝的绘制
@interface UPMarketUIStockCrosshairLayer : CALayer

// 十字丝底部字符串
@property(nonatomic, copy) NSString * xLabelString;
// 十字丝左右字符串
@property(nonatomic, copy) NSString * yLabelString;
// 十字丝是否为虚线
@property (nonatomic, assign) BOOL isLineDash;
// 当前点击的坐标
@property (nonatomic, assign, readonly) CGPoint point;
// 当前底部字符串的外包矩形
@property (nonatomic, assign) CGRect xLabelRect;
// 左边距
@property (assign, nonatomic) CGFloat leftMargin;
// 是否只显示Y轴线
@property (nonatomic, assign) BOOL isDrawYLine;

- (void)setPoint:(CGPoint)point isDrawYLine:(BOOL)isDrawYLine;

@end

NS_ASSUME_NONNULL_END
