//
//  UPMarketUIKlineBaseFetcher.h
//  UPMarketUISDK
//
//  Created by fang on 2020/3/20.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UPMarketUISDK/UPMarketUISDK.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UPMarketUIKlineFetcherDelegate <NSObject>

- (void)startLoadingMoreKlineData;

- (void)endLoadingMoreKlineData;

@end

@interface UPMarketUIKlineBaseFetcher : UPMarketUIBaseStockDataFetcher <NSArray<UPHqAnalyData *> *>

@property (nonatomic, assign, readonly) UPMarketUIKlineDataType dataType;

@property (nonatomic, assign) UPMarketFQMode fqMode;

@property (nonatomic, weak) id<UPMarketUIKlineFetcherDelegate> delegate;

- (void)loadMore;

@end

NS_ASSUME_NONNULL_END
