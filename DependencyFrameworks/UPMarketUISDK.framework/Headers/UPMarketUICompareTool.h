//
//  UPCompareTool.h
//  UPBaseUI
//
//  Created by <PERSON> on 2017/5/16.
//  Copyright © 2017年 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface UPMarketUICompareTool : NSObject


/**
 比较颜色

 @param data 数据源
 @param baseData 基础值
 @param precise 精度
 @return 比较的颜色
 */
+ (UIColor *)compareWithData:(CGFloat)data baseData:(CGFloat)baseData precise:(NSInteger)precise;

/**
 比较浮点型大小
 
 @param value1 第一个
 @param value2 第二个
 @param precise 精度
 @return 比较结果
 */
+ (NSComparisonResult)compareWithDoubleValue:(CGFloat)value1 andAnotherDoubleValue:(CGFloat)value2 precise:(NSInteger)precise;

/**
 比较浮点型大小
 
 @param value1 第一个
 @param value2 第二个
 @return 比较结果
 */
+ (BOOL)compareWithDoubleValue:(CGFloat)value1 andAnotherDoubleValue:(CGFloat)value2;

/**
比较 CGFloat 0

@param value 比较值
@return 比较结果
*/
+ (BOOL)isZero:(CGFloat)value;


/*
 * 返回最大值
 * */
+ (CGFloat)getMaxWithMax:(CGFloat)maxValue list:(NSArray *)compareArray;

/*
 * 返回最小值
 * */
+ (CGFloat)getMinWithMin:(CGFloat)minValue list:(NSArray *)compareArray;

/*
 * 最大值 除了0
 * */
+ (CGFloat)getMaxWithoutZeroMax:(CGFloat)maxValue list:(NSArray *)compareArray;

/*
 * 最小值 除了0
 * */
+ (CGFloat)getMinWithoutZeroMin:(CGFloat)minValue list:(NSArray *)compareArray;


@end
