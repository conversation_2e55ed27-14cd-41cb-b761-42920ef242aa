//
//  UPMarketUICalculateUtil.h
//  UPMarketUISDK
//
//  Created by fang on 2020/2/10.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPMarketUICalculateUtil : NSObject

/*
 计算成交量转换单位

 @param vol 成交量
 @precise 精度
 @return 转换后的字符串
 */
+ (NSString *)transStringWithUnitOfValue:(NSNumber *)value afterPoint:(NSInteger)precise;

/*
 计算成交量转换单位

 @param vol 成交量
 @precise 精度
 @strictMode  严格模式 小于 1000 时展示小数点
 @return 转换后的字符串
 */
+ (NSString *)transStringWithUnitOfValue:(NSNumber *)value afterPoint:(NSInteger)precise strictMode:(BOOL)strictMode;

/*
 传入精度自动四舍五入 不判断 0

 @param number 需要计算的值
 @param precise 精度
 @param symbol + - 符号
 @return 计算后的值
*/
+ (NSString *)transNumberByRoundingOff:(double)number precise:(NSInteger)precise needsymbol:(BOOL)symbol;

/*
 传入精度自动四舍五入 不判断 0

 @param number 需要计算的值,传NSNumber
 @param precise 精度
 @param symbol + - 符号
 @return 计算后的值
 */
+(NSString *)transNumberByRoundingOffWithoutLost:(NSNumber *)number precise:(NSInteger)precise needsymbol:(BOOL)symbol;

/*
 百分比自动计算
 @param percent 百分比值
 @return 计算后的百分比
*/
+ (NSString *)transPercent:(double)percent needSymbol:(BOOL)needSymbol;

/*
 百分比自动计算
 @param percent 百分比值
 @param precise 精度
 @return 计算后的百分比
*/
+ (NSString *)transPercent:(double)percent precise:(NSInteger)precise needSymbol:(BOOL)needSymbol;

/*
 计算分钟数转换成 时:分

 @param minutes 距离当前0点分钟数
 @return 时:分
 */
+ (NSString *)transTimeStringWithMinutes:(NSInteger)minutes;

/*
 转换涨跌比
 @param chgRatio 涨跌比
 @param chgValue 涨跌值
 @param baseValue 基于值显示--
 @return 计算后的百分比
 */
+ (NSString *)transChgRatio:(double)chgRatio chgValue:(double)chgValue baseValue:(double)baseValue needSymbol:(BOOL)needSymbol;

/*
 传入精度自动计算价格 会判断 0 是否显示 --

 @param price 原价
 @param nowPrice 现价
 @param precise 精度
 @param symbol + - 符号
 @return 计算后的价
 */
+(NSString *)transPriceString:(double)price precise:(NSInteger)precise needsymbol:(BOOL)symbol;

/*
 除去INF和NAN
 */
+ (double)transWithoutINForNAN:(double)number;

/**
 *  将HHMMSS的格式转化为分钟数
 *
 * @param time        HHMMSS格式时间
 * @return 分钟数
 */
+ (short)transTimeToMin:(int)time;
    
@end

NS_ASSUME_NONNULL_END
