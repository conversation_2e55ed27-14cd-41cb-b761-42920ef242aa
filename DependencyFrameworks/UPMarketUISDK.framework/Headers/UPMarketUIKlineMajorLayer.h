//
//  UPMarketUIKlineMajorLayer.h
//  UPMarketUISDK
//
//  Created by fang on 2020/3/24.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UPMarketUISDK/UPMarketUIKlineBaseLayer.h>

#import "UPMarketUIKLineBSModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface UPMarketUIKlineMajorLayer : UPMarketUIKlineBaseLayer

@property (nonatomic, strong) NSArray *klineDataList;

@property (nonatomic, assign) BOOL isDrawMALine, isDrawCccbx, isDrawRegion;

@property (nonatomic, assign) UPMarketUIKLinePattern klinePattern;

@property (nonatomic, assign) BOOL showGap;

@property (nonatomic, strong) UPMarketUIRegionModel *regionModel;

@property (nonatomic, strong) UPHqQXData *qxData;

@property (assign, nonatomic) BOOL useCustomDailyTailString;

@property (copy, nonatomic) NSString *dailyTailString;

@property (nonatomic, strong) NSMutableArray<UPYunShenStockModel *> *ysList;

@end

NS_ASSUME_NONNULL_END
