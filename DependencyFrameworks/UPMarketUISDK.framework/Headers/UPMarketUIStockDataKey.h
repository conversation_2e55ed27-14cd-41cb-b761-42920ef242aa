//
//  UPMarketUIStockDataKey.h
//  UPMarketUISDK
//
//  Created by sammy<PERSON> on 2020/2/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// MARK: minute
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyMinuteSingle;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyMinuteFiveday;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyMinuteAuction;

// MARK: historyMinute
FOUNDATION_EXPORT NSString * const kUPMarketUIHistoryMinuteNotificationKey;

// MARK: kline
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyKlineDaily;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyKlineWeek;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyKlineMonth;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyKlineSeason;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyKlineYear;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyKlineMinute1;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyKlineMinute5;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyKlineMinute15;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyKlineMinute30;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyKlineMinute60;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyKlineMinute120;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyStockQX;

FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyStockHQ;
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyStockDDE;

// MARK: mask
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyStockMask;

// MARK: index
FOUNDATION_EXPORT NSString * const kUPMarketUIStockDataKeyStockIndex;

NS_ASSUME_NONNULL_END
