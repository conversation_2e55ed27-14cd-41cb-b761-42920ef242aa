//
//  UPMarketUIVolPositionModel.h
//  UPMarketUISDK
//
//  Created by ChenR<PERSON> on 2020/4/1.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
/**
成交量位置模型
*/
@interface UPMarketUIVolPositionModel : NSObject
/**
 *  开始点
 */
@property (nonatomic, assign) CGPoint StartPoint;

/**
 *  结束点
 */
@property (nonatomic, assign) CGPoint EndPoint;

/**
 *  特殊颜色
 */
@property (nonatomic, copy) UIColor *color;

/**
 *  特殊颜色
 */
@property (nonatomic, assign) NSComparisonResult result;

/**
 涨跌颜色
 */
@property (nonatomic, assign) BOOL isRise;

/**
 *  工厂方法
 */
+ (instancetype) modelWithStartPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint;
@end

NS_ASSUME_NONNULL_END
