//
//  UPMarketUIIndexPeriod.h
//  UPMarketUISDK
//
//  Created by fang on 2020/3/13.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPMarketUIBasePeriod : NSObject

+ (instancetype)period:(NSDictionary *)dic;

- (UPTAFJSONObject *)toJsonObject;

@end

@interface UPMarketUIMAPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger ma1,ma2,ma3,ma4,ma5,ma6,ma7,ma8;
@property (nonatomic, assign) BOOL ma1On,ma2On,ma3On,ma4On,ma5On,ma6On,ma7On,ma8On;

@end

@interface UPMarketUIMACDPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger diff1,diff2,dea;

@end

@interface UPMarketUIKDJPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger k,d,j;

@end

@interface UPMarketUIRSIPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger rsi1,rsi2,rsi3;

@end

@interface UPMarketUIBOLLPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger boll;

@end

@interface UPMarketUIVOLPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger ma1,ma2,ma3;

@end

@interface UPMarketUIBIASPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger bias1,bias2,bias3;

@end

@interface UPMarketUIOBVPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger m;

@end

@interface UPMarketUICCIPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger n;

@end

@interface UPMarketUIWRPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger n,n1;

@end

@interface UPMarketUIVRPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger m,n;

@end

@interface UPMarketUICRPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger n,m1,m2,m3,m4;

@end

@interface UPMarketUIDMAPeriod : UPMarketUIBasePeriod

@property (nonatomic, assign) NSInteger m,n1,n2;

@end

@interface UPMarketUIIndexPeriod : NSObject

+ (void)setPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline period:(UPMarketUIBasePeriod *)period;

+ (UPMarketUIMAPeriod *)getMAPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

+ (UPMarketUIMACDPeriod *)getMACDPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

+ (UPMarketUIKDJPeriod *)getKDJPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

+ (UPMarketUIRSIPeriod *)getRSIPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

+ (UPMarketUIBOLLPeriod *)getBOLLPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

+ (UPMarketUIVOLPeriod *)getVOLPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

+ (UPMarketUIBIASPeriod *)getBIASPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

+ (UPMarketUIVRPeriod *)getVRPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

+ (UPMarketUIOBVPeriod *)getOBVPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

+ (UPMarketUICCIPeriod *)getCCIPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

+ (UPMarketUIWRPeriod *)getWRPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

+ (UPMarketUICRPeriod *)getCRPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

+ (UPMarketUIDMAPeriod *)getDMAPeriod:(BOOL)isMajorIndex position:(NSInteger)position indexID:(NSInteger)indexID isKline:(BOOL)isKline;

@end

NS_ASSUME_NONNULL_END
