//
//  UPMarketUIBaseStockDataFetcher.h
//  UPMarketUISDK
//
//  Created by sa<PERSON><PERSON> on 2020/2/4.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPMarketUIBaseStockDataFetcher <__covariant T> : NSObject

@property (nonatomic, strong) UPMarketUIBaseModel *stockModel;

@property (nonatomic, strong) UPHqStockHq *stockHq;

@property(nonatomic, strong) T stockData;

@property (nonatomic, copy, readonly) NSString *fetcherKey;

@property (nonatomic, assign) NSInteger wantNum;

@property (nonatomic, assign) BOOL maskFetcher;

- (void)start;

- (void)stop;

- (void)clearData;

@end

@interface UPMarketUIBaseStockDataFetcher (UPMarketUIDebugging)

@property(nonatomic, copy, nullable) NSString *upmarketui_debugKey;

@end

NS_ASSUME_NONNULL_END
