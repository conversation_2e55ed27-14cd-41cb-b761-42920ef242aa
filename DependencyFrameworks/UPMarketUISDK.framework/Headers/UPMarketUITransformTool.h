//
// Created by <PERSON> on 2017/8/4.
// Copyright (c) 2017 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface UPMarketUITransformTool : NSObject

/**
 根据交易时期数组返回总交易分钟数

 */
+ (NSUInteger)getTotolTradeMinutesFromTradeArray:(NSArray<NSArray <NSNumber *>*> *)tradeArray containAfter:(BOOL)containAfter;

/**
 根据交易时期数组返回当前交易分钟数
 
 */
+ (int)getCurTradeMinutesFromTradeArray:(NSArray<NSArray <NSNumber *>*> *)tradeArray tradeTime:(int)tradeTime;

/**
 自动根据宽度计算字体大小

 @param width 宽度
 @param string 总字符
 @param maxSize 最大字号
 @param minSize 最小字号
 @return 缩了的字号
 */
+ (CGFloat)fontSizeAutoFitByWidth:(CGFloat)width string:(NSString *)string maxFontSize:(CGFloat)maxSize minFontSize:(CGFloat)minSize;


/*
 * 根据币种返回对应名称
 * */
+ (NSString *)transformWithCoinTypeToString:(UPMarketCoinType)coinType;
@end
