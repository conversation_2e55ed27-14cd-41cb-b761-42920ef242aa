//
//  TAFHTTPRequest.h
//  UPTAF
//
//  Created by sammy<PERSON> on 2020/12/14.
//  Copyright © 2020 UPChina. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <UPTAF/TAFJSONObject.h>

NS_ASSUME_NONNULL_BEGIN

// MARK: UPTAFHTTPRequestBody

@protocol UPTAFHTTPRequestBody <NSObject>

- (NSURLSessionTask *)createSessionTask:(NSURLSession *)session request:(NSMutableURLRequest *)request;

@end

// MARK: UPTAFHTTPRequestDataBody

@interface UPTAFHTTPRequestDataBody : NSObject <UPTAFHTTPRequestBody>

+ (instancetype)dataBodyWithData:(NSData *)data;

+ (instancetype)dataBodyWithFile:(NSURL *)file;

@end

// MARK: UPTAFHTTPRequestFormBody

@interface UPTAFHTTPRequestFormBody : NSObject <UPTAFHTTPRequestBody>

- (void)add:(NSString *)name value:(NSString *)value;

- (void)addEncoded:(NSString *)name value:(NSString *)value;

@end

// MARK: UPTAFHTTPRequestJSONBody

@interface UPTAFHTTPRequestJSONBody : NSObject <UPTAFHTTPRequestBody>

- (instancetype)initWithDictionary:(NSDictionary *)dict;

- (instancetype)initWithData:(NSData *)data;

@end

// MARK: UPTAFHTTPRequestMultipartBody

@interface UPTAFHTTPRequestMultipartBody : NSObject <UPTAFHTTPRequestBody>

- (void)add:(NSString *)name value:(NSString *)value;

- (void)add:(NSString *)name data:(NSData *)data;

- (void)add:(NSString *)name file:(NSURL *)file;

@end

// MARK: UPTAFHTTPRequest

@interface UPTAFHTTPRequest : NSObject

@property (nonatomic, readonly, copy) NSString * method;
@property (nonatomic, readonly, strong) id<UPTAFHTTPRequestBody> body;
@property (nonatomic, readonly, assign) NSTimeInterval timeout;

+ (instancetype)get:(NSString *)URLString;

+ (instancetype)get:(NSString *)URLString timeout:(NSTimeInterval)timeout;

+ (instancetype)post:(NSString *)URLString body:(id<UPTAFHTTPRequestBody>)body;

+ (instancetype)post:(NSString *)URLString body:(id<UPTAFHTTPRequestBody>)body timeout:(NSTimeInterval)timeout;

- (NSURL *)getURL;

/*!
 * @abstract
 * 添加Header, 会被encode
 */
- (void)addHeader:(NSString *)name value:(NSString *)value;

/*!
 * @abstract
 * 添加已encoded的Header
 */
- (void)addEncodedHeader:(NSString *)name value:(NSString *)value;

/*!
 * @abstract
 * 添加Header, 会被encode
 */
- (void)addHeaders:(NSDictionary *)headers;

/*!
 * @abstract
 * 添加Path, 会被encode, 支持a/b/c这样的多级形式
 */
- (void)addPath:(NSString *)pathSegment;

/*!
 * @abstract
 * 添加query, 会被encode
 */
- (void)addQueryParameter:(NSString *)name value:(NSString *)value;

@end

NS_ASSUME_NONNULL_END
