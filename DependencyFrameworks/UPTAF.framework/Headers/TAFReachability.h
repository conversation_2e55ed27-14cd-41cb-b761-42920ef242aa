//
//  TAFReachability.h
//  UPTAF
//
//  Created by sa<PERSON><PERSON> on 2018/6/19.
//  Copyright © 2018年 UPChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <SystemConfiguration/SystemConfiguration.h>

#define UPTAFNetworkReachable ([UPTAFReachability isReachable])
#define UPTAFNetworkNotReachable (![UPTAFReachability isReachable])

FOUNDATION_EXPORT NSString * const UPTAFReachabilityChangeNotification;

typedef NS_ENUM(NSInteger, UPTAFReachabilityStatus) {
    UPTAFReachabilityStatusUnknown          = -1, // 未知, 刚初始化的时候会是这个状态
    UPTAFReachabilityStatusNotReachable     = 0, // 没有网络
    UPTAFReachabilityStatusReachableViaWWAN = 1, // 移动网络
    UPTAFReachabilityStatusReachableViaWiFi = 2, // WiFi网络
};

@interface UPTAFReachability : NSObject

+(instancetype)sharedInstance;

+(UPTAFReachabilityStatus)status;

+(BOOL)isReachable;

@end
