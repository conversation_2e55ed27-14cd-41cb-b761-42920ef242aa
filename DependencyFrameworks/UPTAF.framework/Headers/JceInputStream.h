#import "JceStream.h"

@interface UPTAFHeadData : NSObject

@property (nonatomic, assign) int type;
@property (nonatomic, assign) int tag;

@end

@interface UPTAFJceInputStream : UPTAFJceStream

+ (UPTAFJceInputStream *)streamWithBuffer:(void *)buffer length:(int)length;
+ (UPTAFJceInputStream *)streamWithData:(NSData *)data;

//浮点精度控制函数
- (void)setMaxPrecision:(unsigned char)precision;
- (void) saveResetPrecision;
- (unsigned char) readResumePrecision;

- (BOOL)readHead:(UPTAFHeadData *)hd skip:(BOOL)move;

- (long long)readInt:(int)tag;
- (float)readFloat:(int)tag;
- (double)readDouble:(int)tag;

- (NSNumber *)readNumber:(int)tag required:(BOOL)required;
- (NSString *)readString:(int)tag required:(BOOL)required;
- (NSData *)readData:(int)tag required:(BOOL)required;
- (id)readObject:(int)tag required:(BOOL)required description:(Class)theClass;
- (NSArray *)readArray:(int)tag required:(BOOL)required description:(id)description;
- (NSDictionary *)readDictionary:(int)tag required:(BOOL)required description:(UPTAFJcePair *)description;
- (id)readAnything:(int)tag required:(BOOL)required description:(id)description;

@end
