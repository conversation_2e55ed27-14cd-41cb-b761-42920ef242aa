//
//  TAFAlarmSender.h
//  UPTAF
//
//  Created by tamry on 2020/1/2.
//  Copyright © 2020 UPChina. All rights reserved.
//

#import <Foundation/Foundation.h>

/** !!! 相关常量定义，是与服务端协商确定的，不能随意更改 !!! */

typedef NS_ENUM(NSInteger, UPTAFAlarmCategory) {
    UPTAFAlarmCategoryMarket = 1,    // 行情
    UPTAFAlarmCategoryPay,           // 支付
    UPTAFAlarmCategoryUser,          // 用户
};

typedef NS_ENUM(NSInteger, UPTAFAlarmSubCategory) {
    UPTAFAlarmSubCategoryPayWeiXin = 1,    // 支付-微信
    UPTAFAlarmSubCategoryPayALi,           // 支付-支付宝
    UPTAFAlarmSubCategoryPayHuaWei,        // 支付-华为 (供Android使用)
    UPTAFAlarmSubCategoryPayApple,         // 支付-苹果
};

NS_ASSUME_NONNULL_BEGIN

@interface UPTAFAlarmSender : NSObject

+(void)send:(UPTAFAlarmCategory)category title:(NSString *)title desc:(NSString *)desc ext:(NSString *)ext;

+(void)send:(UPTAFAlarmCategory)category subCategory:(UPTAFAlarmSubCategory)subCategory title:(NSString *)title desc:(NSString *)desc ext:(NSString *)ext;

@end

NS_ASSUME_NONNULL_END
