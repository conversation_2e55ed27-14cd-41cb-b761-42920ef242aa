#import "JceObject.h"

@interface UPTAFUniAttribute : UPTAFJceObject

@property (nonatomic, strong) NSMutableDictionary* jce_uniAttributes;

+ (UPTAFUniAttribute *)fromAttributeData:(NSData *)data;
- (NSData *)attributeData;

- (NSData *)attrValueWithName:(NSString *)name;
- (void)setAttrValue:(NSData *)data withName:(NSString *)name;

@end

// MARK: Helper

@interface UPTAFUniAttributeHelper : NSObject

// MARK: JceObject

+ (UPTAFJceObject *)objectWithName:(NSString *)name inAttributes:(UPTAFUniAttribute *)attrs description:(Class)theClass;
+ (void)setJceObjectInAttributes:(UPTAFJceObject *)jceObject attrs:(UPTAFUniAttribute *)attrs withName:(NSString *)name;

// MARK: NSData

+ (NSData *)dataWithName:(NSString *)name inAttributes:(UPTAFUniAttribute *)attrs;
+ (void)setDataInAttributes:(NSData *)data attrs:(UPTAFUniAttribute *)attrs withName:(NSString *)name;

// MARK: NSString

+ (NSString *)stringWithName:(NSString *)name inAttributes:(UPTAFUniAttribute *)attrs;
+ (void)setStringInAttributes:(NSString *)str attrs:(UPTAFUniAttribute *)attrs withName:(NSString *)name;

// MARK: NSNumber

+ (NSNumber *)numberWithName:(NSString *)name inAttributes:(UPTAFUniAttribute *)attrs;
+ (void)setNumberInAttributes:(NSNumber *)number attrs:(UPTAFUniAttribute *)attrs withName:(NSString *)name;

@end
