//
//  TAFResponse.h
//  UPTAFNetwork
//
//  Created by sa<PERSON><PERSON> on 16/9/23.
//  Copyright © 2016年 TAF. All rights reserved.
//

#ifndef UPTAFResponse_h
#define UPTAFResponse_h

#import <Foundation/Foundation.h>

#import "UniPacket.h"

@interface UPTAFResponse : NSObject

@property (nonatomic, strong) id result;
@property (nonatomic, strong) NSError * error;
@property (nonatomic, strong) UPTAFUniPacket * rawPacket;

- (BOOL)isSuccessful;

+ (instancetype)initWithResult:(id)result rawPacket:(UPTAFUniPacket *)rawPacket;

+ (instancetype)initWithError:(NSError *)error;

@end

#endif /* UPTAFResponse_h */
