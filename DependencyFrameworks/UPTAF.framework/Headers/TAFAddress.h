//
//  TAFAddress.h
//  UPTAFLogin
//
//  Created by sa<PERSON><PERSON> on 16/9/23.
//  Copyright © 2016年 TAF. All rights reserved.
//

#ifndef UPTAFAddress_h
#define UPTAFAddress_h

@interface UPTAFAddress : NSObject <NSCoding>

@property (nonatomic, assign) int type;
@property (nonatomic, copy) NSString * name;
@property (nonatomic, copy) NSString * address;
@property (nonatomic, strong) NSArray * servants;
@property (nonatomic, assign) int priority; // 优先级, 越小越高
@property (nonatomic, assign) int failed;
@property (nonatomic, assign) long delay;
@property (nonatomic, copy) NSString * monitorPath; // 测速地址

@property (nonatomic, assign, readonly) double weight;

+ (NSArray *)loadAddressList:(NSString *)path;

+ (void)saveAddressList:(NSArray *)list path:(NSString *)path;

- (int)match:(int)type servant:(NSString *)servant;

@end

#endif /* UPTAFAddress_h */
