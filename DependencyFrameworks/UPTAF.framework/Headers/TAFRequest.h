//
//  UPTAFRequest.h
//  UPTAFNetwork
//
//  Created by sa<PERSON><PERSON> on 16/9/23.
//  Copyright © 2016年 TAF. All rights reserved.
//

#ifndef UPTAFRequest_h
#define UPTAFRequest_h

#import <Foundation/Foundation.h>

#import "UniPacket.h"

#import "TAFResponse.h"

typedef void (^UPTAFRequestCallback)(UPTAFResponse *);

@interface UPTAFRequest : NSObject

@property (nonatomic, copy) NSString * servantName;
@property (nonatomic, copy) NSString * funcName;
@property (nonatomic, copy) NSString * address;
@property (nonatomic, copy) NSString * extra;
@property (nonatomic) int timeout;

- (void)buildRequest:(UPTAFUniPacket *)uniPacket;

- (id)parseResponse:(UPTAFUniPacket *)uniPacket;

- (UPTAFResponse *)execute;

- (void)enqueue:(UPTAFRequestCallback)callback;

@end

#endif /* UPTAFRequest_h */
