//
//  TAFJSONObject.h
//  UPTAF
//
//  Created by sammy<PERSON> on 2018/12/10.
//  Copyright © 2018 UPChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class UPTAFJSONArray;
@class UPTAFJSONObject;

// MARK: - UPTAFJSONBase

@interface UPTAFJSONBase : NSObject
@end

// MARK: - UPTAFJSONArray

@interface UPTAFJSONArray : UPTAFJSONBase

@property (nonatomic, readonly) NSUInteger count;

@property (nonatomic, readonly) NSArray * array;

+ (nullable instancetype)jsonArrayWithString:(NSString *)string;

- (UPTAFJSONObject *)objectAtIndex:(NSUInteger)index;

- (UPTAFJSONArray *)arrayAtIndex:(NSUInteger)index;

- (NSString *)stringAtIndex:(NSUInteger)index;

- (BOOL)boolAtIndex:(NSUInteger)index;

- (int)intAtIndex:(NSUInteger)index;

- (unsigned int)unsignedIntAtIndex:(NSUInteger)index;

- (long)longAtIndex:(NSUInteger)index;

- (unsigned long)unsignedLongAtIndex:(NSUInteger)index;

- (long long)longLongAtIndex:(NSUInteger)index;

- (unsigned long long)unsignedLongLongAtIndex:(NSUInteger)index;

- (NSInteger)integerAtIndex:(NSUInteger)index;

- (NSUInteger)unsignedIntegerAtIndex:(NSUInteger)index;

- (float)floatAtIndex:(NSUInteger)index;

- (double)doubleAtIndex:(NSUInteger)index;

@end

// MARK: - UPTAFJSONObject

@interface UPTAFJSONObject : UPTAFJSONBase

@property (nonatomic, readonly) NSUInteger count;

@property (nonatomic, readonly) NSDictionary * dictionary;

+ (nullable instancetype)jsonObjectWithString:(NSString *)string;

+ (nullable instancetype)jsonObjectWithDictionary:(NSDictionary *)dict;

- (UPTAFJSONObject *)objectForKey:(NSString *)key;

- (UPTAFJSONArray *)arrayForKey:(NSString *)key;

- (NSString *)stringForKey:(NSString *)key;

- (BOOL)boolForKey:(NSString *)key;

- (int)intForKey:(NSString *)key;

- (unsigned int)unsignedIntForKey:(NSString *)key;

- (long)longForKey:(NSString *)key;

- (unsigned long)unsignedLongForKey:(NSString *)key;

- (long long)longLongForKey:(NSString *)key;

- (unsigned long long)unsignedLongLongForKey:(NSString *)key;

- (NSInteger)integerForKey:(NSString *)key;

- (NSUInteger)unsignedIntegerForKey:(NSString *)key;

- (float)floatForKey:(NSString *)key;

- (double)doubleForKey:(NSString *)key;

@end

NS_ASSUME_NONNULL_END
