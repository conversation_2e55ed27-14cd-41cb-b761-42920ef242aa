//
//  NSDate+Util.h
//  UPBase
//
//  Created by sammy<PERSON> on 2020/3/12.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXPORT NSString * const kUPDateFormat_HHmm;                    // HH:mm
FOUNDATION_EXPORT NSString * const kUPDateFormat_HHmmss;                  // HH:mm:ss
FOUNDATION_EXPORT NSString * const kUPDateFormat_MMdd;                    // MM.dd
FOUNDATION_EXPORT NSString * const kUPDateFormat_MMdd_WithMinus;          // MM-dd
FOUNDATION_EXPORT NSString * const kUPDateFormat_MMddHHmm;                // MM.dd HH:mm
FOUNDATION_EXPORT NSString * const kUPDateFormat_MMddHHmm_WithMinus;      // MM-dd HH:mm
FOUNDATION_EXPORT NSString * const kUPDateFormat_MMddHHmmss;              // MM.dd HH:mm:ss
FOUNDATION_EXPORT NSString * const kUPDateFormat_MMddHHmmss_WithMinus;    // MM-dd HH:mm:ss
FOUNDATION_EXPORT NSString * const kUPDateFormat_yyyyMMdd;                // yyyy.MM.dd
FOUNDATION_EXPORT NSString * const kUPDateFormat_yyyyMMdd_NoDot;          // yyyyMMdd
FOUNDATION_EXPORT NSString * const kUPDateFormat_yyyyMMdd_WithMinus;      // yyyy-MM-dd
FOUNDATION_EXPORT NSString * const kUPDateFormat_yyyyMMddHHmm;            // yyyy.MM.dd HH:mm
FOUNDATION_EXPORT NSString * const kUPDateFormat_yyyyMMddHHmm_WithMinus;  // yyyy-MM-dd HH:mm
FOUNDATION_EXPORT NSString * const kUPDateFormat_yyyyMMddHHmmss;          // yyyy.MM.dd HH:mm:ss
FOUNDATION_EXPORT NSString * const kUPDateFormat_yyyyMMddHHmmss_WithMinus;// yyyy-MM-dd HH:mm:ss

@interface NSDate (UPUtil)

+ (instancetype)up_dateFromString:(NSString *)str format:(NSString *)format;

+ (instancetype)up_dateFromSecond:(NSTimeInterval)second;

+ (instancetype)up_dateFromMillisecond:(NSTimeInterval)millisecond;

- (NSString *)up_formatDate:(NSString *)format;

- (NSInteger)up_year;

- (NSInteger)up_month;

- (NSInteger)up_weekOfYear;

- (NSInteger)up_weekOfMonth;

- (NSInteger)up_dayOfYear;

- (NSInteger)up_dayOfMonth;

- (NSInteger)up_dayOfWeekByMondayFirst;

- (NSInteger)up_dayOfWeekBySundayFirst;

- (NSInteger)up_hourOfDay;

- (NSInteger)up_minuteOfHour;

- (NSInteger)up_minuteOfDay;

- (uint64_t)up_timestampSeconds;

- (uint64_t)up_timestampMilliseconds;

@end

NS_ASSUME_NONNULL_END
