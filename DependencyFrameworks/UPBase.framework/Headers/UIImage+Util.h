//
//  UIImage+Util.h
//  UPBase
//
//  Created by sammy<PERSON> on 2019/12/13.
//  Copyright © 2019 UpChina. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIImage (UPUtil)

/*
* 缩小图片到指定尺寸，如果图片尺寸比目标尺寸小则不做处理，否则等比例缩小，可能返回空
*/
-(NSData *)up_scaleDownToSize:(CGSize)size;

/*
* 缩小图片到指定尺寸，如果图片尺寸比目标尺寸小则不做处理，否则等比例缩小，可能返回空
*/
-(NSData *)up_scaleDownToSize:(CGSize)size quality:(CGFloat)quality;

/*
* 压缩图片到指定大小，可能会缩放尺寸，可能返回空
*/
-(NSData *)up_compressToSize:(NSUInteger)maxSize;

@end

NS_ASSUME_NONNULL_END
