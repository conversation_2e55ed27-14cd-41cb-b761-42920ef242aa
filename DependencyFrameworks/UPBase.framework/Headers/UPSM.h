//
//  UPSM.h
//  iOSTest
//
//  Created by sa<PERSON><PERSON> on 2020/3/30.
//  Copyright © 2020 UPChina. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

FOUNDATION_EXPORT const int kSM2OptionC1C3C2;
FOUNDATION_EXPORT const int kSM2OptionC1C2C3;

FOUNDATION_EXPORT const int kSM4OptionECB;
FOUNDATION_EXPORT const int kSM4OptionCBC;
FOUNDATION_EXPORT const int kSM4OptionCFB128;
FOUNDATION_EXPORT const int kSM4OptionOFB;
FOUNDATION_EXPORT const int kSM4OptionCTR;

FOUNDATION_EXPORT const int kPaddingNONE;
FOUNDATION_EXPORT const int kPaddingPKCS7;
FOUNDATION_EXPORT const int kPaddingISO7816_4;
FOUNDATION_EXPORT const int kPaddingANSI923;
FOUNDATION_EXPORT const int kPaddingISO10126;
FOUNDATION_EXPORT const int kPaddingZERO;

@interface UPSM : NSObject

/*!
 * @abstract
 * SM2加密解密
 *
 * @param key
 * hex字符串
 *
 * @param data
 * 数据
 *
 * @param option
 * kSM2OptionC1C3C2 或者 kSM2OptionC1C2C3
 *
 * @param encrypt
 * YES-加密, NO-解密
 */
+(NSData *)sm2:(NSString *)key data:(NSData *)data option:(int)option encrypt:(BOOL)encrypt;

/*!
 * @abstract
 * SM2加密解密, 加密返回小写hex字符串, 解密返回原文字符串
 *
 * @param key
 * hex字符串
 *
 * @param str
 * 加密传原文字符串, 解密传hex字符串
 *
 * @param option
 * kSM2OptionC1C3C2 或者 kSM2OptionC1C2C3
 *
 * @param encrypt
 * YES-加密, NO-解密
 */
+(NSString *)sm2:(NSString *)key str:(NSString *)str option:(int)option encrypt:(BOOL)encrypt;

/*!
 * @abstract
 * SM3摘要, 返回小写hex字符串
 *
 * @param str
 * 字符串
 */
+(NSString *)sm3:(NSString *)str;

/*!
 * @abstract
 * SM4加密解密
 *
 * @param key
 * 密钥
 *
 * @param iv
 * 加密向量
 *
 * @param data
 * 数据
 *
 * @param option
 * kSM4OptionXXX
 *
 * @param padding
 * kPaddingXXX
 *
 * @param encrypt
 * YES-加密, NO-解密
 */
+(NSData *)sm4:(NSString *)key iv:(nullable NSString *)iv data:(NSData *)data option:(int)option padding:(int)padding encrypt:(BOOL)encrypt;

/*!
 * @abstract
 * SM4加密解密, 加密返回小写hex字符串, 解密返回原文字符串
 *
 * @param key
 * 密钥
 *
 * @param iv
 * 加密向量
 *
 * @param str
 * 加密传原文字符串, 解密传hex字符串
 *
 * @param option
 * kSM4OptionXXX
 *
 * @param padding
 * kPaddingXXX
 *
 * @param encrypt
 * YES-加密, NO-解密
 */
+(NSString *)sm4:(NSString *)key iv:(nullable NSString *)iv str:(NSString *)str option:(int)option padding:(int)padding encrypt:(BOOL)encrypt;

@end

NS_ASSUME_NONNULL_END
