//
//  UPBaseDefine.h
//  UPBase
//
//  Created by sa<PERSON><PERSON> on 2020/3/11.
//  Copyright © 2020 UpChina. All rights reserved.
//

#ifndef UPBaseSDKDefine_h
#define UPBaseSDKDefine_h

#define WeakSelf(weakSelf) __weak typeof(self) weakSelf = self;

#define IsValidateArray(array) (array && [array isKindOfClass:[NSArray class]] && [array count] > 0)

#define IsValidateDict(dict) (dict && [dict isKindOfClass:[NSDictionary class]] && [dict count] > 0)

#define IsValidateString(str) (str && [str isKindOfClass:[NSString class]] && [str length] > 0)

#define IsValidateNumber(num) (num && [num isKindOfClass:[NSNumber class]])

#define HexColor(string) [UIColor up_colorFromHexString:(string)]

#endif /* UPBaseSDKDefine_h */
