//
//  FMRelatedStockView.m
//  QCYZT
//
//  Created by zeng on 2022/3/15.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMRelatedStockView.h"
#import "UPMarketCommonHeader.h"

@interface RelatedStockInnerView : UIView

@property (nonatomic, strong) UIImageView *optionalImgV; // 是自选股显示的图片
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *changeRatioLabel;

@property (nonatomic, copy) UPMarketCodeMatchInfo *upStockModel;
@property (nonatomic, strong) UPHqStockHq *hqData;

@end

@implementation RelatedStockInnerView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUp];
    }
    
    return self;
}

- (void)setUp {
    self.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    self.userInteractionEnabled = YES;
    WEAKSELF
    [self addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
        if (![__weakSelf.upStockModel isKindOfClass:UPMarketCodeMatchInfo.class] || !__weakSelf.upStockModel.code.length) {
            return;
        }
        [UPRouterUtil goMarketStock:__weakSelf.upStockModel.setCode code:__weakSelf.upStockModel.code];

    }]];
    
    UIImageView *optionalImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"personal_stock_icon")];
    [self addSubview:optionalImgV];
    [optionalImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.left.equalTo(@7);
        make.width.equalTo(@9);
        make.height.equalTo(@10);
    }];
    self.optionalImgV = optionalImgV;
    
    UILabel *nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self addSubview:nameLabel];
    [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(optionalImgV.mas_right).offset(4);
        make.centerY.equalTo(@0);
        make.top.equalTo(@5);
    }];
    self.nameLabel = nameLabel;
    
    UILabel *changeRatioLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:UIColor.up_riseColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self addSubview:changeRatioLabel];
    [changeRatioLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(nameLabel.mas_right).offset(7);
        make.centerY.equalTo(@0);
        make.right.equalTo(@-7);
    }];
    self.changeRatioLabel = changeRatioLabel;
}

- (void)setUpStockModel:(UPMarketCodeMatchInfo *)upStockModel {
    _upStockModel = upStockModel;
    
    if ([FMSelfOptionStockCacheTool isAddedWithStockCode:upStockModel.code setCode:[NSString stringWithFormat:@"%d", upStockModel.setCode]]) {
        self.optionalImgV.hidden = NO;
        [self.optionalImgV mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@7);
            make.width.equalTo(@9);
        }];
    } else {
        self.optionalImgV.hidden = YES;
        [self.optionalImgV mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@3);
            make.width.equalTo(@0);
        }];
    }
    
    self.nameLabel.text = upStockModel.name;
}

- (void)setHqData:(UPHqStockHq *)hqData {
    _hqData = hqData;
    
    BOOL isStopStatus = [UPCommonMarketUtil isStopStatus:hqData.tradeStatus];
    if (isStopStatus) {
        self.changeRatioLabel.text = [UPMarketUIDataTool getStateWithTradeStatus:hqData.tradeStatus];
        self.changeRatioLabel.textColor = UIColor.up_textSecondaryColor;
    } else {
        self.changeRatioLabel.text = [UPMarketUICalculateUtil transPercent:hqData.changeRatio needSymbol:YES];
        self.changeRatioLabel.textColor = [UPMarketUICompareTool compareWithData:hqData.changeRatio baseData:0 precise:0];
    }
}

@end

@interface FMRelatedStockView ()

@property (nonatomic, assign) CGFloat viewHeight;

@property (nonatomic, strong) NSArray<UPMarketCodeMatchInfo *> *upStockModels;

@property (nonatomic, strong) NSDictionary *hqDic;


@end

@implementation FMRelatedStockView

- (void)configStockModels:(NSArray<UPMarketCodeMatchInfo *>*)upStockModels hqDic:(NSDictionary *)hqDic {
    _upStockModels = upStockModels;
    _hqDic = hqDic;
    
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self setUp];
}

- (void)setUp {
    for (NSInteger i = 0; i < self.upStockModels.count; i++) {
        UPMarketCodeMatchInfo *model = self.upStockModels[i];
        NSString *key = [FMUPDataTool jointWithSetCode:model.setCode code:model.code];
        RelatedStockInnerView *innerView = [[RelatedStockInnerView alloc] init];
        innerView.upStockModel = model;
        if (self.hqDic[key]) {
            innerView.hqData = self.hqDic[key];
        }

        [self addSubview:innerView];
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if (self.subviews.count) {
        CGFloat lastInnerViewRight = 0; // 前面一个innerView的右边
        CGFloat innerViewW = 0;
        CGFloat innerViewH = 0;
        NSInteger row = 0;
        
        for (int i=0; i<self.upStockModels.count; i++) {
            RelatedStockInnerView *innerView = self.subviews[i];
            innerView.hidden = NO;

            CGSize size = [innerView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize];
            innerViewW = size.width;
            innerViewH  = size.height;
            
            if (lastInnerViewRight + self.rightPadding + innerViewW > self.bounds.size.width) { // 如果超出自身宽度
                if (self.numberofLines != 1) { // 换行
                    row++;
                    if (row == self.numberofLines && row != 0) { // 达到限制行数
                        row--;
                        break;
                    }
                    lastInnerViewRight = 0;
                } else {
                    innerView.hidden = YES;
                }
            }
            
            CGFloat innerViewX = (lastInnerViewRight == 0) ? self.leftPadding : (lastInnerViewRight + self.middlePadding);
            CGFloat innerViewY = row * innerViewH + row * self.middlePadding + self.topPadding;
            
            innerView.frame = CGRectMake(innerViewX, innerViewY, innerViewW, innerViewH);
            
            if (!innerView.hidden) {
                lastInnerViewRight = innerViewX + innerViewW;
            }
        }
        _viewHeight = (row+1) * innerViewH + row * self.middlePadding + self.topPadding + self.bottomPadding;
    }
}


@end
