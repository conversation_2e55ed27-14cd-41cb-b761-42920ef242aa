//
//  FMAskCodeViewController.m
//  QCYZT
//
//  Created by zeng on 2021/11/15.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMAskCodeViewController.h"
#import "FMAskCodeListViewController.h"
#import "AskQuestionViewController.h"
#import "FMUPDataTool.h"

@interface FMAskCodeViewController ()<SGPageTitleViewDelegate,SGPageContentCollectionViewDelegate>

@property (nonatomic, strong) NSMutableArray *titleArr;
@property (nonatomic, strong) SGPageTitleView *pageTitleView;
@property (nonatomic, strong) SGPageContentCollectionView *pageContentCollectionView;
@property (nonatomic, strong) UIButton *questionBtn;
@property (nonatomic, assign) BOOL zxData; //是否有自选数据 提升为属性 在将要进入问股页面时 新手指引的UI显示判断

@property (nonatomic, assign) BOOL needsRefresh;

@end

@implementation FMAskCodeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.title = @"问股";
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    UIBarButtonItem *search = [[UIBarButtonItem alloc] initWithImage:ImageWithName(@"search") style:UIBarButtonItemStylePlain target:self action:@selector(jumpToSearch)];
    self.navigationItem.rightBarButtonItem = search;
    
    [self.view addSubview:self.questionBtn];
//    [self.questionBtn mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.right.equalTo(@-7.5);
//        make.bottom.equalTo(@-22.5);
//        make.width.height.equalTo(@65);
//    }];
    self.questionBtn.hidden = YES;
    
    [self requestSelfStocksRelationListCount];

    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleLoginStatusNotification) name:kFMUpdateSelfStockDatabaseNotification object:nil];
}


- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (self.needsRefresh) {
        self.needsRefresh = NO;
        [self requestSelfStocksRelationListCount];
    }
}

- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

- (void)askQuestion {
    AskQuestionViewController *vc = [[AskQuestionViewController alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
}

// 请求是否有自选股相关的问股
- (void)requestSelfStocksRelationListCount {
    if ([FMHelper isLogined]) {
        [HttpRequestTool getSelfStocksQuestionCountWithKeyWord:[FMUPDataTool selfStockDataFormaterCodeColonName] start:^{
            [SVProgressHUD show];
        } failure:^{
            [SVProgressHUD dismiss];
            [self configData:NO];
        } success:^(NSDictionary *dic) {
            [SVProgressHUD dismiss];
            if ([dic[@"status"] isEqualToString:@"1"] && [dic[@"data"] integerValue] > 0) { // 有数据
                [self configData:YES];
            } else {
                [self configData:NO];
            }
        }];
    } else {
        [self configData:NO];

    }
}

- (void)configData:(BOOL)zxData {
    [self.titleArr removeAllObjects];
    self.zxData = zxData;
    NSDictionary *rm = @{@"title" : @"热门", @"askCodeListType" : @"2"};
    NSDictionary *zx = @{@"title" : @"自选", @"askCodeListType" : @"8"};
    NSDictionary *wowd = @{@"title" : @"我问的", @"askCodeListType" : @"4"};
    NSDictionary *wwod = @{@"title" : @"问我的", @"askCodeListType" : @"6"};

    if ([FMUserDefault getUserFlag]) {
        if ([FMSelfOptionStockCacheTool getSelfStocks].count) {
            if (zxData) {
                [self.titleArr addObjectsFromArray:@[rm, zx, wwod]];
            } else {
                [self.titleArr addObjectsFromArray:@[rm, wwod]];
            }
        } else {
            [self.titleArr addObjectsFromArray:@[rm, wwod]];
        }
    } else {
        if ([FMSelfOptionStockCacheTool getSelfStocks].count) {
            if (zxData) {
                [self.titleArr addObjectsFromArray:@[rm, zx, wowd]];
            } else {
                [self.titleArr addObjectsFromArray:@[rm, wowd]];
            }
        } else {
            [self.titleArr addObjectsFromArray:@[rm, wowd]];
        }
    }
    
    [self setupPageView];
    
    self.questionBtn.hidden = [FMUserDefault getUserFlag];
    [self.view bringSubviewToFront:self.questionBtn];
}

-(void)setupPageView {
    [self.pageTitleView removeFromSuperview];
    [self.pageContentCollectionView removeFromSuperview];
    self.pageTitleView = nil;
    self.pageContentCollectionView = nil;

    NSArray *titles = [self.titleArr valueForKeyPath:@"title"];
    SGPageTitleViewConfigure *configure = [SGPageTitleViewConfigure pageTitleViewConfigure];
    configure.titleColor = UIColor.up_textSecondaryColor;
    configure.titleFont = [FMHelper scaleFont:16.0];
    configure.titleSelectedColor = UIColor.up_textPrimaryColor;
    configure.titleSelectedFont = [FMHelper scaleBoldFont:18];
    configure.indicatorStyle = SGIndicatorStyleFixed;
    configure.indicatorColor = FMNavColor;
    configure.indicatorFixedWidth = 18;
    configure.indicatorHeight = 3;
    configure.indicatorCornerRadius = 1.5;
    configure.titleAdditionalWidth = 30;
    configure.equivalence = NO;
    configure.showBottomSeparator = NO;
    self.pageTitleView = [SGPageTitleView pageTitleViewWithFrame:CGRectMake(0, 10, UI_SCREEN_WIDTH, 45) delegate:self titleNames:titles configure:configure];
    self.pageTitleView.backgroundColor = UIColor.up_contentBgColor;
    [self.view addSubview:self.pageTitleView];
    self.pageContentCollectionView = [[SGPageContentCollectionView alloc] initWithFrame:CGRectMake(0, 55, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT-(UI_SAFEAREA_TOP_HEIGHT + 55 + UI_SAFEAREA_BOTTOM_HEIGHT)) parentVC:self childVCs:[self addChildVC]];
    self.pageContentCollectionView.delegatePageContentCollectionView = self;
    [self.view addSubview:self.pageContentCollectionView];
    
    for (NSInteger i = 0; i < titles.count; i++) {
        if ([self.typeStr isEqualToString:titles[i]]) {
            self.pageTitleView.selectedIndex = i;
            break;
        }
    }
}

- (NSArray *)addChildVC {
    for (UIViewController *subVc in self.childViewControllers) {
        [subVc.view removeFromSuperview];
        [subVc removeFromParentViewController];
    }
    
    for (NSDictionary *dic in self.titleArr) {
        FMAskCodeListViewController *vc = [[FMAskCodeListViewController alloc] init];
        vc.askCodeListType = [dic[@"askCodeListType"] integerValue];
        [self addChildViewController:vc];
    }

    return self.childViewControllers;
}

- (void)jumpToSearch {
    [ProtocolJump jumpWithUrl:@"qcyzt://search"];
}

#pragma mark - SGPageTitleViewDelegate
- (void)pageTitleView:(SGPageTitleView *)pageTitleView selectedIndex:(NSInteger)selectedIndex{
    [self.pageContentCollectionView setPageContentCollectionViewCurrentIndex:selectedIndex];
}

#pragma mark - SGPageContentCollectionViewDelegate
- (void)pageContentCollectionView:(SGPageContentCollectionView *)pageContentCollectionView progress:(CGFloat)progress originalIndex:(NSInteger)originalIndex targetIndex:(NSInteger)targetIndex {
    [self.pageTitleView setPageTitleViewWithProgress:progress originalIndex:originalIndex targetIndex:targetIndex];
}

#pragma mark - Getter
- (NSMutableArray *)titleArr {
    if (!_titleArr) {
        _titleArr = [NSMutableArray array];
    }
    
    return _titleArr;
}

- (UIButton *)questionBtn {
    if (!_questionBtn) {
        _questionBtn = [[UIButton alloc] initWithFrame:CGRectMake(UI_SCREEN_WIDTH - 65 - 7.5, UI_SCREEN_HEIGHT - UI_SAFEAREA_TOP_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT - 22.5-65, 65, 65) font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"askcode_question") target:self action:@selector(askQuestion)];
    }
    
    return _questionBtn;
}

@end
