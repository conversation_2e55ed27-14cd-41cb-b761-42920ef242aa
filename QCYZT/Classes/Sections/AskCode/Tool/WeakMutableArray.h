//
//  WeakMutableArray.h
//  QCYZT
//
//  Created by macPro on 2017/11/6.
//  Copyright © 2017年 sdcf. All rights reserved.
//  数组对内部元素都是弱引用

#import <Foundation/Foundation.h>

@interface WeakMutableArray : NSObject

@property (nonatomic, strong, readonly)  NSArray    *allObjects;

@property (nonatomic, readonly)          NSInteger   usableCount; // 有效对象个数，不包含NULL

@property (nonatomic, readonly)          NSInteger   allCount; //数组中所有对象的个数(包括NULL)

- (void)addObject:(id)object;

- (BOOL)containsObject:(id)object;

- (void)removeObject:(id)object;

- (id)objectAtWeakMutableArrayIndex:(NSUInteger)index; // 包括NULL




@end
