//
//  WeakMutableArray.m
//  QCYZT
//
//  Created by macPro on 2017/11/6.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "WeakMutableArray.h"

@interface WeakMutableArray()

@property (nonatomic, strong) NSPointerArray  *pointerArray;

@end

@implementation WeakMutableArray

- (instancetype)init {
    if (self = [super init]) {
        self.pointerArray = [NSPointerArray weakObjectsPointerArray];
    }
    
    return self;
}

- (void)addObject:(id)object {
    [self.pointerArray addPointer:(__bridge void *)(object)];
}

- (void)removeObject:(id)object {
    for (long i = self.pointerArray.count - 1; i >= 0; i--) {
        id obj = [self.pointerArray pointerAtIndex:i];
        if (object == obj) {
            [self.pointerArray removePointerAtIndex:i];
        }
    }
}

- (BOOL)containsObject:(id)object {
    for (long i = 0; i < self.pointerArray.count; i++) {
        id obj = [self.pointerArray pointerAtIndex:i];
        if ([object isEqual: obj]) {
            return YES;
        }
    }
    
    return NO;
}

- (id)objectAtWeakMutableArrayIndex:(NSUInteger)index {
    return [self.pointerArray pointerAtIndex:index];
}

#pragma mark - 重写getter方法
- (NSArray *)allObjects {
    return self.pointerArray.allObjects;
}

- (NSInteger)usableCount {
    return self.pointerArray.allObjects.count;
}

- (NSInteger)allCount {
    return self.pointerArray.count;
}


@end
