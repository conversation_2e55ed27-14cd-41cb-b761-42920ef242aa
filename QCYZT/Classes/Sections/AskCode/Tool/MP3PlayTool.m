//
//  MP3PlayTool.m
//  播放远程音乐
//
//  Created by th on 16/12/29.
//  Copyright © 2016年 zl. All rights reserved.
//

#import <MediaPlayer/MediaPlayer.h>
#import "FileManagerTool.h"
@interface MP3PlayTool ()

@property(nonatomic,strong) CADisplayLink * timer;
@property (nonatomic,strong) AVPlayer *player;

@property (nonatomic, assign) CGFloat loadProgress;
@property (nonatomic, assign) CGFloat playProgress;
@property (nonatomic, assign) CGFloat playTime;
@property (nonatomic, assign) MP3PlayToolPlayStatus playStatus;

@property (nonatomic, assign) BOOL isConfigLockScreenControl; // 是否配置过锁屏控制，多次配置在iOS12上有问题

@end


static MP3PlayTool * _mp = nil;

@implementation MP3PlayTool
+ (instancetype)shareMusicPlay {
    if (_mp == nil) {
        static dispatch_once_t once_token;
        dispatch_once(&once_token, ^{
            _mp = [[MP3PlayTool alloc] init];
        });
    }
    return _mp;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _mp = [super allocWithZone:zone];
    });
    return _mp;
}

- (id)copyWithZone:(NSZone *)zone {
    return _mp;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _player = [[AVPlayer alloc] init];
        _urlDic = [NSMutableDictionary dictionary];
        _playStatus = MP3PlayToolPlayStatusStopped;
        
        // 支持后台播放
        AVAudioSession *audioSession = [AVAudioSession sharedInstance];
        [audioSession setCategory:AVAudioSessionCategoryPlayback error:nil];
        [audioSession setActive:YES error:nil];
        
        // 接收远程事件，处理锁屏播放
        [[UIApplication sharedApplication] beginReceivingRemoteControlEvents];
        
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(handleInterruption:)
                                                     name:AVAudioSessionInterruptionNotification
                                                   object:[AVAudioSession sharedInstance]];
        
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(handleRouteChange:)
                                                     name:AVAudioSessionRouteChangeNotification
                                                   object:[AVAudioSession sharedInstance]];
    }
    return self;
}

#pragma mark - KVO，监听播放准备状态和缓冲进度
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context {
    if ([keyPath isEqualToString:@"status"]) {
        switch ([[change valueForKey:@"new"] integerValue]) {
            case AVPlayerItemStatusUnknown:
                FMLog(@"不知道什么错误");
                break;
            case AVPlayerItemStatusReadyToPlay:
                FMLog(@"准备播放");
                break;
            case AVPlayerItemStatusFailed:
                FMLog(@"准备失败");
                break;
            default:
                break;
        }
    }
    
    if ([keyPath isEqualToString:@"loadedTimeRanges"]) {
        AVPlayerItem *item = (AVPlayerItem *)object;
        AVURLAsset *asset = (AVURLAsset *)item.asset;
        if ([asset.URL.scheme isEqualToString:@"file"]) {
            self.loadProgress = 1.0f;
            return;
        }
        
        NSArray * timeRanges = self.player.currentItem.loadedTimeRanges;
        
        //本次缓冲的时间范围
        CMTimeRange timeRange = [timeRanges.firstObject CMTimeRangeValue];
        //缓冲总长度
        NSTimeInterval totalLoadTime = CMTimeGetSeconds(timeRange.start) + CMTimeGetSeconds(timeRange.duration);
        //音乐的总时间
        NSTimeInterval duration = CMTimeGetSeconds(self.player.currentItem.duration);
        //计算缓冲百分比例
        NSTimeInterval scale = totalLoadTime/duration;
        
        //更新缓冲进度条
        self.loadProgress = scale;
    }
}

#pragma mark - Notification
// 播放结束通知
- (void)endOfPlay:(NSNotification *)sender {
    if (self.playProgress != 1.0f) {
        self.playProgress = 1.0f; // 播放结束时，根据self.playTime/self.totalTime计算的playProgress通常不会是1.0，在这设置为1.0
    }
    
//    NSLog(@"播放完毕");
    [self seekToBeginAndPause];
}

// 电话、闹钟等中断通知
- (void)handleInterruption:(NSNotification *)noti {
    NSDictionary *info = noti.userInfo;
    AVAudioSessionInterruptionType type = [info[AVAudioSessionInterruptionTypeKey] unsignedIntegerValue];
    if (type == AVAudioSessionInterruptionTypeBegan) {
        if (self.playStatus == MP3PlayToolPlayStatusPlaying) {
            [self musicPause];
        }
    } else if (type == AVAudioSessionInterruptionTypeEnded) {
        AVAudioSessionInterruptionOptions options = [info[AVAudioSessionInterruptionOptionKey] unsignedIntegerValue];
        if (options == AVAudioSessionInterruptionOptionShouldResume) {
            if (self.playStatus == MP3PlayToolPlayStatusPaused) {
                [self musicPlay];
                if (self.lockPlay) {
                    self.lockPlay();
                }
            }
        }
    }
}

// 线路改变（连上耳机等）通知
- (void)handleRouteChange:(NSNotification *)notification {
    NSDictionary *info = notification.userInfo;
    AVAudioSessionRouteChangeReason reason =
    [info[AVAudioSessionRouteChangeReasonKey] unsignedIntValue];
    if (reason == AVAudioSessionRouteChangeReasonOldDeviceUnavailable) {
        //获取前一个线路的设备
        AVAudioSessionRouteDescription *previousRoute = info[AVAudioSessionRouteChangePreviousRouteKey];
        //获取线路的音频接口类型
        if (previousRoute.outputs.firstObject) { //firstObject is nil when empty
            AVAudioSessionPortDescription *previousOutput = previousRoute.outputs.firstObject;
             // 现在可以安全地使用 previousOutput
            //获取音频接口类型
            NSString *portType = previousOutput.portType;
            //如果是耳机、蓝牙接口
            if ([portType isEqualToString:AVAudioSessionPortHeadphones]||[portType isEqualToString:AVAudioSessionPortBluetoothA2DP]||[portType isEqualToString:AVAudioSessionPortBluetoothLE]||[portType isEqualToString:AVAudioSessionPortBluetoothHFP]) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self musicPause];
                });
            }
        } else {
                // 处理 previousRoute.outputs 为空的情况
            NSLog(@"Warning: previousRoute.outputs is empty!");
        }
    }
}

#pragma mark - Private
- (void)musicPlay {
    
    // 视频播放和语音播放互斥
    [self.videoPlayerManager pause];
    
    if (self.timer == nil) {
        self.timer = [CADisplayLink displayLinkWithTarget:self selector:@selector(timerAction:)];
        [self.timer addToRunLoop:[NSRunLoop mainRunLoop] forMode:NSRunLoopCommonModes];
    }
    
    [self.player play];
    self.playStatus = MP3PlayToolPlayStatusPlaying;
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES]; // 防止锁屏
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kMP3MusicPlay object:nil];
    
}


- (void)musicPause {
    [self.timer invalidate];
    self.timer = nil;
    
    [self.player pause];
    self.playStatus = MP3PlayToolPlayStatusPaused;
    [[UIApplication sharedApplication] setIdleTimerDisabled:NO];
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kMP3MusicPuase object:nil];
}

- (void)musicStop {
    [self.timer invalidate];
    self.timer = nil;
    
    [self.player pause];
    [[UIApplication sharedApplication] setIdleTimerDisabled:NO];
    
    [self.player.currentItem removeObserver:self forKeyPath:@"status"];
    [self.player.currentItem removeObserver:self  forKeyPath:@"loadedTimeRanges"];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemDidPlayToEndTimeNotification object:self.player.currentItem];
    
    [self.player replaceCurrentItemWithPlayerItem:nil];
    self.playProgress = 0;
    self.playTime = 0;
    self.totalTime = 0;
    self.loadProgress = 0;
    self.playStatus = MP3PlayToolPlayStatusStopped;
    self.currentPlayUrl = nil;
    
    [[NSNotificationCenter defaultCenter] postNotificationName:kMP3MusicStop object:nil];
    
    [self cleanLockScreenInfoAndControl];
}

- (void)seekToTimeWithProgress:(CGFloat)progress completionBlock:(void (^)())completionBlock{
    if (self.player.status != AVPlayerStatusReadyToPlay) {
        return;
    }
    [self.player seekToTime:CMTimeMake(progress * [self getTotleTime], 1) completionHandler:^(BOOL finished) {
        self.playProgress = self.playTime / [self getTotleTime];

        completionBlock();
        
        [self configLockScreenInfo];
    }];
}

- (void)seekToTime:(CGFloat)time completionBlock:(void (^)())completionBlock{
    if (self.player.status != AVPlayerStatusReadyToPlay) {
        return;
    }
    [self.player seekToTime:CMTimeMake((int)time, 1) completionHandler:^(BOOL finished) {
        self.playProgress = self.playTime / [self getTotleTime];

        completionBlock();
        
        [self configLockScreenInfo];
    }];
}

- (void)seekToBeginAndPause {
    self.playStatus = MP3PlayToolPlayStatusStopped; // 先设置为Stopped，外部根据playStatus从Stopped变成Pause来判断是播放完毕回到待播发状态
    [self musicPause];
    [self.player seekToTime:CMTimeMake(0, 1) completionHandler:^(BOOL finished) {
        if (self.playProgress != 0) {
            self.playProgress = 0;
        }

        FMLog(@"播放完毕回到开头");
        [self configLockScreenInfo];
    }];
}

- (void)timerAction:(NSTimer * )sender {
    self.playProgress = self.playTime / [self getTotleTime];
    
    [self configLockScreenInfo];
}

- (CGFloat)playTime {
    if (self.player.currentItem) {
        _playTime = CMTimeGetSeconds(self.player.currentItem.currentTime);
        return _playTime;
    }
    return 0;
}

// 获取总时长
-(CGFloat)getTotleTime {
    CMTime totalTime = self.player.currentItem.duration;
    if (totalTime.timescale == 0) {
        return 1;
    } else {
        if (self.totalTime != 0) {
            return (CGFloat)self.totalTime;
        } else {
            self.totalTime = CMTimeGetSeconds(totalTime);
            return (CGFloat)self.totalTime;
        }
    }
}

// 将整数秒转换为 00:00 格式的字符串
-(NSString *)valueToString:(NSInteger)value {
    return [NSString stringWithFormat:@"%.2zd:%.2zd",value/60,value%60];
}

#pragma mark - Setter
- (void)setCurrentPlayUrl:(NSString *)currentPlayUrl {
    _currentPlayUrl = currentPlayUrl;
    if (currentPlayUrl.length > 0) {
        if (self.player.currentItem) {
            [self.player.currentItem removeObserver:self forKeyPath:@"status"];
            [self.player.currentItem removeObserver:self forKeyPath:@"loadedTimeRanges"];
            [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemDidPlayToEndTimeNotification object:self.player.currentItem];
        }
        
        // 判断currentPlayUrl对应是否有缓存
        NSArray *arr  = [currentPlayUrl componentsSeparatedByString:@"/"];
        NSString *path = [FileManagerTool getPathAtCachesWithDirectoryName:@"MP3" fileName:arr.lastObject];
        AVPlayerItem *item = nil;
        if (path.length) { // 已经下载，加载本地MP3
            item = [[AVPlayerItem alloc] initWithURL:[NSURL fileURLWithPath:path]];
        } else { // 未下载则加载网络音频
            item = [[AVPlayerItem alloc] initWithURL:[NSURL URLWithString:self.currentPlayUrl]];
            [self downLoadAudiofile:[FileManagerTool createDirAtCachesWithDirectoryName:@"MP3"]];
        }
        [self.player replaceCurrentItemWithPlayerItem:item];
        [item addObserver:self forKeyPath:@"status" options:(NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld) context:nil];
        [item addObserver:self forKeyPath:@"loadedTimeRanges" options:NSKeyValueObservingOptionNew context:nil]; // 缓冲进度
        
        if([[UIDevice currentDevice] systemVersion].intValue >= 10 && [currentPlayUrl rangeOfString:@".m3u8"].location == NSNotFound){
            // 增加下面这行可以解决iOS10兼容性问题了(如果使用的是HLS(m3u8)协议的，是不会由于升级ios10出现这个播放问题的。)
            self.player.automaticallyWaitsToMinimizeStalling = NO;
        }
        if ([[UIDevice currentDevice] systemVersion].intValue >= 10 && [currentPlayUrl rangeOfString:@".m3u8"].location != NSNotFound) {
            self.player.automaticallyWaitsToMinimizeStalling = YES;
        }
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(endOfPlay:) name:AVPlayerItemDidPlayToEndTimeNotification object:self.player.currentItem];
    }
}

- (void)downLoadAudiofile:(NSString *)catchPath{
    [HttpRequestTool downLoadFileRequestWithOperations:nil SavePath:catchPath UrlString:self.currentPlayUrl success:^(NSDictionary *dic) {
        FMLog(@"mp3下载完成");
    } failure:^{
    } progress:^(float progress) {
    }];
}


#pragma mark - LockScreen
- (void)cleanLockScreenInfoAndControl {
    [MPNowPlayingInfoCenter defaultCenter].nowPlayingInfo = nil;
    
    MPRemoteCommandCenter *commandCenter = [MPRemoteCommandCenter sharedCommandCenter];
    commandCenter.pauseCommand.enabled = NO;
    commandCenter.playCommand.enabled = NO;
    commandCenter.togglePlayPauseCommand.enabled = NO;
    commandCenter.skipBackwardCommand.enabled = NO;
    commandCenter.skipForwardCommand.enabled = NO;
    commandCenter.changePlaybackPositionCommand.enabled = NO;
    self.isConfigLockScreenControl = NO;
}

- (void)configLockScreenInfo {
    // 1.获取锁屏界面中心
    MPNowPlayingInfoCenter *playingInfoCenter = [MPNowPlayingInfoCenter defaultCenter];

    // 1.1.设置展示的信息
    NSMutableDictionary *playingInfo = [NSMutableDictionary dictionary];
    [playingInfo setObject:self.artist ? self.artist : @"" forKey:MPMediaItemPropertyAlbumTitle];
    [playingInfo setObject:self.title ? self.title : @"" forKey:MPMediaItemPropertyTitle];

    MPMediaItemArtwork *artWork = nil;
    if (self.lockImage == nil) {
        artWork = [[MPMediaItemArtwork alloc] initWithBoundsSize:CGSizeMake(40, 40) requestHandler:^UIImage * _Nonnull(CGSize size) {
            return [UIImage imageNamed:@"lock_logo"];
        }];
    } else {
        WEAKSELF
        artWork = [[MPMediaItemArtwork alloc] initWithBoundsSize:CGSizeMake(40, 40) requestHandler:^UIImage * _Nonnull(CGSize size) {
            return __weakSelf.lockImage;
        }];
    }
    [playingInfo setObject:artWork forKey:MPMediaItemPropertyArtwork];

    if (self.playTime >= 0) {
//        FMLog(@"设置锁屏信息");
        [playingInfo setObject:@(ceil([self getTotleTime])) forKey:MPMediaItemPropertyPlaybackDuration]; // 与
        [playingInfo setObject:@(self.playTime) forKey:MPNowPlayingInfoPropertyElapsedPlaybackTime];
    }
    playingInfoCenter.nowPlayingInfo = playingInfo;
    
    if (!self.isConfigLockScreenControl) {
        [self configLockScreenControl];
    }
}

- (void)configLockScreenControl {
    self.isConfigLockScreenControl = YES;
    
    // 2.获取锁屏远程控制中心
    MPRemoteCommandCenter *commandCenter = [MPRemoteCommandCenter sharedCommandCenter];
    commandCenter.pauseCommand.enabled = YES;
    commandCenter.playCommand.enabled = YES;
    commandCenter.togglePlayPauseCommand.enabled = YES;
    commandCenter.skipBackwardCommand.enabled = YES;
    commandCenter.skipForwardCommand.enabled = YES;
    commandCenter.changePlaybackPositionCommand.enabled = YES;

    // 2.1.暂停，不用addTargetWithHandler，因为会多次进入block
    [commandCenter.pauseCommand addTarget:self action:@selector(lockScreenPause:)];
    // 2.2.播放
    [commandCenter.playCommand addTarget:self action:@selector(lockScreenPlay:)];
    // 2.3.线控
    [commandCenter.togglePlayPauseCommand addTarget:self action:@selector(handleTogglePlayPause:)];

    if (self.lockCanDrag) {
        // 2.4.1快进
        commandCenter.skipForwardCommand.preferredIntervals = @[@(15)];
        [commandCenter.skipForwardCommand addTarget:self action:@selector(skipForward:)];
        // 2.4.2快退
        commandCenter.skipBackwardCommand.preferredIntervals = @[@(15)];
        [commandCenter.skipBackwardCommand addTarget:self action:@selector(skipBackward:)];
        // 2.5.拖拽改变播放位置
        [commandCenter.changePlaybackPositionCommand addTarget:self action:@selector(changePlaybackPosition:)];
    }
}

- (MPRemoteCommandHandlerStatus)lockScreenPause:(MPRemoteCommandEvent *)event {
    FMLog(@"锁屏暂停");
    if (self.playStatus == MP3PlayToolPlayStatusPlaying) {
        [self musicPause];
    }
    return MPRemoteCommandHandlerStatusSuccess;
}

- (MPRemoteCommandHandlerStatus)lockScreenPlay:(MPRemoteCommandEvent *)event {
    FMLog(@"锁屏播放");
    if (self.playStatus != MP3PlayToolPlayStatusPlaying) {
        [self musicPlay];
        if (self.lockPlay) {
            self.lockPlay();
        }
    }
    return MPRemoteCommandHandlerStatusSuccess;
}

- (MPRemoteCommandHandlerStatus)handleTogglePlayPause:(MPRemoteCommandEvent *)event {
    FMLog(@"线控播放与暂停---%zd", self.playStatus);
    if (self.playStatus == MP3PlayToolPlayStatusPlaying) {
        [self musicPause];
    } else {
        [self musicPlay];
        if (self.lockPlay) {
            self.lockPlay();
        }
    }
    return MPRemoteCommandHandlerStatusSuccess;
}

- (MPRemoteCommandHandlerStatus)skipForward:(MPRemoteCommandEvent *)event {
    FMLog(@"锁屏快进");
    if (self.playTime + 15 <= self.totalTime) {
        [self seekToTime:self.playTime + 15 completionBlock:^{

        }];
    }
    return MPRemoteCommandHandlerStatusSuccess;
}

- (MPRemoteCommandHandlerStatus)skipBackward:(MPRemoteCommandEvent *)event {
    FMLog(@"锁屏快退");
    if (self.playTime - 15 >= 0) {
        [self seekToTime:self.playTime - 15 completionBlock:^{

        }];
    }
    return MPRemoteCommandHandlerStatusSuccess;
}

- (MPRemoteCommandHandlerStatus)changePlaybackPosition:(MPRemoteCommandEvent *)event {
    MPChangePlaybackPositionCommandEvent * playbackPositionEvent = (MPChangePlaybackPositionCommandEvent *)event;
    FMLog(@"锁屏拖拽--%f", playbackPositionEvent.positionTime);
    [self.player seekToTime:CMTimeMake(playbackPositionEvent.positionTime, 1) completionHandler:^(BOOL finished) {

    }];
    return MPRemoteCommandHandlerStatusSuccess;
}

@end

