//
//  MP3PlayTool.h
//  播放远程音乐
//
//  Created by th on 16/12/29.
//  Copyright © 2016年 zl. All rights reserved.
//  缓冲和播放进度没有使用代理而采用KVO的原因是KVO更方便设置多个监听者，而此处代理只能设置一个

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>
#import "ZFAVPlayerManager.h"

typedef NS_ENUM(NSInteger, MP3PlayToolPlayStatus) {
    MP3PlayToolPlayStatusPlaying,  // 播放中
    MP3PlayToolPlayStatusPaused,   // 已暂停
    MP3PlayToolPlayStatusStopped    // 已停止
};

@interface MP3PlayTool : NSObject

@property(nonatomic,strong,readonly) AVPlayer * player;
@property (nonatomic,assign) CGFloat totalTime; // 外界传入就用外界的
@property (nonatomic,copy) NSString *currentPlayUrl;
@property (nonatomic, strong) NSMutableDictionary *urlDic;

// 播放的一些状态
@property (nonatomic, assign, readonly) CGFloat loadProgress;
@property (nonatomic, assign, readonly) CGFloat playProgress;
@property (nonatomic, assign, readonly) CGFloat playTime; // 当前播放的时间，真实播放时间，不用playProgress*总时间（因为总时间不准）
@property (nonatomic, assign, readonly) MP3PlayToolPlayStatus playStatus; // 当前状态

// 锁屏信息
@property (nonatomic, copy) NSString *artist;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) UIImage *lockImage;
@property (nonatomic, assign) BOOL lockCanDrag; // 锁屏时可以拖拽
@property (nonatomic, copy) void(^lockPlay)(void); // 锁屏时播放
@property (nonatomic, copy) void(^lockPause)(void); // 锁屏时暂停
// 视频播放管理类
@property (nonatomic, strong) ZFAVPlayerManager *videoPlayerManager;



+ (instancetype)shareMusicPlay;

// 播放音乐
- (void)musicPlay;
// 暂停音乐
- (void)musicPause;
// 停止
- (void)musicStop;

- (void)seekToTimeWithProgress:(CGFloat)progress completionBlock:(void (^)())completionBlock;

- (void)seekToTime:(CGFloat)time completionBlock:(void (^)())completionBlock;

@end

