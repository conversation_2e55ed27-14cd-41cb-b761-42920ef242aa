//
//  FMAskCodeDetailCell.m
//  QCYZT
//
//  Created by th on 16/12/27.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import "FMAskCodeDetailHeaderCell.h"
#import "FMAskCodeModel.h"
#import "NSString+characterJudge.h"
#import "DakaInfoNewView.h"

@interface FMAskCodeDetailHeaderCell() <UIAlertViewDelegate>

@property (weak, nonatomic) UILabel *questionerLabel; // 提问人

@property (weak, nonatomic) YYLabel *detailLB; // 内容详情

@property (weak, nonatomic) DakaInfoNewView *infoView;  // 头像、姓名、头衔、个人简介

@property (weak, nonatomic) FocusButton *focusBtn; // 关注按钮

@end

@implementation FMAskCodeDetailHeaderCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.contentView.backgroundColor = UIColor.up_contentBgColor;

    // 提问人
    UILabel *questionLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:14] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:questionLabel];
    [questionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@15);
        make.right.equalTo(@-15);
    }];
    self.questionerLabel = questionLabel;
    
    // 详情
    YYLabel *detailLB = [[YYLabel alloc] init];
    detailLB.numberOfLines = 0;
    detailLB.preferredMaxLayoutWidth = UI_SCREEN_WIDTH - 30;
    [self.contentView addSubview:detailLB];
    [detailLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(questionLabel);
        make.top.equalTo(questionLabel.mas_bottom).offset(15);
        make.right.equalTo(@(-15));
    }];
    self.detailLB = detailLB;
    
    // 头像、姓名、头衔、个人简介
    DakaInfoNewView *infoView = [[DakaInfoNewView alloc] init];
    [self.contentView addSubview:infoView];
    [infoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(detailLB.mas_bottom).offset(15);
        make.left.equalTo(@15);
//        make.height.equalTo(@(42));
        make.bottom.equalTo(@-15);
    }];
    infoView.nameLabel.textColor = UIColor.up_textPrimaryColor;
    self.infoView = infoView;
    
    // 关注
    FocusButton *focusBtn = [FocusButton buttonWithType:UIButtonTypeCustom];
    CGSize focusSize = [FMHelper isBigFont] ? CGSizeMake(75, 30) : CGSizeMake(65, 30);
    focusBtn.size = focusSize;
    focusBtn.text = @"关注";
    focusBtn.focusText = @"已关注";
    focusBtn.textColor = FMNavColor;
    focusBtn.focusTextColor = UIColor.fm_stock_calendar_textDisabledColor;
    focusBtn.image = [UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"add_focus")];
    focusBtn.focusImage = ImageWithName(@"");
    focusBtn.boardColor =  FMNavColor;
    focusBtn.focusBoardColor = UIColor.fm_stock_calendar_textDisabledColor;
    focusBtn.titleLabel.font = [FMHelper scaleFont:14.0];
    [self.contentView addSubview:focusBtn];
    [focusBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@(-15));
        make.centerY.equalTo(infoView).offset(-5);
        make.height.equalTo(@30);
        make.width.equalTo(@75);
    }];
    self.focusBtn = focusBtn;
}

- (void)setModel:(FMAskCodeModel *)model {
    _model = model;
    
    if (model.questionUserName.length > 0) {
        NSString *str = [NSString stringWithFormat:@"来自%@的提问",model.questionUserName];
        NSRange range = [str rangeOfString:model.questionUserName];
        NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:str];
        [attrStr addAttributes:@{NSForegroundColorAttributeName:ColorWithHex(0x0076ff)} range:range];
        self.questionerLabel.attributedText = attrStr;
    }
    
    if (model.questionContent.length > 0) {
        self.detailLB.attributedText = model.questionContentAttrStr;
    }
    
    // 头像、名称、头衔、简介
    [self.infoView.iconImgV.iconImg sd_setImageWithURL:[NSURL URLWithString:model.bignameDto.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.infoView.nameLabel.text = model.bignameDto.userName;
    self.infoView.userId = model.bignameDto.userId;
    self.infoView.islive = model.bignameDto.isLive;
    self.infoView.tagView.hidden = YES;
//    self.infoView.tags = [[model.bignameDto.userGoodAt componentsSeparatedByString:@"、"] bk_reject:^BOOL(NSString * _Nonnull obj) {
//        return !obj.length;
//    }];
    self.infoView.attestationType = model.bignameDto.attestationType;
    
    
    NSDate *nowDate = [NSDate dateWithTimeIntervalSince1970:model.answerTime / 1000];
    NSString *timeStr = nil;
    if ([nowDate isToday]) {
        timeStr = [NSString stringFromDate:nowDate format:@"HH:mm"];
    } else {
        if ([nowDate isThisYear]) {
            timeStr = [NSString stringFromDate:nowDate format:@"MM-dd HH:mm"];
        } else {
            timeStr = [NSString stringFromDate:nowDate format:@"yyyy-MM-dd"];
        }
    }
    if (model.contentFileType.integerValue != 1) {
        timeStr = [timeStr stringByAppendingString:@" 回答"];
    }
    self.infoView.timeLabel.text = timeStr;
    
    [self updateUIWithFollowStatus];
}

- (void)focusBtnClicked:(UIButton *)sender {
    if ([[FMUserDataSyncManager sharedManager] isDakaNoticed:self.model.bignameDto.userId]) {
        UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:nil message:@"确定不再关注此投顾？" delegate:self cancelButtonTitle:@"取消" otherButtonTitles:@"确定", nil];
        [alertView show];
    } else {
        [FMCommonHttp focusBtnWithButton:sender bigCastId:self.model.bignameDto.userId roomId:nil focusSuccess:^{
            [self updateUIWithFollowStatus];
        }];
    }
}

- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex {
    if (buttonIndex == 1) {
        [FMCommonHttp UnfollowWithButton:self.focusBtn bigCastId:self.model.bignameDto.userId unfollowSuccess:^{
            [self updateUIWithFollowStatus];
        }];
    }
}

- (void)enterToDakaHomePage {
    [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://bigname?id=%@", self.model.bignameDto.userId]];
}

// 更新关注相关UI，（关注按钮，关注数量）
- (void)updateUIWithFollowStatus {
    self.focusBtn.dakaId = self.model.bignameDto.userId;
    self.focusBtn.isFocus = [[FMUserDataSyncManager sharedManager] isDakaNoticed:self.model.bignameDto.userId];
}

@end

