//
//  FMAskCodeDetailBottomCell.m
//  QCYZT
//
//  Created by macPro on 2017/4/20.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "FMAskCodeDetailBottomCell.h"
#import "FMAskCodeModel.h"

@interface FMAskCodeDetailBottomCell()

@property (nonatomic, weak) UILabel *descLabel; // 声明
@property (nonatomic, strong) UILabel *watchListenLabel;
@property (nonatomic, strong) UIButton *praiseBtn;

@end

@implementation FMAskCodeDetailBottomCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    // 免责声明
    UILabel *descLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:descLabel];
    [descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@12);
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
    }];
    [descLabel setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisVertical];
    self.descLabel = descLabel;
    
    UIView *sepline = [self.contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.right.equalTo(descLabel);
        make.top.equalTo(descLabel.mas_bottom).offset(20);
        make.height.equalTo(@0.7);
    }];
    sepline.backgroundColor = UIColor.fm_sepline_color;
    
    UILabel *watchListenLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:watchListenLabel];
    [watchListenLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(descLabel.mas_bottom).offset(40);
        make.left.equalTo(descLabel);
    }];
    self.watchListenLabel = watchListenLabel;
    
    // 超值按钮
    UIButton *praiseBtn = [[UIButton alloc] init];
    [self.contentView addSubview:praiseBtn];
    [praiseBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(descLabel);
        make.top.equalTo(descLabel.mas_bottom).offset(32.5);
        make.bottom.equalTo(@-12.5);
    }];
    praiseBtn.titleLabel.font = [FMHelper scaleFont:12];
    [praiseBtn setImage:[UIImage imageNamed:@"note_praise"] forState:UIControlStateNormal];
    [praiseBtn setImage:[UIImage imageNamed:@"note_praised"] forState:UIControlStateDisabled];
    [praiseBtn setTitleColor:UIColor.up_textSecondaryColor forState:UIControlStateNormal];
    [praiseBtn setTitleColor:FMNavColor forState:UIControlStateDisabled];
    [praiseBtn addTarget:self action:@selector(praiseBtnDidClicked) forControlEvents:UIControlEventTouchUpInside];
    self.praiseBtn = praiseBtn;
}

- (void)setModel:(FMAskCodeModel *)model {
    _model = model;
    
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:model.declareContent.length ? model.declareContent : @""];
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 5.0f;
    [attrStr setAttributes:@{NSParagraphStyleAttributeName : style} range:NSMakeRange(0, attrStr.length)];
    self.descLabel.attributedText = attrStr;
    
    // 浏览人数
    NSString *listenNumStr = nil;
    if (model.listenerNums.integerValue > 0) {
        if (model.listenerNums.integerValue < 10000) {
            listenNumStr = [NSString stringWithFormat:@"%@人",model.listenerNums];
        } else {
            double wan = model.listenerNums.integerValue / 10000.0;
            listenNumStr = [NSString stringWithFormat:@"%.1f万人", wan];
        }
        if (model.contentFileType.integerValue == 1) {
            self.watchListenLabel.text = [NSString stringWithFormat:@"%@看过",listenNumStr];
        } else {
            self.watchListenLabel.text = [NSString stringWithFormat:@"%@听过",listenNumStr];
        }
    } else {
        self.watchListenLabel.text = @"";
    }
    
    // 点赞人数
    [self.praiseBtn setTitle:[self praisedNumBtnTextWithNums:model.satisfiedNums.integerValue] forState:UIControlStateNormal];
    [self.praiseBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5.0f];
    self.praiseBtn.enabled = ![[FMUserDataSyncManager sharedManager] isQuestionLiked:model.askCodeId];
}

- (NSString *)praisedNumBtnTextWithNums:(NSInteger)nums {
    NSString *satisfiedNumString = @"点赞";
    if (nums > 0) {
        if (nums < 10000) {
            satisfiedNumString = [NSString stringWithFormat:@"%zd", nums];
        } else {
            double wan = nums / 10000.0;
            satisfiedNumString = [NSString stringWithFormat:@"%.1f万", wan];
        }
    }
    return satisfiedNumString;
}

- (void)praiseBtnDidClicked {    
    if (![FMHelper checkLoginStatus]) {
        return ;
    }
    [HttpRequestTool questionPraiseWithQuestionId:self.model.askCodeId start:^{
        self.praiseBtn.userInteractionEnabled = NO;
    } failure:^{
        self.praiseBtn.userInteractionEnabled = YES;
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        self.praiseBtn.userInteractionEnabled = YES;
        
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            
            self.praiseBtn.enabled = NO;
            [[FMUserDataSyncManager sharedManager] likeQuestion:self.model.askCodeId];
            self.model.satisfiedNums = [NSString stringWithFormat:@"%ld",self.model.satisfiedNums.integerValue + 1];
            [self.praiseBtn setTitle:[self praisedNumBtnTextWithNums:self.model.satisfiedNums.integerValue] forState:UIControlStateNormal];
            [self.praiseBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageLeft imageTitleSpacing:5.0f];
            
            [[NSNotificationCenter defaultCenter] postNotificationName:kAskCodePriasedNotification object:nil userInfo:@{@"questionId" : self.model.askCodeId}];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

@end
