//
//  FMAskCodeDetailVideoTabCell.m
//  QCYZT
//
//  Created by shumi on 2021/11/23.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMAskCodeDetailVideoTabCell.h"

@interface FMAskCodeDetailVideoTabCell ()
@end


@implementation FMAskCodeDetailVideoTabCell
- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    
    self.userInteractionEnabled = YES;
    WEAKSELF
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(UITapGestureRecognizer  *sender) {
        __weakSelf.playBtn.hidden = YES;
        __weakSelf.timeLB.hidden = YES;
        if (__weakSelf.playBrnClick) {
            __weakSelf.playBrnClick();
        }
    }];
    [self addGestureRecognizer:tap];
 
    UIImageView *liveBgImageV = [[UIImageView alloc] init];
    liveBgImageV.userInteractionEnabled = YES;
    [self.contentView addSubview:liveBgImageV];
    [liveBgImageV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
    self.liveBgImageV = liveBgImageV;

}

- (void)setModel:(FMAskCodeModel *)model {
    _model = model;
    [self.liveBgImageV sd_setImageWithURL:[NSURL URLWithString:model.questionImg] placeholderImage:[UIImage imageNamed:@"home_shortCut_bigPlaceholder"]];
    self.timeLB.text = model.answerVideoTime;
}

@end
