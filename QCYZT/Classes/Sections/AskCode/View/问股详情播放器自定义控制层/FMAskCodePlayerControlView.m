//
//  FMAskCodePlayerControlView.m
//  QCYZT
//
//  Created by shumi on 2022/10/20.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMAskCodePlayerControlView.h"
#import "FMAskCodeModel.h"
#import "FMNoteOrAskStockDetailVideoPayView.h"
@interface FMAskCodePlayerControlView ()<DetailVideoPayViewDelegate>

@property (nonatomic, strong) UIView *topView; //试看UI
@property (nonatomic, strong) UIButton *actionBtn; // 开通权限按钮
@property (nonatomic, strong) UILabel *permissionsLB;// 权限文案
@property (nonatomic, strong) FMNoteOrAskStockDetailVideoPayView *payBgView; //支付操作页面

@end

@implementation FMAskCodePlayerControlView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    [self.portraitControlView.topToolView addSubview:self.topView];
    
    FMNoteOrAskStockDetailVideoPayView *payView = [[FMNoteOrAskStockDetailVideoPayView alloc] init];
    [self addSubview:payView];
    [payView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
    payView.hidden = YES;
    payView.delegate = self;
    self.payBgView = payView;
}


#pragma mark - 监听手势
/// 开始滑动手势事件
- (void)gestureBeganPan:(ZFPlayerGestureControl *)gestureControl panDirection:(ZFPanDirection)direction panLocation:(ZFPanLocation)location {
    if (self.payBgView.hidden == NO) {
        return;
    }
    [super gestureBeganPan:gestureControl panDirection:direction panLocation:location];
}

/// 滑动中手势事件
- (void)gestureChangedPan:(ZFPlayerGestureControl *)gestureControl panDirection:(ZFPanDirection)direction panLocation:(ZFPanLocation)location withVelocity:(CGPoint)velocity {
    if (self.payBgView.hidden == NO) {
        return;
    }
    [super gestureChangedPan:gestureControl panDirection:direction panLocation:location withVelocity:velocity];
}

/// 滑动结束手势事件
- (void)gestureEndedPan:(ZFPlayerGestureControl *)gestureControl panDirection:(ZFPanDirection)direction panLocation:(ZFPanLocation)location {
    if (self.payBgView.hidden == NO) {
        return;
    }
    [super gestureEndedPan:gestureControl panDirection:direction panLocation:location];
}

//双击屏幕事件
- (void)gestureDoubleTapped:(ZFPlayerGestureControl *)gestureControl {
    if (self.payBgView.hidden == NO) {
        return;
    }
    [super gestureDoubleTapped:gestureControl];
}

- (void)setModel:(FMAskCodeModel *)model {
    _model = model;
    self.topView.hidden = YES;
    if (model.questionPerm.type.integerValue != 1) {
        // 免费看
        self.payBgView.hidden = YES;
    } else {
        if (model.tryPlaySecond.integerValue > 0) {
            //试看播放结束,不能试看了
            if (model.playStatus == 2) {
                self.payBgView.hidden = NO;
                self.payBgView.askCodeModel = model;
                [self.player.currentPlayerManager pause];
            } else {
                self.payBgView.hidden = YES;
                self.topView.hidden = NO;
                self.permissionsLB.text =  [NSString stringWithFormat:@"可试看，%@金币看完整视频",model.listenPriceStr];
                [self.actionBtn setTitle:@"立即支付" forState:UIControlStateNormal];
                NSArray *colors =  @[(__bridge  id)ColorWithHex(0xFEDE87).CGColor,(__bridge  id)ColorWithHex(0xF3CB5C).CGColor];
                [self.actionBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.right.top.bottom.equalTo(self.topView);
                    make.size.equalTo(@(CGSizeMake(93, 25)));
                }];
                self.actionBtn.bounds = CGRectMake(0, 0, 93, 25);
                [self.actionBtn drawCAGradientWithcolors:colors];
                [self.actionBtn bringSubviewToFront:self.actionBtn.titleLabel];
            }
        } else {
            self.payBgView.hidden = NO;
            self.payBgView.askCodeModel = model;
            [self.player.currentPlayerManager pause];
        }
    }
}

- (void)actionBtnClick:(UIButton *)sender {
    if ([_delegate respondsToSelector:@selector(detailPayViewPayBtnClick:)]) {
        [_delegate detailPayViewPayBtnClick:sender];
    }
}

#pragma mark - paybgViewDelegate
- (void)noteOrAskCodeVideoDetailPayViewPayBtnClick:(UIButton *)btn {
    if ([_delegate respondsToSelector:@selector(detailPayViewPayBtnClick:)]) {
        [_delegate detailPayViewPayBtnClick:btn];
    }
}

- (void)noteOrAskCodeVideoReWatch:(UIButton *)sender {
    if ([_delegate respondsToSelector:@selector(videoReWatch:)]) {
        [_delegate videoReWatch:sender];
    }
}

- (UIView *)topView {
    if (!_topView) {
        _topView = [[UIView alloc] init];
        _topView.backgroundColor = ColorWithHexAlpha(0x000000, 0.6);
        UI_View_Radius(_topView, 25 / 2.0);
        [self.portraitControlView.topToolView addSubview:_topView];
        [_topView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(@(-10));
            make.top.equalTo(@(10));
            make.height.equalTo(@(25));
        }];
        
        UIButton *actionBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(12.0) backgroundColor:FMWhiteColor target:self action:@selector(actionBtnClick:)];
        [actionBtn setTitleColor:ColorWithHex(0x522212) forState:UIControlStateNormal];
        UI_View_Radius(actionBtn, 25 / 2.0);
        [_topView addSubview:actionBtn];
        [actionBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.top.bottom.equalTo(actionBtn.superview);
        }];
        self.actionBtn = actionBtn;

        UILabel *permissionsLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12.0) textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1];
        permissionsLB.textAlignment = NSTextAlignmentLeft;
        [_topView addSubview:permissionsLB];
        [permissionsLB mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(permissionsLB.superview.mas_left).offset(7.5);
            make.top.bottom.equalTo(permissionsLB.superview);
            make.right.equalTo(actionBtn.mas_left).offset(-7.5);
        }];
        self.permissionsLB = permissionsLB;
    }
    return _topView;
}

@end
