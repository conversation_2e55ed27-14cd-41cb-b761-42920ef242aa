//
//  FMBigCastCommonModel.m
//  QCYZT
//
//  Created by shum<PERSON> on 2022/12/1.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMBigCastCommonModel.h"

@implementation DakaBannersModel

@end

@implementation FMBigCastCommonModel

- (NSString *)userGoodAt {
    if (_userGoodAt.length > 0) {
        NSArray *arr = [_userGoodAt componentsSeparatedByString:@","];
        return [arr componentsJoinedByString:@"、"];
    }
    return _userGoodAt;
}

- (NSString *)userNoteNums {
    if (_userNoteNums.integerValue < 0) {
        return @"0";
    }
    return _userNoteNums;
}

- (NSArray *)isLive {
    if (self.attestationType == 2) {
        NSDictionary *infoDic = [FMUserDefault getUnArchiverDataForKey:kALLDakaLiveInfo];
        if (infoDic) {
            NSArray *livingArr = [NSArray arrayWithArray:infoDic[@"living"]];
            NSMutableArray *roomArr = [NSMutableArray array];
            for (NSInteger i = 0; i < livingArr.count; i ++) {
                NSDictionary  *liveInfo = livingArr[i];
                if (self.userId.integerValue == [liveInfo[@"bignameId"] integerValue]) {
                    if (![roomArr containsObject:liveInfo[@"roomId"]]) {
                        [roomArr addObject:liveInfo[@"roomId"]];
                    }
                }
            }
            return roomArr;
        }
    }
    return @[];
}

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{@"userId" : @[@"userid",@"userId"]};
}

+ (NSDictionary *)modelContainerPropertyGenericClass{
    return @{@"banners" : DakaBannersModel.class};
}

@end
