//
//  BigCastModel.h
//  QCYZT
//
//  Created by <PERSON><PERSON> on 2017/1/3.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface BigCastModel : NSObject

/**
 *  投顾ID
 */
@property (nonatomic, copy) NSString *userid;

/**
 *  证书编号
 */
@property (nonatomic, copy) NSString *cert_code;

/**
 *  向投顾提问接口
 */
@property (nonatomic, copy) NSString *user_question_action;

/**
 *  关注投顾接口
 */
@property (nonatomic, copy) NSString *user_notice_action;

/**
 *  投顾名称
 */
@property (nonatomic, copy) NSString *user_name;

/**
 *  投顾简介
 */
@property (nonatomic, copy) NSString *user_profiles;

/**
 *  投顾头衔
 */
@property (nonatomic, copy) NSString *user_title;

/**
 *  投顾头像
 */
@property (nonatomic, copy) NSString *user_ico;

/**
 *  关注人数
 */
@property (nonatomic,copy) NSString *user_notice_num;

@property (nonatomic,copy) NSString *answer_price;

@property (nonatomic, assign) NSInteger windowType; // 2需要升级VIP 1不需要
@property (nonatomic, assign) VIPReadAuthority needVip;

@property (nonatomic, copy) NSString *answerNum;
@property (nonatomic, copy) NSString *noteNum;
@property (nonatomic,copy) NSString *good_at;
//认证机构  认证类型,1专栏 2投顾 3机构
@property (nonatomic,assign) NSInteger attestationType;

@end
