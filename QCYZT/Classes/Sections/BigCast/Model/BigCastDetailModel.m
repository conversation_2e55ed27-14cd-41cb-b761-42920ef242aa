//
//  BigCastDetailModel.m
//  QCYZT
//
//  Created by th on 17/1/8.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "BigCastDetailModel.h"

@implementation BigCastDetailBannerModel
@end
@implementation BigCastDetailPropagateModel
@end

@implementation BigCastDetailModel

- (BOOL)showFoldBtn {
    return [self.bignameDetail getlinesArrayWithFont:FontWithSize(15) width:UI_SCREEN_WIDTH-30].count > 2;
}

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{@"userNoticerNums" : @[@"user_noticer_nums",@"userNoticerNums",@"user_notice_num"],
             @"userName":@[@"user_name",@"visitUserName",@"userName"],
             @"userIco":@[@"visitUserIco",@"user_ico",@"userIco"],
             @"userid":@[@"visitUserId",@"userid"],
             @"userProfiles":@[@"user_profiles",@"profiles",@"userProfiles"],
             @"certCode" :@[@"cert_code", @"certCode"],
             @"userTitle":@[@"user_title",@"userTitle"],
             @"userGoodAt" : @[@"userGoodAt",@"good_at"],
             @"isLiveNum": @[@"isLiveNum",@"isLive"],
             @"letterPrice":@[@"letterPrice",@"letter_price"],
             @"userWriteNums":@[@"userWriteNums",@"writeNum"]
    };
}

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{@"textPropagandas" : [BigCastDetailPropagateModel class]};
}


- (NSInteger)isLiveNum {
    if (self.attestationType.integerValue == 2 || self.attestationType.integerValue == 1) {
        NSDictionary *infoDic = [FMUserDefault getUnArchiverDataForKey:kALLDakaLiveInfo];
        if (infoDic) {
            NSArray *livingArr = [NSArray arrayWithArray:infoDic[@"living"]];
            for (NSInteger i = 0; i < livingArr.count; i ++) {
                NSDictionary  *liveInfo = livingArr[i];
                if ([self.userid isEqualToString:liveInfo[@"bignameId"]]) {
                    self.livingRoomId = [liveInfo[@"roomId"] integerValue];
                    return 1;
                    break;
                }
            }
            
            NSArray *livedArr = [NSArray arrayWithArray:infoDic[@"lived"]];
            for (NSNumber *num in livedArr) {
                if ([self.userid isEqualToString:num.description]) {
                    return 2;
                }
            }
        }
    }
    return 0;
}

- (NSString *)userGoodAt {
    
    if (_userGoodAt.length > 0) {
        NSArray *arr = [_userGoodAt componentsSeparatedByString:@","];
        return [arr componentsJoinedByString:@"、"];
    }
    return _userGoodAt;
}

-(BOOL)isTouGuVerify{
    return self.attestationType.integerValue == 2;
}

@end
