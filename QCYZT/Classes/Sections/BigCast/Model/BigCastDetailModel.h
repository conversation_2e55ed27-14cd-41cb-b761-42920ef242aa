//
//  BigCastDetailModel.h
//  QCYZT
//
//  Created by th on 17/1/8.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface BigCastDetailBannerModel : NSObject

@property (nonatomic, copy) NSString *action;
@property (nonatomic, copy) NSString *banner;
@property (nonatomic, copy) NSString *bignameId;
@property (nonatomic, copy) NSString *name;

@end

@interface BigCastDetailPropagateModel : NSObject

@property (nonatomic, copy) NSString *clickAction;
@property (nonatomic, copy) NSString *tagName;
@property (nonatomic, copy) NSString *content;

@end

@interface BigCastDetailModel : NSObject

@property (nonatomic, copy) NSString *userIco;
@property (nonatomic, copy) NSString *userName;
@property (nonatomic, copy) NSString *userid;
@property (nonatomic, copy) NSString *certCode;
@property (nonatomic, copy) NSString *userNoticerNums; //粉丝数
@property (nonatomic, copy) NSString *userTitle;
@property (nonatomic, copy) NSString *userProfiles;
@property (nonatomic, copy) NSString *userGoodAt;
@property (nonatomic, copy) NSString *bignameDetail;

@property (nonatomic, assign) NSInteger isLiveNum; // 0表示没有在直播，没有直播室列表；1表示正在直播（肯定有直播室列表)； 2表示没有在直播，有直播室列表
/// 正在直播时的 直播间id
@property (nonatomic,assign) NSInteger livingRoomId;

@property (nonatomic, strong) NSDictionary *communication_list; // 交流圈

@property (nonatomic, assign) NSInteger windowType; // 2需要升级VIP 1不需要
@property (nonatomic, assign) VIPReadAuthority needVip;  


@property (nonatomic, assign) BOOL showFoldBtn; // 是否显示展开折叠按钮，超过2行显示
@property (nonatomic, assign) BOOL isYZK; // 是否已展开，默认为NO
/// 0(投顾没有vip)  1(投顾有vip,且用户不拥有所有分组)  2(投顾有vip,且用户拥有所有分组)
@property (nonatomic,copy) NSString *allowOpenVip;
@property (nonatomic,  copy) NSString *answerPrice; // 问股价格 为0 不显示问股 大于0 显示问股
@property (nonatomic, copy) NSString *letterPrice; // 私信价格 为0 不显示私信按钮 大于0 显示私信
/// 认证机构  认证类型,1专栏 2投顾 3机构
@property (nonatomic, copy) NSString *attestationType;
/// 是否是投顾认证
@property(nonatomic,assign,readonly) BOOL isTouGuVerify;
@property (nonatomic,  copy) NSString *attestationTitle; //认证机构名称
@property (nonatomic,assign) NSInteger userWriteNums; // 创作数目
@property (nonatomic,assign) NSInteger noticeNum; //关注数
/// 累计打赏
@property(nonatomic, copy) NSString *reward;
@property (nonatomic,copy) NSString  *allSatisfiedNum; // 获赞数
@property (nonatomic,copy) NSString *  infoIntroduction; // 服务介绍

@property (nonatomic, assign) NSInteger rank; // 上榜次数

@property (nonatomic, assign, getter=isProfileUnFold) BOOL profileUnFold; // 简介是否展开


@property (nonatomic, strong) NSArray<BigCastDetailBannerModel *> *banners;
@property (nonatomic, strong) NSArray<BigCastDetailPropagateModel *> *textPropagandas;


@end
