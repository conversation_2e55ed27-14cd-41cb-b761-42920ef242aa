//
//  BigCastModel.m
//  QCYZT
//
//  Created by <PERSON><PERSON> on 2017/1/3.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "BigCastModel.h"

@implementation BigCastModel

+ (NSDictionary *)modelCustomPropertyMapper {
    return @{
             @"user_notice_num" : @[@"user_notice_num", @"user_noticer_nums"],
             @"userid":@[@"userid",@"visitUserId"],
             @"user_name":@[@"visitUserName",@"user_name"],
             @"user_ico":@[@"user_ico",@"visitUserIco"]
    };
}
 
- (NSString *)good_at {
    if (_good_at.length > 0) {
        NSArray *arr = [_good_at componentsSeparatedByString:@","];
        return [arr componentsJoinedByString:@"、"];
    }
    return _good_at;
}

@end
