//
//  FMBigcastHomePageLiveViewController.m
//  QCYZT
//
//  Created by zeng on 2021/11/28.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMBigcastHomePageLiveViewController.h"
#import "FMDakaLiveTabCell.h"
#import "FMDakaLiveDetailVC.h"
#import "HttpRequestTool+Live.h"
#import "FMLiveModel.h"

@interface FMBigcastHomePageLiveViewController ()

@property (nonatomic, strong) NSMutableArray *dataArr;
@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;

@end

@implementation FMBigcastHomePageLiveViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 10;
    
    [self configTableView];
    [self requestData];
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMDakaLiveTabCell *cell = [tableView reuseCellClass:[FMDakaLiveTabCell class]];
    cell.model = self.dataArr[indexPath.row];
    cell.isLastCell = (indexPath.row == self.dataArr.count - 1);
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 120;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
        [self jumpToLiveDetail:indexPath.row locationView:self.tableView];
    }];
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - Request
- (void)requestData {
    [HttpRequestTool getDakaLiveListWithPageNo:self.page pageSize:self.pageSize bigNameId:self.authorId start:^{
    } failure:^{
        [self endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        [self endRefreshForSuccess];
        [self dealData:dic];
    }];
    
}

- (void)dealData:(NSDictionary *)dic {
    if ([dic[@"status"] isEqualToString:@"1"]) {
        if ([dic[@"data"] isKindOfClass:[NSArray class]]) {
            [self endRefreshForSuccess];

            if (self.page == 1) {
                [self.dataArr removeAllObjects];
                [self.tableView.mj_footer resetNoMoreData];
            }
            NSArray *arr = [NSArray modelArrayWithClass:[FMLiveModel class] json:dic[@"data"]];
            if (arr.count < self.pageSize) {
                [self.tableView.mj_footer endRefreshingWithNoMoreData];
            }
            [self.dataArr addObjectsFromArray:arr];
            if (!self.dataArr.count) {
                [self.tableView showNoDataViewWithImage:UPTImgInMarket2Module(@"个股/common_nodata") string:@"暂无直播" attributes:nil offsetY:80];
                self.tableView.mj_footer.hidden = YES;
            } else {
                [self.tableView dismissNoDataView];
                self.tableView.mj_footer.hidden = NO;
            }
            [self.tableView reloadData];
        }
    } else {
        [self endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
    }
}

- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.currentPage = self.page;
}

- (void)jumpToLiveDetail:(NSInteger)index locationView:(id)view {
    FMLiveModel *model = self.dataArr[index];
    NSString *roomId = [NSString stringWithFormat:@"%ld",model.liveId];
    FMDakaLiveDetailVC *vc = [[FMDakaLiveDetailVC alloc] initWithRoomId:roomId];
    WEAKSELF;
    vc.reservationBlock = ^(BOOL isReservation) {
        if ([view isKindOfClass:[UITableView class]]) {
            model.isReservation = [[NSNumber numberWithBool:isReservation] integerValue];
            FMDakaLiveTabCell *cell = [__weakSelf.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:index inSection:0]];
            model.reservationPersonNum ++;
            cell.model = model;
        }
    };
    vc.liveStatusBlock = ^(NSString *status) {
        if ([view isKindOfClass:[UITableView class]]) {
            FMDakaLiveTabCell *cell = [__weakSelf.tableView cellForRowAtIndexPath:[NSIndexPath indexPathForRow:index inSection:0]];
            model.status = status;
            cell.model = model;
        }
    };
  
    [self.navigationController pushViewController:vc animated:YES];
}


#pragma mark - Private
- (void)configTableView {
    self.tableView.backgroundColor = self.view.backgroundColor = UIColor.up_contentBgColor;
    [self.tableView registerCellClass:[FMDakaLiveTabCell class]];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    
    MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(footerAction)];
    footer.stateLabel.textColor = ColorWithHex(0xbfbfbf);
    footer.hidden = YES;
    self.tableView.mj_footer = footer;
}

- (void)footerAction {
    self.page++;
    [self requestData];
}

#pragma mark - Setter/Getter
- (NSMutableArray *)dataArr {
    if (_dataArr == nil) {
        _dataArr = [[NSMutableArray alloc] init];
    }
    return _dataArr;
}

@end
