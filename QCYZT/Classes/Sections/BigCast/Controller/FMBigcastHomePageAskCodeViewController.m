//
//  FMBigcastHomePageAskCodeViewController.m
//  QCYZT
//
//  Created by zeng on 2021/11/28.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMBigcastHomePageAskCodeViewController.h"
#import "FMAskCodeListCell.h"
#import "FMAskCodeVideoListCell.h"
#import "FMAskCodeModel.h"
#import "FMAskCodeDetailViewController.h"
@interface FMBigcastHomePageAskCodeViewController ()

@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;
@property (nonatomic, strong) NSMutableArray *dataArr;

@property (nonatomic, assign) BOOL needsRefresh;

@end

@implementation FMBigcastHomePageAskCodeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 10;
    
    [self configTableView];
    [self requestData];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(questionPaySuccess:) name:kQuestionPaySuccess object:nil];
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(praisedNotification:) name:kAskCodePriasedNotification object:nil];
}

- (void)dealloc {
    FMLog(@"%s", __func__);
    
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (self.needsRefresh) {
        self.needsRefresh = NO;
        self.page = 1;
        self.currentPage = self.page;
        [self requestData];
    }
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMAskCodeModel *model = self.dataArr[indexPath.row];
    if (model.contentFileType.integerValue == 1 && (model.questionAnswerFlag.integerValue == 1 && model.checkStatus.integerValue == 1)) {
        //视频问股
        FMAskCodeVideoListCell *cell = [tableView reuseCellClass:[FMAskCodeVideoListCell class]];
        cell.askModel = model;
        cell.isLastCell = (indexPath.row == self.dataArr.count - 1);
        cell.infoView.iconImgV.userInteractionEnabled = NO;
        cell.infoView.containerView.userInteractionEnabled = NO;
        return cell;
    } else {
        //语音问股
        FMAskCodeListCell *cell = [tableView reuseCellClass:[FMAskCodeListCell class]];
        cell.askCodeListType = AskCodeListTypeDakaAnswered;
        cell.askModel = self.dataArr[indexPath.row];
        cell.isLastCell = (indexPath.row == self.dataArr.count - 1);
        cell.infoView.iconImgV.userInteractionEnabled = NO;
        cell.infoView.containerView.userInteractionEnabled = NO;
        return cell;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    FMAskCodeModel *model = self.dataArr[indexPath.row];
    if (model.contentFileType.integerValue == 1 && (model.questionAnswerFlag.integerValue == 1 && model.checkStatus.integerValue == 1)) {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMAskCodeVideoListCell class]) configuration:^(FMAskCodeVideoListCell *cell) {
            cell.askModel = self.dataArr[indexPath.row];
        }];
    } else {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMAskCodeListCell class]) configuration:^(FMAskCodeListCell *cell) {
            cell.askCodeListType = AskCodeListTypeDakaAnswered;
            cell.askModel = self.dataArr[indexPath.row];
        }];
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    FMAskCodeModel *model = self.dataArr[indexPath.row];
    FMAskCodeDetailViewController *vc = [[FMAskCodeDetailViewController alloc] init];
    vc.questionId = model.askCodeId;
    [self.navigationController pushViewController:vc animated:YES];
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - HTTP
- (void)requestData {
    
    [HttpRequestTool getDakaAskCodeListWithPage:self.page pageSize:self.pageSize dakaId:self.answerUserId start:^{
    } failure:^{
        [self endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        [self dealData:dic];
    }];
}

- (void)dealData:(NSDictionary *)dic {
    if ([dic[@"status"] isEqualToString:@"1"]) {
        [self endRefreshForSuccess];

        if (self.page == 1) {
            [self.tableView.mj_footer resetNoMoreData];
            [self.dataArr removeAllObjects];
        }
        
        NSArray *dataArr = [NSArray modelArrayWithClass:[FMAskCodeModel class] json:dic[@"data"]];
        if (dataArr.count < self.pageSize) {
            [self.tableView.mj_footer endRefreshingWithNoMoreData];
        }
        
        [self removeRepeatDataWithArray:dataArr];
        if (!self.dataArr.count) {
            [self.tableView showNoDataViewWithImage:UPTImgInMarket2Module(@"个股/common_nodata") string:@"暂无问股" attributes:nil offsetY:80];
            self.tableView.mj_footer.hidden = YES;
        } else {
            [self.tableView dismissNoDataView];
            self.tableView.mj_footer.hidden = NO;
        }
        
        [self.tableView reloadData];
    } else {
        [self endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
    }
}

#pragma mark - Notification
- (void)questionPaySuccess:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    
    for (int i = 0; i<self.dataArr.count; i++) {
        FMAskCodeModel *model = self.dataArr[i];
        if ([model.askCodeId isEqualToString:questionId]) {
            model.questionPerm.type = @"3";
            [self.tableView reloadData];

            break;
        }
    }
}

- (void)praisedNotification:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *questionId = userInfo[@"questionId"];
    
    for (int i = 0; i <self.dataArr.count; i++) {
        FMAskCodeModel *model = self.dataArr[i];
        if ([model.askCodeId isEqualToString:questionId]) {
            [[FMUserDataSyncManager sharedManager] likeQuestion:questionId];
            model.satisfiedNums = [NSString stringWithFormat:@"%ld",model.satisfiedNums.integerValue + 1];
            [self.tableView reloadData];
            
            break;
        }
    }
}

- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

#pragma mark - Private
- (void)footerAction {
    self.page++;
    [self requestData];
}

- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.currentPage = self.page;
}

- (void)removeRepeatDataWithArray:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (FMAskCodeModel *model in array) {
        // 投顾主页不显示直播状态
        model.bignameDto.isLive = @[];
        [dic setObject:model forKey:model.askCodeId];
    }
    
    for (FMAskCodeModel *model in self.dataArr.reverseObjectEnumerator) {
        if ([dic valueForKey:model.askCodeId]) {
            [self.dataArr removeObject:model];
        }
    }
    
    [self.dataArr addObjectsFromArray:array];
}

- (void)configTableView {
    self.tableView.backgroundColor = self.view.backgroundColor = UIColor.up_contentBgColor;
    [self.tableView registerCellClass:[FMAskCodeListCell class]];
    [self.tableView registerCellClass:[FMAskCodeVideoListCell class]];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;

    MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(footerAction)];
    footer.stateLabel.textColor = ColorWithHex(0xbfbfbf);
    footer.hidden = YES;
    self.tableView.mj_footer = footer;
}

#pragma mark - Getter/Setter
- (NSMutableArray *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray array];
    }
    return _dataArr;
}

@end
