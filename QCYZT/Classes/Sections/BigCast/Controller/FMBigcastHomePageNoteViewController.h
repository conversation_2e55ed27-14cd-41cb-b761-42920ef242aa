//
//  FMBigcastHomePageNoteViewController.h
//  QCYZT
//
//  Created by zeng on 2021/11/28.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMInnerTableViewController.h"

NS_ASSUME_NONNULL_BEGIN


@interface FMBigcastHomePageNoteViewController : FMInnerTableViewController
/// 初始化方法
/// @param userId 用户id
-(instancetype)initWithUserId:(NSString *)userId;
/// 初始化方法
/// @param userId 用户id
/// @param sign 笔记分栏的标签字段
-(instancetype)initWithUserId:(NSString *)userId sign:(NSString *)sign;
/// 初始化方法
/// @param userId 用户id
/// @param isSearch 是否为搜索模式
-(instancetype)initWithUserId:(NSString *)userId searchMoudule:(BOOL)isSearch;
/// 是否限制长文（专栏）
@property (nonatomic,assign) BOOL onlyNote;
-(instancetype)init NS_UNAVAILABLE;
+(instancetype)new NS_UNAVAILABLE;
-(instancetype)initWithCoder:(NSCoder *)coder NS_UNAVAILABLE;
-(instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil NS_UNAVAILABLE;


@end

NS_ASSUME_NONNULL_END
