//
//  FMAllDakaListVC.m
//  QCYZT
//
//  Created by shumi on 2022/6/29.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMAllDakaListVC.h"
#import "FMAllDakaLeftTabCell.h"
#import "FMAllDakaRightTabCell.h"
#import "HttpRequestTool+Home.h"
#import "AllBigCastListModel.h"
#import "FMBigCastOperationRankViewController.h"

@interface FMAllDakaListVC ()<UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) UITableView *leftTableView;
@property (nonatomic, strong) UITableView *rightTableView;
@property (nonatomic, strong) NSMutableArray *dataArr;
@property (nonatomic, strong) NSArray *rightArr;

@property (nonatomic, assign) BOOL needsRefresh;

@end

@implementation FMAllDakaListVC

- (void)viewDidLoad {
    
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.up_contentBgColor;
    self.navigationItem.title = @"全部投顾";
    [self setupUI];
    [self requestData];
    
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}


#pragma mark - NSNotification
- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self configNavWhiteColorWithCloseSEL:@selector(backArrowClicked)];

    if (self.needsRefresh) {
        self.needsRefresh = NO;
        [self requestData];
    }
}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)setupUI {
    NSDictionary *initDic = [[NSUserDefaults standardUserDefaults] objectForKey:AppInit_Key];
    BOOL divineStockTag = [[NSString stringWithFormat:@"%@",initDic[@"divineStockTag"]] boolValue];
    UIImageView *imageView = [[UIImageView alloc] init];
    imageView.image = [UIImage imageNamed:@"daka_operation_banner"];
    [self.view addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(0);
        make.left.equalTo(0);
        make.right.equalTo(0);
        if (divineStockTag) {
            make.height.equalTo(imageView.mas_width).multipliedBy(97.5/375);
        } else {
            make.height.equalTo(0);
        }
    }];
    imageView.hidden = !divineStockTag;
    imageView.userInteractionEnabled = YES;
    WEAKSELF
    [imageView bk_whenTapped:^{
        FMBigCastOperationRankViewController *vc = [[FMBigCastOperationRankViewController alloc] init];
        [__weakSelf.navigationController pushViewController:vc animated:YES];
    }];

    
    [self.view addSubview:self.leftTableView];
    [self.leftTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.equalTo(0);
        make.top.equalTo(imageView.mas_bottom);
        make.width.equalTo(@(UI_SCREEN_WIDTH * (16 / 65.0)));
    }];
    
    [self.view addSubview:self.rightTableView];
    [self.rightTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.bottom.equalTo(0);
        make.top.equalTo(imageView.mas_bottom);
        make.left.equalTo(self.leftTableView.mas_right);
    }];
}

#pragma mark - request
- (void)requestData {
    [HttpRequestTool getAllDakaRequestStart:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        [SVProgressHUD dismiss];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            if ([dic[@"data"] isKindOfClass:[NSArray class]]) {
                NSArray *arr = [NSArray modelArrayWithClass:[AllBigCastListModel class] json:dic[@"data"]];
                if (arr.count > 0) {
                    AllBigCastListModel *model = [arr firstObject];
                    model.isSelected = YES;
                    self.rightArr = [NSArray arrayWithArray:model.list];
                }
                [self.dataArr removeAllObjects];
                [self.dataArr addObjectsFromArray:arr];
                [self.leftTableView reloadData];
                [self.rightTableView reloadData];
            }
        }
    }];
}

#pragma mark - tableViewDelegate
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (self.dataArr.count > 0) {
        if ([tableView isEqual:self.leftTableView]) {
            return self.dataArr.count;
        } else {
            return self.rightArr.count;
        }
    }
    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.dataArr.count == 0) {
        return [UITableViewCell new];
    }
    if ([tableView isEqual:self.leftTableView]) {
        FMAllDakaLeftTabCell *cell = [tableView reuseCellClass:[FMAllDakaLeftTabCell class]];
        AllBigCastListModel *model = self.dataArr[indexPath.row];
        model.isLast = (indexPath.row == self.dataArr.count - 1);
        cell.model = model;
        return cell;
    } else {
        FMAllDakaRightTabCell *cell = [tableView reuseCellClass:[FMAllDakaRightTabCell class]];
        BigCastDetailModel *model = self.rightArr[indexPath.row];
        cell.model = model;
        return cell;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if ([tableView isEqual:self.leftTableView]) {
        return 60;
    } else {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMAllDakaRightTabCell class]) configuration:^(FMAllDakaRightTabCell *cell) {
            BigCastDetailModel *model = self.rightArr[indexPath.row];
            cell.model = model;
        }];
    }
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    if ([tableView isEqual:self.leftTableView]) {
        for (NSInteger i = 0; i < self.dataArr.count; i ++) {
            AllBigCastListModel *model = self.dataArr[i];
            if (i == indexPath.row) {
                model.isSelected = YES;
                self.rightArr = [NSArray arrayWithArray:model.list];
            } else {
                model.isSelected = NO;
            }
        }
        [self.leftTableView reloadData];
        [self.rightTableView reloadData];
    } else {
        BigCastDetailModel *model = self.rightArr[indexPath.row];
        [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://bigname?id=%@", model.userid]];
    }
}

#pragma mark- lazy
- (UITableView *)leftTableView {
    if (!_leftTableView) {
        _leftTableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self];
        _leftTableView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        [_leftTableView registerCellClass:[FMAllDakaLeftTabCell class]];
        _leftTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _leftTableView.tableFooterView = [UIView new];
    }
    return _leftTableView;
}

- (UITableView *)rightTableView {
    if (!_rightTableView) {
        _rightTableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self];
        [_rightTableView registerCellClass:[FMAllDakaRightTabCell class]];
        _rightTableView.separatorInset = UIEdgeInsetsZero;
        _rightTableView.separatorColor = UIColor.fm_sepline_color;
        _rightTableView.tableFooterView = [UIView new];
        _rightTableView.backgroundColor = UIColor.up_contentBgColor;
    }
    return _rightTableView;
}

- (NSMutableArray *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray array];
    }
    return _dataArr;
}

@end
