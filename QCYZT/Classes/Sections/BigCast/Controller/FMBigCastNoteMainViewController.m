//
//  FMBigCastNoteMainViewController.m
//  QCYZT
//
//  Created by shumi on 2023/1/10.
//  Copyright © 2023 LZKJ. All rights reserved.
//

#import "FMBigCastNoteMainViewController.h"
#import "FMDetailBottomView.h"

@interface FMBigCastNoteMainViewController ()<SGPageContentCollectionViewDelegate>
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) ZLTagView *tagView;
@property (nonatomic, strong) SGPageContentCollectionView *pageContentCollectionView;

@end

@implementation FMBigCastNoteMainViewController

- (void)viewDidLoad {
    [super viewDidLoad];
        
    if (self.attestationType == 2) { // 投顾认证UI
        [self setupTabUI];
    } else { // 非投顾认证UI
        [self setupNormalUI];
    }
}

/// 设置分栏UI
- (void)setupTabUI {
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    [self.view addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(0);
        make.height.equalTo(45);
    }];

    self.tagView.titleArray = @[@"全部", @"策略", @"教学", @"跟踪", @"短视频"];
    [self.view setNeedsLayout];
    [self.view layoutIfNeeded];
    self.tagView.frame = CGRectMake(0, 0, self.tagView.singleLineViewWidth, 37);
    self.scrollView.contentSize = CGSizeMake(self.tagView.singleLineViewWidth, 37);
        
    CGRect pageContentFrame;
    if ([self.userId isEqualToString:[FMUserDefault getUserId]]) {
        pageContentFrame = CGRectMake(0, 45, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT-(UI_SAFEAREA_TOP_HEIGHT + 45 + 45 + UI_SAFEAREA_BOTTOM_HEIGHT));
    } else {
        pageContentFrame = CGRectMake(0, 45, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT-(UI_SAFEAREA_TOP_HEIGHT + 45 + 45 + DetailBottomViewHeight));
    }
    self.pageContentCollectionView = [[SGPageContentCollectionView alloc] initWithFrame:pageContentFrame parentVC:self childVCs:[self addChildVC]];
    self.pageContentCollectionView.delegatePageContentCollectionView = self;
    [self.view addSubview:self.pageContentCollectionView];
    
    [self.pageContentCollectionView setPageContentCollectionViewCurrentIndex:self.chooseIndex];
}

/// 设置非分栏UI
- (void)setupNormalUI {
    self.pageContentCollectionView = [[SGPageContentCollectionView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT-(UI_SAFEAREA_TOP_HEIGHT + 45 + UI_SAFEAREA_BOTTOM_HEIGHT)) parentVC:self childVCs:[self addChildVC]];
    self.pageContentCollectionView.delegatePageContentCollectionView = self;
    [self.view addSubview:self.pageContentCollectionView];
}

- (NSArray *)addChildVC {
    // 全部
    FMBigcastHomePageNoteViewController *noteVC1 = [[FMBigcastHomePageNoteViewController alloc] initWithUserId:self.userId sign:@"全部"];
    self.showVC = noteVC1;
    [self addChildViewController:noteVC1];
    
    if (self.attestationType == 2) {
        // 策略
        FMBigcastHomePageNoteViewController *noteVC2 = [[FMBigcastHomePageNoteViewController alloc] initWithUserId:self.userId sign:@"策略"];
        [self addChildViewController:noteVC2];
        
        // 教学
        FMBigcastHomePageNoteViewController *noteVC3 = [[FMBigcastHomePageNoteViewController alloc] initWithUserId:self.userId sign:@"教学"];
        [self addChildViewController:noteVC3];
        
        // 跟踪
        FMBigcastHomePageNoteViewController *noteVC4 = [[FMBigcastHomePageNoteViewController alloc] initWithUserId:self.userId sign:@"跟踪"];
        [self addChildViewController:noteVC4];
        
        // 短视频
        FMBigcastHomePageNoteViewController *noteVC5 = [[FMBigcastHomePageNoteViewController alloc] initWithUserId:self.userId sign:@"短视频"];
        [self addChildViewController:noteVC5];
    }
    
    return self.childViewControllers;
}

- (void)pageContentCollectionView:(SGPageContentCollectionView *)pageContentCollectionView progress:(CGFloat)progress originalIndex:(NSInteger)originalIndex targetIndex:(NSInteger)targetIndex {
    [self.tagView.selections removeAllObjects];
    [self.tagView.selections addObject:@(targetIndex)];
    [self.tagView setNeedsLayout];
    [self.tagView layoutIfNeeded];
    FMBigcastHomePageNoteViewController *vc = (FMBigcastHomePageNoteViewController *)self.childViewControllers[targetIndex];
    self.showVC = vc;
}


- (UIScrollView *)scrollView {
    if (!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.showsHorizontalScrollIndicator = NO;
    }
    return _scrollView;
}

- (ZLTagView *)tagView {
    if (!_tagView) {
        _tagView = [[ZLTagView alloc] initWithFrame:CGRectMake(0, 0, CGFLOAT_MAX, 0)];
        [self.scrollView addSubview:_tagView];
        _tagView.backgroundColor = UIColor.up_contentBgColor;
        _tagView.tagLabelFont = FontWithSize(14);
        _tagView.leftPadding = _tagView.rightPadding = 20.0f;
        _tagView.middlePadding = 15.0f;
        _tagView.topPadding = 10.0f;
        _tagView.tagLabelWidthPadding = 40.0f;
        _tagView.labelHeight = 27.0f;
        _tagView.tagLabelCornerRadius = 13.5f;
        _tagView.allowsSelection = YES;
        _tagView.allowsMultipleSelection = NO;
        _tagView.singSelectionCanNotInvertSelect = YES;
        _tagView.tagLabelTextColor = UIColor.up_textPrimaryColor;
        _tagView.selectedTextColor = FMWhiteColor;
        _tagView.tagLabelBgColor = UIColor.fm_note_tag_bgColor;
        _tagView.selectedBackgroundColor = FMNavColor;
        _tagView.singSelectionCanNotInvertSelect = YES;
        [_tagView.selections addObject:@(0)]; // 默认选中
        _tagView.numberofLines = 1;
        WEAKSELF
        _tagView.didSelectedBlock = ^(NSArray *selections, NSInteger currentSelectedIndex) {
            [__weakSelf.pageContentCollectionView setPageContentCollectionViewCurrentIndex:currentSelectedIndex];
            
            [__weakSelf autoScrollToIndex:currentSelectedIndex];
        };
    }
    
    return _tagView;
}

- (void)autoScrollToIndex:(NSInteger)currentSelectedIndex {
    CGFloat scrollViewContentWidth = _scrollView.contentSize.width;
    if (scrollViewContentWidth <= _scrollView.width) {
        return;
    }
    
    ZLTagView *currentSelectedTagLabel = _tagView.subviews[currentSelectedIndex];
    CGFloat currentSelectedTagLabelWidth = currentSelectedTagLabel.width;
    CGFloat currentSelectedTagLabelX = currentSelectedTagLabel.x;
    
    if (currentSelectedTagLabelX < (_scrollView.width - currentSelectedTagLabelWidth) * 0.5) { // 滚动到最前面
        [_scrollView setContentOffset:CGPointMake(0, 0) animated:YES];
    } else if (scrollViewContentWidth < currentSelectedTagLabelX + (_scrollView.width + currentSelectedTagLabelWidth) * 0.5) { // 滚动到最后面
        [_scrollView setContentOffset:CGPointMake(scrollViewContentWidth - _scrollView.width, 0) animated:YES];
    } else { // 滚动到中间
        [_scrollView setContentOffset:CGPointMake(currentSelectedTagLabelX + (currentSelectedTagLabelWidth - _scrollView.width) * 0.5, 0) animated:YES];
    }
}
@end
