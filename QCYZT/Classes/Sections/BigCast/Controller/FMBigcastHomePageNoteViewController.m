//
//  FMBigcastHomePageNoteViewController.m
//  QCYZT
//
//  Created by zeng on 2021/11/28.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMBigcastHomePageNoteViewController.h"
#import "FMNoteCell.h"
#import "FMAllNoteListModel.h"
#import "FMNoteModel.h"
#import "FMNoteDetailViewController.h"
#import "NSObject+FBKVOController.h"
#import "FMPublishAlertViewController.h"
#import "HttpRequestTool+Daka.h"

@interface FMBigcastHomePageNoteViewController ()

@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;
@property (nonatomic, strong) NSMutableArray<FMAllNoteListModel *> *dataArr;
@property (nonatomic, assign) BOOL needsRefresh;
/// 是否是当前用户主页
@property(nonatomic,assign) BOOL isSelfPage;
/// 笔记分栏标签
@property (nonatomic,copy) NSString *sign;
/// 主页用户id
@property (nonatomic,copy) NSString *userId;

@end

@implementation FMBigcastHomePageNoteViewController

-(instancetype)initWithUserId:(NSString *)userId sign:(NSString *)sign searchMoudule:(BOOL)isSearch {
    if (self == [super init]) {
        self.userId = userId;
        self.isSelfPage = [[FMUserDefault getUserId] isEqualToString:userId];;
        self.dataArr = [[NSMutableArray alloc] init];
        self.page = 1;
        self.currentPage = self.page;
        self.pageSize = 10;
        self.sign = sign;
    }
    return self;
};

- (instancetype)initWithUserId:(NSString *)userId sign:(NSString *)sign {
    return  [self initWithUserId:userId sign:sign searchMoudule:NO];
}

- (instancetype)initWithUserId:(NSString *)userId {
    return  [self initWithUserId:userId sign:@"" searchMoudule:NO];
}

- (instancetype)initWithUserId:(NSString *)userId searchMoudule:(BOOL)isSearch {
    return [self initWithUserId:userId sign:@"" searchMoudule:isSearch];;
}

- (void)viewDidLoad {
    [super viewDidLoad];

    [self configTableView];
    
    [self requestData];
    
    [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(praisedNotification:) name:kNotePriasedNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appointmentNotification:) name:kLiveAppointmentStatusChangeNotification object:nil];
    if (_isSelfPage) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshNow) name:kMyCreationCastToTopNoteNotification object:nil];
    }
}

-(void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (self.needsRefresh) {
        self.needsRefresh = NO;
        self.page = 1;
        [self requestData];
    }
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMAllNoteListModel *model = self.dataArr[indexPath.row];
    model.noteApiDto.indexPath = indexPath;
    FMNoteCell *cell = [tableView reuseCellClass:[FMNoteCell class]];
    cell.isFromPersonalHomepage = YES;
    cell.model = model.noteApiDto;
    cell.isLastCell = (indexPath.row == self.dataArr.count - 1);
    WEAKSELF
    cell.deleteBlock = ^{
        [__weakSelf.dataArr removeObjectAtIndex:indexPath.row];
        if (__weakSelf.dataArr == 0) {
            [tableView.mj_header beginRefreshing];
        }
        [tableView reloadData];
    };
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMAllNoteListModel *model = self.dataArr[indexPath.row];
    model.noteApiDto.indexPath = indexPath;
    NSArray *commentIds = [model.noteApiDto.topComments valueForKeyPath:@"commentId"];
    NSString *commentCache = [commentIds componentsJoinedByString:@"-"];
    return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMNoteCell class]) cacheByKey:[NSString stringWithFormat:@"%@-%@-%ld-%@",model.noteApiDto.topType,model.noteApiDto.noteId,model.noteApiDto.updateTime,commentCache] configuration:^(FMNoteCell *cell) {
        cell.isFromPersonalHomepage = YES;
        cell.model = model.noteApiDto;
    }];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    FMAllNoteListModel *model = self.dataArr[indexPath.row];
    if (model.type == 1) {
        //进直播详情
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://dakalive?roomid=%@&status=%@",model.liveroomApiDto.liveId,model.liveroomApiDto.status]];
        }];
    } else {
        FMNoteDetailViewController *vc = [[FMNoteDetailViewController alloc] init];
        vc.noteId = model.noteApiDto.noteId;
        vc.deleteBlock = ^{
            [self.dataArr removeObjectAtIndex:indexPath.row];
            [self.tableView reloadData];
        };
        [self.navigationController pushViewController:vc animated:YES];
    }
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}


#pragma mark - Request
- (void)requestData {
    [HttpRequestTool getDakaNoteListWithDakaId:self.userId onlyNote:self.onlyNote sign:self.sign Page:self.page pageSize:self.pageSize start:^{
    } failure:^{
        [self endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        [self dealData:dic];
    }];
}

- (void)dealData:(NSDictionary *)dic {
    if ([dic[@"status"] isEqualToString:@"1"]) {
        
        [self endRefreshForSuccess];
        
        // 根据total总数 确定分页的页码
        NSInteger pageNum = (([dic[@"total"] integerValue] + self.pageSize - 1) / self.pageSize);
        
        if (self.page == 1) {
            [self.tableView.mj_footer resetNoMoreData];
            [self.dataArr removeAllObjects];
        }
        NSArray *dataArr = [NSArray modelArrayWithClass:[FMNoteModel class] json:dic[@"data"]];
        NSMutableArray *mutableArr = [NSMutableArray array];
        for (NSInteger i = 0; i < dataArr.count; i ++) {
            FMAllNoteListModel *model = [[FMAllNoteListModel alloc] init];
            model.noteApiDto = dataArr[i];
            model.type = 0;
            [mutableArr addObject:model];
        }
        
        // 移除重复数据
        [self removeRepeatDataWithArray:mutableArr];
        
        if (self.page == pageNum) {
            [self.tableView.mj_footer endRefreshingWithNoMoreData];
        }
        
        // 占位显示逻辑
        if (!self.dataArr.count) {
            if ([[FMUserDefault getUserId] isEqual:self.userId]) {
                NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:@"前去发言，和股友互动吧" attributes:@{NSFontAttributeName :FontWithSize(13),NSForegroundColorAttributeName:UIColor.up_textSecondary1Color}];
                [attr addAttributes:@{NSFontAttributeName :FontWithSize(13),NSForegroundColorAttributeName:FMNavColor} range:NSMakeRange(0, 4)];
                [self.tableView showNoDataViewWithImage:ImageWithName(@"daka_nodata2") attributedString:attr offsetY:80 actionBlock:^{
                    [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
                        FMPublishAlertViewController *vc = [[FMPublishAlertViewController alloc] init];
                        vc.isHomePage = YES;
                        vc.modalPresentationStyle = UIModalPresentationFullScreen | UIModalPresentationOverCurrentContext;
                        [[FMAppDelegate shareApp].main presentViewController:vc animated:NO completion:nil];
                    }];
                }];
            } else {
                [self.tableView showNoDataViewWithImage:ImageWithName(@"daka_nodata1") string:@"TA还没有发布内容~" attributes:nil offsetY:80];
            }
            self.tableView.mj_footer.hidden = YES;
        } else {
            [self.tableView dismissNoDataView];
            self.tableView.mj_footer.hidden = NO;
        }
        [self.tableView reloadData];
    } else {
        [self endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
    }
}

- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    self.currentPage = self.page;
}

#pragma mark - Notification
-(void)refreshNow{
    self.page = 1;
    [self requestData];
}

- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

- (void)praisedNotification:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *noteId = userInfo[@"noteId"];
    
    for (int i = 0; i <self.dataArr.count; i++) {
        FMAllNoteListModel *model = self.dataArr[i];
        if (model.noteApiDto.noteId.integerValue == noteId.integerValue) {
            [[FMUserDataSyncManager sharedManager] likeNote:model.noteApiDto.noteId];
            model.noteApiDto.satisfiedNums = [NSString stringWithFormat:@"%ld",model.noteApiDto.satisfiedNums.integerValue + 1];
            [self.tableView reloadData];
            break;
        }
    }
}

- (void)appointmentNotification:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    NSString *roomId = userInfo[@"roomId"];
    
    for (NSInteger i = 0; i < self.dataArr.count; i++) {
        FMAllNoteListModel *model = self.dataArr[i];
        if (model.liveroomApiDto.liveId.integerValue == [roomId integerValue]) {
            model.liveroomApiDto.isReservation = YES;
            model.liveroomApiDto.onlineNum ++;
            [self.tableView reloadData];
            break;;
        }
    }
}

#pragma mark - Private
- (void)configTableView {
    self.view.backgroundColor = self.tableView.backgroundColor = UIColor.up_contentBgColor;
    [self.tableView registerCellClass:[FMNoteCell class]];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(footerAction)];
    footer.stateLabel.textColor = ColorWithHex(0xbfbfbf);
    footer.hidden = YES;
    self.tableView.mj_footer = footer;
}

- (void)footerAction {
    self.page++;
    [self requestData];
}

- (void)headerAction {
    self.page = 1;
    self.pageSize = 20;
    [self requestData];
}

- (void)removeRepeatDataWithArray:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (FMAllNoteListModel *model in array) {
        // 投顾主页不显示直播状态
        model.noteApiDto.bignameDto.isLive = @[];
        [dic setObject:model.noteApiDto forKey:model.noteApiDto.noteId];
        model.noteApiDto.rtShowType = [self judgeRTShowTypeWithModel:model];
    }
    
    for (FMAllNoteListModel *model in self.dataArr.reverseObjectEnumerator) {
        if ([dic valueForKey:model.noteApiDto.noteId]) {
            [self.dataArr removeObject:model];
        }
    }
    
    [self.dataArr addObjectsFromArray:array];
}

- (NoteListCellRightTopShowType)judgeRTShowTypeWithModel:(FMAllNoteListModel *)model {
    if (model.noteApiDto.deleteFlag.integerValue < 0) { // 已删除
        if ([[FMUserDefault getUserId] integerValue] == model.noteApiDto.bignameDto.userId.integerValue) {
            if (model.noteApiDto.deleteFlag.integerValue == -1) {
                return NoteListCellRightTopShowTypeMoreBtn;
            } else {
                return NoteListCellRightTopShowTypeNone;
            }
        } else {
            return NoteListCellRightTopShowTypeNone;
        }
    } else {
        return NoteListCellRightTopShowTypeMoreBtn;
    }
}


@end
