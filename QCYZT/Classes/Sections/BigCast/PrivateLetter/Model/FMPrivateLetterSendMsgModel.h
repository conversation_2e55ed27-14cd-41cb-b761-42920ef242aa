//
//  FMPrivateLetterSendMsgModel.h
//  QCYZT
//
//  Created by zeng on 2022/7/1.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface FMPrivateLetterSendMsgModel : NSObject

@property (nonatomic , copy) NSString              * content;
@property (nonatomic , assign) long long              createTime;
@property (nonatomic , assign) NSInteger              currentSentence;
@property (nonatomic , assign) BOOL               isVip;
@property (nonatomic , copy) NSString              * orderId;
@property (nonatomic , copy) NSString              * speekerIco;
@property (nonatomic , copy) NSString              * speekerName;
@property (nonatomic , assign) NSInteger              vipSentence;

@end

NS_ASSUME_NONNULL_END
