//
//  FMPrivateLetterTextFrameModel.m
//  QCYZT
//
//  Created by zeng on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPrivateLetterTextFrameModel.h"

#define kTextMaxWidth (UI_SCREEN_WIDTH - 125)  // 文本最大宽度

@interface FMPrivateLetterTextFrameModel()

// 文本
@property (nonatomic, assign) CGRect textF;

@end

@implementation FMPrivateLetterTextFrameModel

- (CGRect)calculateContainerViewFrameWithModel:(FMPrivateLetterModel *)model iconF:(CGRect)iconF {
    CGSize textMaxSize = CGSizeMake(kTextMaxWidth, MAXFLOAT);
    CGSize textSize = [self.attributedContent boundingRectWithSize:textMaxSize options:NSStringDrawingUsesLineFragmentOrigin context:nil].size;
    CGFloat containerY = CGRectGetMinY(iconF) + kContainerToIconYPadding;
    CGFloat textX, containerX;
    if (model.bubbleType == PrivateLetterBubbleType_MeSend) {
        containerX = CGRectGetMinX(iconF) - kContainerToIconXPadding - kBubbleInsetPadding - kTextInsetPadding * 2 - ceil(textSize.width);
        textX = kTextInsetPadding;
    } else {
        containerX = CGRectGetMaxX(iconF) + kContainerToIconXPadding;
        textX = kBubbleInsetPadding + kTextInsetPadding;
    }
    _textF = (CGRect){{textX, kTextInsetPadding}, CGSizeMake(ceil(textSize.width), textSize.height)};
    CGRect containerF = CGRectMake(containerX, containerY, ceil(textSize.width) + kTextInsetPadding * 2 + kBubbleInsetPadding, textSize.height + kTextInsetPadding * 2);
    self.bubbleF = containerF;
    
    return containerF;
}


- (NSAttributedString *)attributedContent {
    if (!_attributedContent) {
        NSString *reminder;
        // 如果发言人是投顾并且是别人发的（也就是登录账号是用户）
        if (self.model.speekerType == 2 && self.model.bubbleType == PrivateLetterBubbleType_OtherSend) {
            reminder = [NSString stringWithFormat:@"温馨提示：投顾观点仅供参考学习，不构成投资建议，据此操作风险自担！大决策投顾%@，证书编号%@", self.bigcastHomeModel.bignameRealName, self.bigcastHomeModel.cert_code];
        }
        _attributedContent = [self attributedTextWithContent:self.model.content reminder:reminder];
    }
    return _attributedContent;
}


- (NSAttributedString *)attributedTextWithContent:(NSString *)content reminder:(NSString *)reminder {
    if (!content.length) {
        return nil;
    }
    NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:content];
    // 设置字体，保证尺寸计算正确
    NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
    style.lineSpacing = 5.0;
    [attributedText addAttributes:@{NSFontAttributeName : FontWithSize(15.0), NSForegroundColorAttributeName : UIColor.up_textPrimaryColor, NSParagraphStyleAttributeName : style} range:NSMakeRange(0, attributedText.length)];
    if (reminder.length) {
        NSMutableAttributedString *reminderAttrStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"\n%@", reminder]];
        NSMutableParagraphStyle *subStyle = [[NSMutableParagraphStyle alloc] init];
        subStyle.lineSpacing = 3.0;
        [reminderAttrStr addAttributes:@{NSFontAttributeName : FontWithSize(12.0), NSForegroundColorAttributeName : UIColor.up_textSecondaryColor, NSParagraphStyleAttributeName : subStyle} range:NSMakeRange(0, reminderAttrStr.length)];
        [attributedText appendAttributedString:reminderAttrStr];
    }
    
    return attributedText;
}



@end
