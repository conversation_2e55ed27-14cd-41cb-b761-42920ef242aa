//
//  FMPrivateLetterImageFrameModel.m
//  QCYZT
//
//  Created by zeng on 2022/6/30.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPrivateLetterImageFrameModel.h"

static const CGFloat kImageReferenceWidthHeight = 160.0f; // 图片参考显示宽高

@interface FMPrivateLetterImageFrameModel()

// 图片frame
@property (nonatomic, assign) CGRect imageVF;

@end

@implementation FMPrivateLetterImageFrameModel

// 计算containerF
- (CGRect)calculateContainerViewFrameWithModel:(FMPrivateLetterModel *)model iconF:(CGRect)iconF {
    CGFloat containerY = CGRectGetMinY(iconF) + kContainerToIconYPadding;
    CGFloat imageAspectRatio = model.picWidth / model.picHeight; // 宽高比
    imageAspectRatio = (imageAspectRatio > 4) ? 4 : imageAspectRatio;
    imageAspectRatio = (imageAspectRatio < 0.25) ? 0.25 : imageAspectRatio;
    CGFloat imageWidth, imageHeight;
    if (imageAspectRatio >= 1) { // 宽更长
        imageWidth = kImageReferenceWidthHeight;
        imageHeight = imageWidth / imageAspectRatio;
    } else {
        imageHeight = kImageReferenceWidthHeight;
        imageWidth = imageHeight * imageAspectRatio;
    }
    CGFloat containerX;
     if (model.bubbleType == PrivateLetterBubbleType_MeSend) {
         containerX = CGRectGetMinX(iconF) - kContainerToIconXPadding - kBubbleInsetPadding - imageWidth;
    } else {
        containerX = CGRectGetMaxX(iconF) + kContainerToIconXPadding + kBubbleInsetPadding;
    }
    CGRect containerF = CGRectMake(containerX, containerY, imageWidth, imageHeight);
    _imageVF = CGRectMake(0, 0, imageWidth, imageHeight);
    
    return containerF;
}

@end
