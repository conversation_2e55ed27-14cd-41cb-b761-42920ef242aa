//
//  FMPrivateLetterTextCell.m
//  QCYZT
//
//  Created by zeng on 2022/6/28.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "FMPrivateLetterBaseCell.h"

@implementation FMPrivateLetterBaseCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp  {
    self.backgroundColor = FMClearColor;
    self.contentView.backgroundColor = FMClearColor;
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    // 1.时间
    UILabel *timeLabel = [[UILabel alloc] init];
    timeLabel.textAlignment = NSTextAlignmentCenter;
    timeLabel.textColor = UIColor.up_textSecondary1Color;
    timeLabel.font = FontWithSize(kTimeFontSize);
    [self.contentView addSubview:timeLabel];
    self.timeLabel = timeLabel;
    
    // 2.头像
    UIImageView *iconImgV = [[UIImageView alloc] init];
    UI_View_Radius(iconImgV, kIconWidthHeight * 0.5);
    [self.contentView addSubview:iconImgV];
    self.iconImgV = iconImgV;
    
    // 3.气泡
    [self.contentView addSubview:self.bubbleImgV];

    // 4.container
    UIView *containerView = [[UIView alloc] init];
    [self.contentView addSubview:containerView];
    self.containerView = containerView;
    containerView.userInteractionEnabled = YES;
}

- (void)setFrameModel:(FMPrivateLetterFrameModel *)frameModel {
    _frameModel = frameModel;
    FMPrivateLetterModel *model = frameModel.model;
    
    // 1.时间
    self.timeLabel.text = frameModel.timeStr;
    self.timeLabel.frame = frameModel.timeF;
    self.timeLabel.hidden = frameModel.isHiddenTime;
    
    // 2.头像
    [self.iconImgV sd_setImageWithURL:[NSURL URLWithString:model.speekerIco] placeholderImage:[UIImage imageNamed:@"userCenter_dltx"]];
    self.iconImgV.frame = frameModel.iconF;
    
    // 3.气泡
    self.bubbleImgV.frame = frameModel.bubbleF;
    if (model.bubbleType == PrivateLetterBubbleType_MeSend) {
        self.bubbleImgV.image = [self resizableBubbleImage:@"ChatBubble_WhiteRight" tintColor:UIColor.up_contentBgColor];
    } else {
        self.bubbleImgV.image = [self resizableBubbleImage:@"ChatBubble_WhiteLeft" tintColor:UIColor.fm_live_chatTextFromMe_BgColor];
    }
    
    // 4.container
    self.containerView.frame = frameModel.containerF;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    if (self.frameModel.model.bubbleType == PrivateLetterBubbleType_MeSend) {
        self.bubbleImgV.image = [self resizableBubbleImage:@"ChatBubble_WhiteRight" tintColor:UIColor.up_contentBgColor];
    } else {
        self.bubbleImgV.image = [self resizableBubbleImage:@"ChatBubble_WhiteLeft" tintColor:UIColor.fm_live_chatTextFromMe_BgColor];
    }
}

// 缩放气泡图片
- (UIImage *)resizableBubbleImage:(NSString *)name tintColor:(UIColor *)color {
    UIImage *normal = [UIImage imageWithTintColor:color blendMode:kCGBlendModeDestinationIn WithImageObject:[UIImage imageNamed:name]] ;
    CGFloat w = normal.size.width * 0.5-1;
    CGFloat h = normal.size.height * 0.85-1;
    return [normal resizableImageWithCapInsets:UIEdgeInsetsMake(h, w, normal.size.height - h - 1, normal.size.width - w - 1)];
}

- (UIImageView *)bubbleImgV {
    if (!_bubbleImgV) {
        _bubbleImgV = [UIImageView new];
    }
    
    return _bubbleImgV;
}

@end
