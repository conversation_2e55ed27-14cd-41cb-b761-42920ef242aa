//
//  TagListView.m
//  TagListDemo
//
//  Created by 涂威 on 2017/7/28.
//  Copyright © 2017年 涂威. All rights reserved.
//

#import "TagListView.h"

@interface TagListView ()

@property (nonatomic, assign, readwrite) CGFloat containerHeight;

@end

@implementation TagListView

- (instancetype)init {
    if (self = [super init]) {
        self.itemFont = [UIFont systemFontOfSize:12];
        self.itemBorderColor = [UIColor clearColor];
        self.itemBackgroundColor = [UIColor clearColor];
        self.itemTextColor = [UIColor whiteColor];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
}

- (void)setTagList:(NSArray *)tagList {
    _tagList = tagList;
    [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    
    if (tagList.count > 0) {
        NSInteger row = 1;
        UIButton *lastItem;
        for (int i=0; i<tagList.count; i++) {
            NSString *title = tagList[i];
            if (!title.length) {
                continue;
            }
            
            UIButton *item = [UIButton buttonWithType:UIButtonTypeCustom];
            [item setTitle           : title forState:UIControlStateNormal];
            [item setTitleColor      : self.itemTextColor forState:UIControlStateNormal];
            item.titleLabel.font     = self.itemFont;
            item.layer.borderWidth   = self.itemBorderWidth;
            item.layer.borderColor   = self.itemBorderColor.CGColor;
            item.layer.cornerRadius  = self.itemCornerRadius;
            item.layer.masksToBounds = YES;
            item.backgroundColor     = self.itemBackgroundColor;
            [item addTarget:self action:@selector(clickItem:) forControlEvents:UIControlEventTouchUpInside];
            // 计算 itemSize
            CGSize titleSize = [title sizeWithAttributes:@{NSFontAttributeName:self.itemFont}];
            UIEdgeInsets inset = self.containerInsets;
            CGFloat itemWidth  = MIN(titleSize.width+2.f*self.paddingX, self.frame.size.width-inset.left-inset.right);
            CGFloat itemHeight = titleSize.height+2.f*self.paddingY;
            CGSize itemSize = CGSizeMake(itemWidth, itemHeight);
            
            // 计算 frame
            CGRect frame = item.frame;
            frame.size = itemSize;
            if (lastItem) {
                if (CGRectGetMaxX(lastItem.frame)+self.itemSpacing+itemWidth+inset.right > self.frame.size.width) {
                    // 需要换行
                    row++;
                    if (self.numberOfRows > 0 && row > self.numberOfRows) {
                        // 固定行且行数超出限制，跳出循环
                        break;
                    }
                    // 自动换行，或者固定行但行数未超出限制
                    frame.origin = CGPointMake(inset.left, CGRectGetMaxY(lastItem.frame)+self.rowSpacing);
                } else {
                    // 同一行
                    frame.origin = CGPointMake(CGRectGetMaxX(lastItem.frame)+self.itemSpacing, CGRectGetMinY(lastItem.frame));
                }
            } else {
                frame.origin = CGPointMake(inset.left, inset.top);
            }
            item.frame = frame;
            
            // 修改自身高度，并重新赋值高度
            self.containerHeight = CGRectGetMaxY(item.frame) + inset.bottom;
            CGRect rect = self.frame;
            rect.size.height = self.containerHeight;
            self.frame = rect;
            [self addSubview:item];
            
            //准备下一次循环
            lastItem = item;
        }
    }
}

- (void)clickItem:(UIButton *)sender {
    if (self.clickBlock) self.clickBlock([sender titleForState:UIControlStateNormal]);
}

@end
