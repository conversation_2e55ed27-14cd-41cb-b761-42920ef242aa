//
//  AskCodeAnswerTopCell.m
//  QCYZT
//
//  Created by th on 17/1/12.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "AskCodeAnswerTopCell.h"
#import "FMAskCodeModel.h"

@interface AskCodeAnswerTopCell()

@property (weak, nonatomic) UILabel *questionerLabel; // 提问人

@property (weak, nonatomic) UILabel *detailLB; // 内容详情

@property (nonatomic,weak) UILabel *timeLabel;


@end

@implementation AskCodeAnswerTopCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    // 提问人
    UILabel *questionLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(14) textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:questionLabel];
    [questionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.top.equalTo(@21);
    }];
    self.questionerLabel = questionLabel;
    
    // 时间
    UILabel *timeLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentRight];
    [self.contentView addSubview:timeLabel];
    [timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-15);
        make.centerY.equalTo(questionLabel);
        make.left.equalTo(questionLabel.mas_right).offset(15);
    }];
    [timeLabel setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [timeLabel setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    self.timeLabel = timeLabel;

    // 详情
    UILabel *detailLB = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:detailLB];
    [detailLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(questionLabel);
        make.top.equalTo(questionLabel.mas_bottom).offset(15);
        make.right.bottom.equalTo(@(-15));
    }];
    self.detailLB = detailLB;
}

- (void)setModel:(FMAskCodeModel *)model {
    _model = model;
    
    if (model.questionUserName.length > 0) {
        NSString *str = [NSString stringWithFormat:@"来自%@的提问",model.questionUserName];
        NSRange range = [str rangeOfString:model.questionUserName];
        NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:str];
        [attrStr addAttributes:@{NSForegroundColorAttributeName:ColorWithHex(0x0076ff)} range:range];
        self.questionerLabel.attributedText = attrStr;
    }
    
    if (model.questionContent.length > 0) {
        NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
        style.lineSpacing = 6.0f;
        NSDictionary *attr = @{NSParagraphStyleAttributeName:style};
        NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:model.questionContent attributes:attr];
        self.detailLB.attributedText = attrStr;
    }
    
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:model.questionTime / 1000];
    NSString *timeStr = nil;
    if ([date isToday]) {
        timeStr = [NSString stringFromDate:date format:@"HH:mm"];
    } else {
        if ([date isThisYear]) {
            timeStr = [NSString stringFromDate:date format:@"MM-dd HH:mm"];
        } else {
            timeStr = [NSString stringFromDate:date format:@"yyyy-MM-dd"];
        }
    }
    self.timeLabel.text = timeStr;
}

@end
