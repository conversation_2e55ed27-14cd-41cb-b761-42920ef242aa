//
//  AskCodeAnswerBottomCell.m
//  QCYZT
//
//  Created by th on 17/1/12.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "AskCodeAnswerBottomCell.h"
#import "FMAskCodeModel.h"
#import "RecorderView.h"
#import "CustomSwitch.h"

@interface AskCodeAnswerBottomCell()

@property (nonatomic, strong) UIView *topView;
@property (nonatomic, strong) UILabel *questionerLabel;
@property (nonatomic, strong) UILabel *stockLabel;

@property (weak, nonatomic) UILabel *descLabel;
@property (nonatomic, strong) CustomSwitch *publicSwitch;
@property (weak, nonatomic) UIButton *voiceBtn;

@end

@implementation AskCodeAnswerBottomCell

+ (instancetype)cellWithTableView:(UITableView *)tableView {
    static NSString *ID = @"AskCodeAnswerBottomCell";
    AskCodeAnswerBottomCell *cell = [tableView dequeueReusableCellWithIdentifier:ID];
    if (!cell) {
        cell = [[AskCodeAnswerBottomCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:ID];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    return cell;
}

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setUp];
    }
    return  self;
}

- (void)setUp {
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    UIView *grayView = [[UIView alloc] init];
    [self.contentView addSubview:grayView];
    [grayView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(@0);
        make.height.equalTo(@10);
    }];
    grayView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    
    UIView *topView = [[UIView alloc] init];
    [self.contentView addSubview:topView];
    [topView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(grayView.mas_bottom).offset(20);
    }];
    topView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    UI_View_Radius(topView, 8);
    self.topView = topView;
    
    UILabel *questionerLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentCenter];
    [topView addSubview:questionerLabel];
    [questionerLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(@20);
        make.left.equalTo(@20);
    }];
    self.questionerLabel = questionerLabel;
    
    UILabel *stockLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [topView addSubview:stockLabel];
    [stockLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(questionerLabel.mas_bottom).offset(10);
        make.bottom.equalTo(@-20);
    }];
    self.stockLabel = stockLabel;
    
    UILabel *publicLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(16) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:publicLabel];
    [publicLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@-30);
        make.top.equalTo(topView.mas_bottom).offset(30);
    }];
    publicLabel.text = @"是否公开该回答";
    
    NSDictionary *dic = @{NSFontAttributeName : FontWithSize(14), NSForegroundColorAttributeName : UIColor.up_contentBgColor};
    CustomSwitch *publicSwitch = [[CustomSwitch alloc] init];
    publicSwitch.onAttrString = [[NSAttributedString alloc] initWithString:@"是" attributes:dic];
    publicSwitch.offAttrString = [[NSAttributedString alloc] initWithString:@"否" attributes:dic];
    publicSwitch.on = NO;
    publicSwitch.onBgColor = UIColor.up_riseColor;
    publicSwitch.offBgColor = ColorWithHex(0x999ca1);
    publicSwitch.sliderColor = UIColor.whiteColor;
    [self.contentView addSubview:publicSwitch];
    [publicSwitch mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(publicLabel.mas_right).offset(12);
        make.centerY.equalTo(publicLabel);
        make.width.equalTo(@51);
        make.height.equalTo(@25);
    }];
    [publicSwitch addTarget:self action:@selector(valueChanged:) forControlEvents:UIControlEventValueChanged];
    self.publicSwitch = publicSwitch;
    
    UIButton *voiceBtn = [[UIButton alloc] init];
    [self.contentView addSubview:voiceBtn];
    [voiceBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(publicLabel.mas_bottom).offset(26);
        make.width.height.equalTo(@95);
    }];
    [voiceBtn setBackgroundImage:[UIImage imageNamed:@"askcode_answer_microphone"] forState:UIControlStateNormal];
    [voiceBtn addTarget:self action:@selector(voiceBtnClicked) forControlEvents:UIControlEventTouchUpInside];
    self.voiceBtn = voiceBtn;
    
    UILabel *descLabel = [[UILabel alloc] init];
    [self.contentView addSubview:descLabel];
    [descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(@0);
        make.top.equalTo(voiceBtn.mas_bottom).offset(20);
        make.bottom.equalTo(-50);
    }];
    descLabel.font = [UIFont systemFontOfSize:16];
    descLabel.textColor = UIColor.fm_market_nav_text_zeroColor;
    descLabel.numberOfLines = 0;
    descLabel.textAlignment = NSTextAlignmentCenter;
    self.descLabel = descLabel;
}

- (void)setModel:(FMAskCodeModel *)model {
    _model = model;
    self.descLabel.text = [NSString stringWithFormat:@"回答本题收入%@金币\n后续收听每人收费%@金币", model.questionPrice,model.listenPriceStr];
    
    self.questionerLabel.text = [NSString stringWithFormat:@"用户会员情况：%@",model.vipMessage.length ? model.vipMessage : @"非会员"];
    self.stockLabel.text = [NSString stringWithFormat:@"股票情况：%@",model.stockPosition.length ? model.stockPosition : @"普通问股"];
}

- (void)voiceBtnClicked {
    RecorderView *view = [RecorderView new];
    view.questionId = self.model.askCodeId;
    
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    view.frame = keyWindow.bounds; // Ensure view covers the window initially for background dimming
    [keyWindow addSubview:view];
    
    // Initial position off-screen at the bottom
    view.outView.transform = CGAffineTransformMakeTranslation(0, keyWindow.bounds.size.height);

    // Animate the view sliding up from the bottom
    [UIView animateWithDuration:0.3 animations:^{
        view.outView.transform = CGAffineTransformIdentity;
    }];
}

- (void)valueChanged:(CustomSwitch *)switchBtn {
    if (self.publicSwitchChangeValue) {
        self.publicSwitchChangeValue(switchBtn.isOn);
    }
}

@end
