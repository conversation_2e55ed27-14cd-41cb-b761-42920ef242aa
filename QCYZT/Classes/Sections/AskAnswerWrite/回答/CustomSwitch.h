//
//  CustomSwitch.h
//  QCYZT
//
//  Created by zeng on 2022/3/23.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface CustomSwitch : UIControl

@property (nonatomic, copy) NSAttributedString *onAttrString; // 打开时文本
@property (nonatomic, copy) NSAttributedString *offAttrString; // 关闭时文本
@property (nonatomic, strong) UIColor *onBgColor;  // 打开时背景色
@property (nonatomic, strong) UIColor *offBgColor; // 关闭时背景色
@property (nonatomic, strong) UIColor *sliderColor;  // 滑块色

@property (nonatomic, assign, getter=isOn) BOOL on;

@end

NS_ASSUME_NONNULL_END
