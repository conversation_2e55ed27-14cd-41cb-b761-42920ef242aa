//
//  AskCodeAnswerViewController.m
//  QCYZT
//
//  Created by th on 17/1/12.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "AskCodeAnswerViewController.h"
#import "AskCodeAnswerTopCell.h"
#import "AskCodeAnswerBottomCell.h"
#import "FMAskCodeModel.h"

@interface AskCodeAnswerViewController() <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic,strong) UITableView *tableView;
@property (nonatomic,strong) FMAskCodeModel *detailModel;

@property (nonatomic, assign) BOOL isPublic;

@end

@implementation AskCodeAnswerViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"问股回答";
    self.isPublic = NO;
    
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    WEAKSELF;
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.bottom.equalTo(__weakSelf.view);
    }];
    
    self.tableView.hidden = YES;
    [self requestNoteDetail];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(submitAnswer:) name:kAliUpdateMp3Success object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];

}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - TableViewDelegate DataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 2;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        AskCodeAnswerTopCell *cell = [tableView reuseCellClass:[AskCodeAnswerTopCell class]];
        cell.model = self.detailModel;
        
        return cell;
    } else {
        AskCodeAnswerBottomCell *cell = [AskCodeAnswerBottomCell cellWithTableView:tableView];
        cell.model = self.detailModel;
        WEAKSELF
        cell.publicSwitchChangeValue = ^(BOOL isPublic) {
            __weakSelf.isPublic = isPublic;
        };
        return cell;
    }
}

//- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
//    if (indexPath.section == 0) {
//        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([AskCodeAnswerTopCell class]) configuration:^(AskCodeAnswerTopCell *cell) {
//            cell.model = self.detailModel;
//        }];
//    } else {
//        return 400.0f;
//    }
//}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}


#pragma mark - HTTP
- (void)requestNoteDetail {
    WEAKSELF;
    [HttpRequestTool getQuestionDetailWithWithQuestionId:self.questionId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            __weakSelf.tableView.hidden = NO;
            __weakSelf.detailModel = [FMAskCodeModel modelWithDictionary:dic[@"data"]];
            [__weakSelf.tableView reloadData];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (void)submitAnswer:(NSNotification *)noti {
    NSDictionary *userInfo = noti.userInfo;
    if (![userInfo[@"questionId"] isEqualToString:self.questionId]) {
        return;
    }
    
    WEAKSELF;
    [HttpRequestTool questionAnswerWithQuestionid:self.questionId timeLength:userInfo[@"length"] ossUrl:userInfo[@"url"] isPublic:(self.isPublic ? @"1" : @"0") start:^{
        [SVProgressHUD show];
    } failure:^{
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        });
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [SVProgressHUD showSuccessWithStatus:@"上传成功"];
            });

            [[NSNotificationCenter defaultCenter] postNotificationName:kSubmitAnswerSuccess object:nil userInfo:@{@"questionId":__weakSelf.questionId}];
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [__weakSelf.navigationController popViewControllerAnimated:YES];
            });
        } else {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            });
        }
    }];
}

#pragma mark - Private
- (void)back {
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - Getter/Setter
- (UITableView *)tableView {
    if (!_tableView) {
        UITableView *tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
        _tableView = tableView;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.bounces = NO;
        _tableView.delegate = self;
        _tableView.dataSource = self;
        _tableView.estimatedRowHeight = 300;
        _tableView.rowHeight = UITableViewAutomaticDimension;
        _tableView.backgroundColor = UIColor.up_contentBgColor;
        
        [_tableView registerCellClass:[AskCodeAnswerTopCell class]];
    }
    
    return _tableView;
}

@end

