//
//  AskQuestionListViewController.m
//  QCYZT
//
//  Created by <PERSON><PERSON> on 2017/1/10.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "AskQuestionListViewController.h"
#import "AskQuestionTableViewCell.h"
#import "BigCastModel.h"
#import "FMCommentView.h"
#import "FMBigCastCommonModel.h"
#import "FMPayTool.h"
#import "UnLoginView.h"
#import "HttpRequestTool+Home.h"
#import "HttpRequestTool+Daka.h"

@interface AskQuestionListViewController ()<UITableViewDelegate,UITableViewDataSource, UIAlertViewDelegate>

@property (nonatomic, strong) UITableView *bigCastTab;
@property (nonatomic, strong) NSMutableArray *listDataArr;
@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;
@property (nonatomic, strong) FMCommentView *commentView;
@property (nonatomic, copy) NSString *answerId;            // 投顾id
@property (nonatomic, copy) NSString *cert_code;           // 证书编号
@property (nonatomic, copy) NSString *questionContent;
@property (nonatomic, strong) FMSearchStockModel *choosedStockModel;
@property (nonatomic, strong) UnLoginView *unLoginedView;

@property (nonatomic, assign) BOOL needsRefresh;
@end

@implementation AskQuestionListViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    [self setInitData];
    
    [self.view addSubview:self.bigCastTab];
    [self.bigCastTab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    [self.bigCastTab.mj_header beginRefreshing];
    
    if ([self.list_type integerValue] == 1) {
        [FMHelper addLoginAndLogoutNotificationWithObserver:self selector:@selector(handleLoginStatusNotification) monitorAuthLogin:NO];
    }
}


#pragma mark - NSNotification
- (void)handleLoginStatusNotification {
    self.needsRefresh = YES;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if ([self.list_type isEqualToString:@"1"]) {
        if (![FMUserDefault getUserId].length) {
            [self.view addSubview:self.unLoginedView];
            [self.unLoginedView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.edges.equalTo(self.view);
            }];
        } else {
            if (self.unLoginedView.superview) {
                [self.unLoginedView removeFromSuperview];
            }
        }
    }
    
    if (self.needsRefresh) {
        self.needsRefresh = NO;
        self.page = 1;
        self.currentPage = self.page;
        [self getBigCastListData];
    }
}

#pragma mark -- 提问
- (void)publishComment:(NSIndexPath *)index {
    if ([self checkLoginStatus]) {
        [self gotoPayWithIndex:index];
    };
}

- (void)gotoPayWithIndex:(NSIndexPath *)index {
    NSString *answerPrice = nil;
    NSString *dakaName = nil;
    NSInteger windowType = 0;
    if ([self.list_type isEqualToString:@"1"]) {
        BigCastModel *model = self.listDataArr[index.row];
        self.answerId = model.userid;
        self.cert_code = model.cert_code;
        answerPrice = model.answer_price;
        dakaName = model.user_name;
        windowType = model.windowType;
        if ([self.answerId isEqualToString:[FMUserDefault getUserId]]) {
            [SVProgressHUD showInfoWithStatus:@"自己不能向自己提问"];
            return ;
        }
    } else if ([self.list_type isEqualToString:@"2"]) {
        FMBigCastCommonModel *recommendModel = self.listDataArr[index.row];
        self.answerId = recommendModel.userId;
        self.cert_code = recommendModel.certCode;
        answerPrice = recommendModel.answerPrice;
        dakaName = recommendModel.userName;
        if ([self.answerId isEqualToString:[FMUserDefault getUserId]]) {
            [SVProgressHUD showInfoWithStatus:@"自己不能向自己提问"];
            return ;
        }
    }
    
    WEAKSELF
    [[FMPayTool payTool] judgeConfirmOrderStatusWithDakaId:__weakSelf.answerId certCode:__weakSelf.cert_code clickView:__weakSelf.bigCastTab confirmOperation:^{
        __weakSelf.commentView = nil;
        UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
        self.commentView.frame = keyWindow.bounds;
        [keyWindow addSubview:__weakSelf.commentView];
        __weakSelf.commentView.publisLb.textColor = UIColor.up_textPrimaryColor;
        __weakSelf.commentView.publisLb.text =  [NSString stringWithFormat:@"向%@老师提问", dakaName];
        __weakSelf.commentView.askPrice = answerPrice;
        if (__weakSelf.relationStock) {
            __weakSelf.commentView.stockTF.text = __weakSelf.relationStock.name;
            FMSearchStockModel *model = [FMSearchStockModel modelWithDictionary:[__weakSelf.relationStock modelToJSONObject]];
            model.oldStockCode = [FMUPDataTool oldDataWithNewSetCodeAndCode:[FMUPDataTool jointWithSetCode:__weakSelf.relationStock.setCode code:__weakSelf.relationStock.code]];
            __weakSelf.commentView.choosedStockModel = model;
            __weakSelf.commentView.publishBtn.userInteractionEnabled = YES;
            [__weakSelf.commentView.publishBtn setBackgroundColor:FMNavColor];
        }
        __weakSelf.commentView.placeholderText = @"输入您的问题。若已买入该股票，可告知“买入价格”和“仓位情况”，更便于老师根据您的实际情况给予建议。\n若超过预期时间未解答，提问费用或卡券将会自动退回。";
        __weakSelf.commentView.bignameId = __weakSelf.answerId;
        __weakSelf.commentView.consumeType = 2;
        [__weakSelf.commentView requestCouponList];
        WEAKSELF;
        __weakSelf.commentView.publishAskCodeBlock = ^(NSString *content, FMSearchStockModel *stockModel) {
            __weakSelf.questionContent = content;
            __weakSelf.choosedStockModel = stockModel;
            
            NSString *consume = answerPrice;
            if (__weakSelf.commentView.couponId.length > 0) {
                consume = [NSString stringWithFormat:@"%.f",answerPrice.integerValue -  __weakSelf.commentView.selectedCoupon.value];
            }
            [[FMPayTool payTool] compareCoinRemainderWithConsume:consume clickView:nil completeBlock:^(NSString *coinRemainder) {
                // 查询问股回答所需时长
                [__weakSelf checkWaitTime];
            }];
        };
    }];
}


/// 查询问股回答所需时长
- (void)checkWaitTime {
    [HttpRequestTool questionCheckWaitTimeWithAnswerId:self.answerId start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            UIAlertView *alertView = [[UIAlertView alloc] initWithTitle:nil
                                                                message:dic[@"data"]
                                                               delegate:self
                                                      cancelButtonTitle:@"取消"
                                                      otherButtonTitles:@"确定", nil];
            [alertView show];
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

- (BOOL)checkLoginStatus {
    if (![FMUserDefault getUserId].length) {
        [ProtocolJump jumpWithUrl:Login_URL];
        return NO;
    }
    
    return YES;
}

#pragma mark -- UITabViewDelegate & Datasource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return [self.listDataArr count];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    AskQuestionTableViewCell *cell = [self.bigCastTab dequeueReusableCellWithIdentifier:@"AskQuestionTableViewCell" forIndexPath:indexPath];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    if ([self.list_type isEqualToString:@"1"]) {
        if ([self.listDataArr count] > 0) {
            cell.model = self.listDataArr[indexPath.row];
        }
    } else if ([self.list_type isEqualToString:@"2"]) {
        if ([self.listDataArr count] > 0) {
            cell.recommendModel = self.listDataArr[indexPath.row];
        }
    }
    
    WEAKSELF;
    cell.clickBlock = ^() {
        // 提问弹窗
        [__weakSelf publishComment:indexPath];
    };
 
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return [tableView fd_heightForCellWithIdentifier:@"AskQuestionTableViewCell"  configuration:^(AskQuestionTableViewCell *cell) {
        if ([self.list_type isEqualToString:@"1"]) {
            if ([self.listDataArr count] > 0) {
                cell.model = self.listDataArr[indexPath.row];
            }
        } else if ([self.list_type isEqualToString:@"2"]) {
            if ([self.listDataArr count] > 0) {
                cell.recommendModel = self.listDataArr[indexPath.row];
            }
        }
    }];;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    AskQuestionTableViewCell *cell = [tableView cellForRowAtIndexPath:indexPath];
    [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://bigname?id=%@", cell.userId]];
}

#pragma mark - UIAlertView Delegate
- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex {
    if (buttonIndex == 1) {
        WEAKSELF;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [HttpRequestTool questionAskWithAnswerId:__weakSelf.answerId content:__weakSelf.questionContent type:__weakSelf.commentView.type goodsId:__weakSelf.commentView.couponId  stockCode:__weakSelf.choosedStockModel.oldStockCode stockName:__weakSelf.choosedStockModel.name start:^{
                [SVProgressHUD show];
            } failure:^{
                [SVProgressHUD showErrorWithStatus:@"网络不给力"];
            } success:^(NSDictionary *dic) {
                if ([dic[@"status"] isEqualToString:@"1"]) {
                    [SVProgressHUD showSuccessWithStatus:@"您的提问已成功发送，请注意接收回复通知"];
                    __weakSelf.commentView.textView.text = @"";
                    __weakSelf.answerId = nil;
                    __weakSelf.questionContent = nil;
                    __weakSelf.choosedStockModel = nil;
                    __weakSelf.commentView = nil;
                    [[NSNotificationCenter defaultCenter] postNotificationName:kAskCodeSuccess object:nil];
                } else {
                    [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
                }
            }];
        });
    } else {
        self.answerId = nil;
        self.questionContent = nil;
        self.choosedStockModel = nil;
    }
}


#pragma mark -- 数据请求
- (void)getBigCastListData{
    WEAKSELF;
    if ([self.list_type isEqualToString:@"1"]) {
        // 我的关注
        [HttpRequestTool getDakaListWithWithPage:self.page pageSize:self.pageSize orderType:@"2" userType:nil keyWord:nil goodAt:nil listType:@"4" start:^{
        } failure:^{
            [__weakSelf endRefreshForFailure];
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                __weakSelf.currentPage = __weakSelf.page;
                if (__weakSelf.page == 1) {
                    [__weakSelf.bigCastTab.mj_header endRefreshing];
                    [__weakSelf.bigCastTab.mj_footer resetNoMoreData];
                    [__weakSelf.listDataArr removeAllObjects];
                } else {
                    [__weakSelf.bigCastTab.mj_footer endRefreshing];
                }

                NSArray *dataArr = [NSArray modelArrayWithClass:[BigCastModel class] json:dic[@"data"]];
                if (dataArr.count < self.pageSize) {
                    [__weakSelf.bigCastTab.mj_footer endRefreshingWithNoMoreData];
                }
                
                [__weakSelf dealDuplicateDataWithArr:dataArr];
                if (!__weakSelf.listDataArr.count) {
                    [__weakSelf.bigCastTab showNoDataViewWithString:@"暂无关注" attributes:nil position:ShowPositionCenter offsetX:0 offsetY:-30];
                    self.bigCastTab.mj_footer.hidden = YES;
                } else {
                    [__weakSelf.bigCastTab dismissNoDataView];
                    self.bigCastTab.mj_footer.hidden = NO;
                }
                [__weakSelf.bigCastTab reloadData];
            } else {
                [__weakSelf endRefreshForFailure];

                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    } else if ([self.list_type isEqualToString:@"2"]) {
        // 推荐
        [HttpRequestTool getRecommendWithPositionId:@"200105" page_no:self.page page_size:self.pageSize start:^{
            
        } failure:^{
            [__weakSelf endRefreshForFailure];
            
            [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        } success:^(NSDictionary *dic) {
            if ([dic[@"status"] isEqualToString:@"1"]) {
                __weakSelf.currentPage = __weakSelf.page;
                if (__weakSelf.page == 1) {
                    [__weakSelf.bigCastTab.mj_header endRefreshing];
                    [__weakSelf.bigCastTab.mj_footer resetNoMoreData];
                    [__weakSelf.listDataArr removeAllObjects];
                } else {
                    [__weakSelf.bigCastTab.mj_footer endRefreshing];
                }
                
                NSArray *dataArr = [NSArray modelArrayWithClass:[FMBigCastCommonModel class] json:dic[@"data"]];
                if (dataArr.count < self.pageSize) {
                    [__weakSelf.bigCastTab.mj_footer endRefreshingWithNoMoreData];
                }
                [__weakSelf.listDataArr addObjectsFromArray:dataArr];
                
                __weakSelf.bigCastTab.mj_footer.hidden = !__weakSelf.listDataArr.count;
                [__weakSelf.bigCastTab reloadData];
            } else {
                [__weakSelf endRefreshForFailure];

                [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
            }
        }];
    }
}

- (void)endRefreshForFailure {
    if (self.page > 1) { // 上拉失败
        [self.bigCastTab.mj_footer endRefreshing];
    } else {
        [self.bigCastTab.mj_header endRefreshing];
    }
    
    self.page = self.currentPage;
}

#pragma mark - Private
- (void)dealDuplicateDataWithArr:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (BigCastModel *model in array) {
        [dic setObject:model forKey:model.userid];
    }
    
    for (BigCastModel *model in self.listDataArr.reverseObjectEnumerator) {
        if ([dic valueForKey:model.userid]) {
            [self.listDataArr removeObject:model];
        }
    }
    
    [self.listDataArr addObjectsFromArray:array];
}


- (void)setInitData {
    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 10;
}

#pragma mark -- lazy

- (UITableView *)bigCastTab {
    if (!_bigCastTab) {
        _bigCastTab = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(headerAction) footerTarget:self footerAction:@selector(footerAction)];
        _bigCastTab.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        _bigCastTab.separatorStyle = UITableViewCellSeparatorStyleNone;
        [_bigCastTab registerClass:[AskQuestionTableViewCell class] forCellReuseIdentifier:@"AskQuestionTableViewCell"];
        _bigCastTab.tableFooterView = [UIView new];
        _bigCastTab.mj_footer.hidden = YES;
    }
    return _bigCastTab;
}

- (void)headerAction {
    self.page = 1;
    [self getBigCastListData];
}

- (void)footerAction {
    self.page++;
    [self getBigCastListData];
}

- (FMCommentView *)commentView {
    if (!_commentView) {
        _commentView = [[FMCommentView alloc] initWithCouponFrame:[UIScreen mainScreen].bounds];
    }
    return _commentView;
}

- (NSMutableArray *)listDataArr {
    if (!_listDataArr) {
        _listDataArr = [NSMutableArray array];
    }
    
    return _listDataArr;
}

- (UnLoginView *)unLoginedView {
    if (!_unLoginedView) {
        _unLoginedView = [[UnLoginView alloc] init];
        _unLoginedView.frame = CGRectZero;
        _unLoginedView.backgroundColor = ColorWithHex(0xefefef);
    }
    return _unLoginedView;
}

@end
