//
//  FMMainTabController.m
//  QCYZT
//
//  Created by dangfm on 15/8/7.
//  Copyright (c) 2015年 dangfm. All rights reserved.
//
#import "FMMainTabController.h"
#import "FMHomePageVC.h"
#import "FMNewsMainViewController.h"

@interface FMMainTabController ()<FMTabBarDelegate>
@end

@implementation FMMainTabController


- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColor.up_contentBgColor;
    NSArray *items = @[
        @{
            @"vcName"        : @"FMHomePageVC",
            @"title"        : @"",
            @"tabBarTitle"  : @"首页"
        }
        ,
        @{
            @"vcName"        : @"FMDakaLiveMainViewController",
            @"title"        : @"投顾直播",
            @"tabBarTitle"  : @"直播"
        }
        ,
        @{
            @"vcName"        : @"StockViewController",
            @"title"        : @"",
            @"tabBarTitle"  : @"自选"
        }
        ,
        @{
            @"vcName"        : @"FMNewsMainViewController",
            @"title"        : @"",
            @"tabBarTitle"  : @"资讯"
        }
        ,
        @{
            @"vcName"        : @"FMUserCenterViewController",
            @"title"        : @"",
            @"tabBarTitle"  : @"我的"
        }
    ];
    
    FMTabBar *myTabBar = [[FMTabBar alloc] init];
    for (NSDictionary *dic in items) {
        [self addChildVcStr:dic[@"vcName"] title:dic[@"title"]];
        [myTabBar addTabButtonWithTitle:dic[@"tabBarTitle"]];
    }
    myTabBar.delegate = self;
    self.myTabBar = myTabBar;

    [self.tabBar addSubview:myTabBar];
    self.tabBar.translucent = NO; // 不设置为NO为影响tableView中cell的点击，原因未知
    [self.tabBar setTintColor:[UIColor clearColor]]; // 不设置点击会有蓝色字体出现
    
    [self tabBar:self.myTabBar didSelectButtonFrom:0 to:0];
    
    CGRect rect = CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, 0.5);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, FMSepLineColor.CGColor);
    CGContextFillRect(context, rect);
    UIImage *img = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    [self.tabBar setBackgroundImage:img];
    [self.tabBar setShadowImage:img];
    
    if (@available(iOS 15.0, *)) {
        UITabBarAppearance *appearance = [[UITabBarAppearance alloc] init];
        appearance.backgroundColor = FMWhiteColor;
        self.tabBar.scrollEdgeAppearance = appearance;
        self.tabBar.standardAppearance = appearance;
    }
    
}

- (void)viewWillLayoutSubviews {
    [super viewWillLayoutSubviews];
    
    CGRect tabFrame = self.tabBar.frame;
    tabFrame.size.height = UI_TABBAR_HEIGHT + UI_SAFEAREA_BOTTOM_HEIGHT;
    tabFrame.origin.y = self.view.frame.size.height - UI_TABBAR_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT;
    self.tabBar.frame = tabFrame;
    
    self.myTabBar.frame = self.tabBar.bounds;

}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    
    CGRect tabFrame =self.tabBar.frame;
    tabFrame.size.height = UI_TABBAR_HEIGHT + UI_SAFEAREA_BOTTOM_HEIGHT;
    tabFrame.origin.y = self.view.frame.size.height - UI_TABBAR_HEIGHT - UI_SAFEAREA_BOTTOM_HEIGHT;
    self.tabBar.frame = tabFrame;
    
    self.myTabBar.frame = self.tabBar.bounds;
}


- (void)addChildVcStr:(NSString *)childVcStr title:(NSString *)title {
    Class clazz = NSClassFromString(childVcStr);
    UIViewController *childVc = [[clazz alloc] init];
    childVc.navigationItem.title = title;
    
    FMNavigationController *nav = [[FMNavigationController alloc] initWithRootViewController:childVc];
    [self addChildViewController:nav];
}

#pragma mark - TabBarDelegate
- (void)tabBar:(FMTabBar *)tabBar didSelectButtonFrom:(NSInteger)from to:(NSInteger)to {
    self.selectedIndex = to;
    
    FMNavigationController *nav = [[FMAppDelegate shareApp].main viewControllerAtIndex:0];
    FMHomePageVC *homeVC = nav.viewControllers[0];
    if ([homeVC isKindOfClass:[FMHomePageVC class]]) {
        if (to != 0) {
            [homeVC pauseTask];
        } else {
            [homeVC resumeTask];
        }
    }
}

#pragma mark - Public
- (FMNavigationController *)currentNav {
    return (FMNavigationController *)self.selectedViewController;
}
- (FMNavigationController *)viewControllerAtIndex:(NSInteger)index {
    NSArray *viewControllers = self.viewControllers;
    if (index >= viewControllers.count) {
        return nil;
    }
    return (FMNavigationController *)viewControllers[index];
}

#pragma mark - Setter
- (void)setSelectedIndex:(NSUInteger)selectedIndex {
    [super setSelectedIndex:selectedIndex];
    if (self.myTabBar.selectedIndex == 0 && selectedIndex == 0) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kCommunityPageRefreshData object:nil];
    }
    self.myTabBar.selectedIndex = selectedIndex;
    [self.tabBar bringSubviewToFront:self.myTabBar];
}

@end
