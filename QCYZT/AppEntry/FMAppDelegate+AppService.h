//
//  FMAppDelegate+AppService.h
//  QCYZT
//
//  Created by zeng on 2021/8/10.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMAppDelegate.h"


@interface FMAppDelegate (AppService)

// 配置一些初始化数据
- (void)configInitializationData;

// 配置默认的请求地址
- (void)configDefaultRequestPrefix;

// 初始化接口
- (void)requestAppInitWithSuccess:(void(^)(void))successBlock;

/**
静默登录接口 (登录状态下 首页弹窗广告需要在静默登录接口之后调用 通过静默登录接口更新session 首页弹窗广告才能正确返回 )
 */
- (void)requestSilentContent;

@end

