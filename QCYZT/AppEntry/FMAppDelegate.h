//
//  AppDelegate.h
//  QCYZT
//
//  Created by dangfm on 15/8/7.
//  Copyright (c) 2015年 dangfm. All rights reserved.
//

#import "FMMainTabController.h"
#import "WXApi.h"
#import <AVKit/AVKit.h>
#import "WXApiManager.h"

@class FMAppDelegate;
typedef void (^didRotationBlock)(FMAppDelegate *app);


@interface FMAppDelegate : UIResponder <UIApplicationDelegate, WXApiManagerDelegate>

@property (strong, nonatomic) UIWindow *window;

@property (nonatomic, strong) UIWindow *launchWindow;

/**
 *  核心tabbar
 */
@property (strong, nonatomic) FMMainTabController *main;

/**
 *  创建单例
 */
+(FMAppDelegate*)shareApp;

// 配置画中画
- (void)configPiP:(AVPlayerLayer *)layer;
@property (nonatomic,assign) BOOL isLandScape;

// app初始化标记
@property (nonatomic, assign) NSInteger appInitRetryCount;


@end

