<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="FMFlaseLaunchViewController">
            <connections>
                <outlet property="containView" destination="DBB-B0-Uhw" id="qrl-Ci-okd"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DBB-B0-Uhw">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="四川大决策证券投资顾问有限公司" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sQv-Er-Twb">
                            <rect key="frame" x="104.66666666666669" y="788.66666666666663" width="184" height="14.333333333333371"/>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="calibratedRGB"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="LaunchLogo.png" translatesAutoresizingMaskIntoConstraints="NO" id="nsB-iU-A9C">
                            <rect key="frame" x="146.66666666666666" y="275.66666666666669" width="100" height="108.33333333333331"/>
                        </imageView>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="LaunchDJC.png" translatesAutoresizingMaskIntoConstraints="NO" id="UU6-s3-f0I">
                            <rect key="frame" x="163" y="752.66666666666663" width="67.333333333333314" height="26"/>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="nsB-iU-A9C" firstAttribute="centerX" secondItem="DBB-B0-Uhw" secondAttribute="centerX" id="PDx-S3-1FP"/>
                        <constraint firstItem="UU6-s3-f0I" firstAttribute="centerX" secondItem="DBB-B0-Uhw" secondAttribute="centerX" id="v1f-Gg-HoW"/>
                        <constraint firstItem="sQv-Er-Twb" firstAttribute="centerX" secondItem="DBB-B0-Uhw" secondAttribute="centerX" id="wd8-Tn-fnn"/>
                        <constraint firstItem="sQv-Er-Twb" firstAttribute="top" secondItem="UU6-s3-f0I" secondAttribute="bottom" constant="10" id="wt0-vn-g8L"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="rvq-s9-Z38"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="DBB-B0-Uhw" secondAttribute="trailing" id="BEo-Db-EEo"/>
                <constraint firstItem="nsB-iU-A9C" firstAttribute="top" secondItem="rvq-s9-Z38" secondAttribute="top" constant="216.66999999999999" id="FCX-zp-GWL"/>
                <constraint firstItem="DBB-B0-Uhw" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="Fyk-JC-nBA"/>
                <constraint firstAttribute="bottom" secondItem="DBB-B0-Uhw" secondAttribute="bottom" id="HKe-KT-YzQ"/>
                <constraint firstItem="rvq-s9-Z38" firstAttribute="bottom" secondItem="sQv-Er-Twb" secondAttribute="bottom" constant="15" id="XyU-67-R8B"/>
                <constraint firstItem="DBB-B0-Uhw" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="sU1-IP-RJL"/>
            </constraints>
            <point key="canvasLocation" x="-190" y="21"/>
        </view>
    </objects>
    <resources>
        <image name="LaunchDJC.png" width="67.333335876464844" height="26"/>
        <image name="LaunchLogo.png" width="100" height="108.33333587646484"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
