//
//  AppIntroduceViewController.h.m
//  QCYZT
//
//  Created by macPro on 2017/9/20.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "AppIntroduceViewController.h"

@interface AppIntroduceViewController () <UIScrollViewDelegate>

@property (nonatomic, strong) UIWindow *window;
@property (nonatomic, strong) NSArray *imageFiles;
@property (nonatomic, copy) void(^dismissBlock)(void);

@end

@implementation AppIntroduceViewController

#pragma mark - Initialization
+ (instancetype)presentedWithKey:(NSString *)key imageFiles:(NSArray *)imageFiles dismissBlock:(void (^)(void))dismissBlock {
    if ([[NSUserDefaults standardUserDefaults] boolForKey:key]) {
        return nil;
    }
    AppIntroduceViewController *vc = [[self alloc] initWithImageFiles:imageFiles dismissBlock:dismissBlock];
    [vc presented];
    return vc;
}

- (instancetype)initWithImageFiles:(NSArray *)imageFiles dismissBlock:(void (^)(void))dismissBlock{
    if (self = [super init]) {
        self.imageFiles = imageFiles;
        self.dismissBlock = dismissBlock;
    }
    return self;
}


#pragma mark - LifeCycle Method
- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = FMClearColor;
    [self addSubviews];
}

- (void)dealloc {
    FMLog(@"%s", __func__);
}

#pragma mark - Private Method
- (void)presented {
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    
    UIWindow *window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
    window.windowLevel = UIWindowLevelStatusBar - 1;
    [window makeKeyAndVisible];
    window.rootViewController = self;
    self.window = window;
    if (@available(iOS 13.0, *)) {
        self.window.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
    } else {
        // Fallback on earlier versions
    }
    window.backgroundColor = FMZeroColor;
    
    [keyWindow makeKeyWindow];
}

- (void)addSubviews {
    UIScrollView *scrollView = [[UIScrollView alloc] initWithFrame:[UIScreen mainScreen].bounds];
    scrollView.contentSize = CGSizeMake(UI_SCREEN_WIDTH * self.imageFiles.count, 0);
    scrollView.pagingEnabled = YES;
    scrollView.delegate = self;
    scrollView.bounces = NO;
    [self.view addSubview:scrollView];
    for (int i=0; i<self.imageFiles.count; i++) {
        UIImage *image = [UIImage imageWithContentsOfFile:[[NSBundle mainBundle] pathForResource:self.imageFiles[i] ofType:nil]];
        UIImageView *newPeopleImg = [[UIImageView alloc] init];
        newPeopleImg.frame = CGRectMake(i*UI_SCREEN_WIDTH , 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
        newPeopleImg.image = image;
        [scrollView addSubview:newPeopleImg];
    }
    UIButton *goInBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    goInBtn.frame = CGRectMake(80, UI_SCREEN_HEIGHT-UI_Relative_WidthValue(150), UI_SCREEN_WIDTH-160, UI_Relative_WidthValue(100));
    goInBtn.backgroundColor = [UIColor clearColor];
    [goInBtn addTarget:self action:@selector(dismiss) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:goInBtn];
}

- (void)dismiss {
    [UIView animateWithDuration:0.25 animations:^{
        self.window.alpha = 0;
        self.window.transform = CGAffineTransformMakeTranslation(-UI_SCREEN_WIDTH, 0);
    } completion:^(BOOL finished) {
        [[FMAppDelegate shareApp].window makeKeyAndVisible];
        self.window = nil;
        if (self.dismissBlock) self.dismissBlock();
    }];
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    CGPoint offset = scrollView.contentOffset;
    if (offset.x == UI_SCREEN_WIDTH * (self.imageFiles.count-1)) {
        [self performSelector:@selector(dismiss) withObject:nil afterDelay:3];
        UIPanGestureRecognizer *pan = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(dismiss)];
        [scrollView addGestureRecognizer:pan];
    }
}

@end
