//
//  FMTabBar.m
//  
//
//  Created by macPro on 2019/9/19.
//  Copyright © 2019 macPro. All rights reserved.
//

#import "FMTabBar.h"
#import "YYImage.h"
#import "UIImage+circleImage.h"

@interface FMTabBarButton : UIButton

@property (nonatomic, assign) CGFloat labelToBottom;
@property (nonatomic, assign) CGFloat imageToText;

@end

@implementation FMTabBarButton

-(void)layoutSubviews {
    [super layoutSubviews];
    CGPoint textCenter = self.titleLabel.center;
    textCenter.x = self.bounds.size.width * 0.5;
    textCenter.y = self.bounds.size.height - self.titleLabel.bounds.size.height * 0.5 - self.labelToBottom;
    self.titleLabel.center = textCenter;
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    
    CGPoint imageCenter = self.imageView.center;
    imageCenter.x = self.bounds.size.width * 0.5;
    imageCenter.y = self.bounds.size.height - self.titleLabel.bounds.size.height - self.labelToBottom - self.imageView.bounds.size.height * 0.5 - self.imageToText;
    self.imageView.center = imageCenter;
    
}


@end



@interface FMTabBar()

@property (nonatomic, strong) UIButton *selectedButton;

@end

@implementation FMTabBar

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = UIColor.up_contentBgColor;
    }
    
    return self;
}



- (void)addTabButtonWithTitle:(NSString *)title {
    // 创建按钮
    FMTabBarButton *button = [FMTabBarButton buttonWithType:UIButtonTypeCustom];
    button.labelToBottom = 5.5;
    button.imageToText = 2.0;
    button.titleLabel.font = [FMHelper isBigFont] ? FontWithSize(15) : FontWithSize(13);
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitleColor:UIColor.fm_tabbar_textColor forState:UIControlStateNormal];
    [button setTitleColor:UIColor.fm_tabbar_selectedTextColor forState:UIControlStateSelected];
    NSString *imgStr = [NSString stringWithFormat:@"TabBar/%@", title];
    NSString *selImgStr = [NSString stringWithFormat:@"TabBar/%@选中", title];
    [button setImage:FMImgInBundle(imgStr) forState:UIControlStateNormal];
    [button setImage:FMImgInBundle(selImgStr) forState:UIControlStateSelected];
    
    [self addSubview:button];
    [button addTarget:self action:@selector(buttonClick:) forControlEvents:UIControlEventTouchDown];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    NSUInteger count = self.subviews.count;
    for (NSUInteger i = 0; i < count; i++) {
        FMTabBarButton *button = self.subviews[i];
        button.tag = i;
        
        // 设置frame
        CGFloat buttonY = 0;
        CGFloat buttonW = UI_SCREEN_WIDTH / count;
        CGFloat buttonH = UI_TABBAR_HEIGHT;
        CGFloat buttonX = i * buttonW;
        button.frame = CGRectMake(buttonX, buttonY, buttonW, buttonH);
    }
}

- (void)buttonClick:(UIButton *)button {
    if ([self.delegate respondsToSelector:@selector(tabBar:didSelectButtonFrom:to:)]) {
        [self.delegate tabBar:self didSelectButtonFrom:self.selectedButton.tag to:button.tag];
    }
}

- (void)setSelectedIndex:(NSInteger)selectedIndex {
    _selectedIndex = selectedIndex;

    FMTabBarButton *button = self.subviews[selectedIndex];
    self.selectedButton.selected = NO;
    button.selected = YES;
    self.selectedButton = button;
}


@end
