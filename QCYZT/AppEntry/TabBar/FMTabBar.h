//
//  FMTabBar.h
//  
//
//  Created by macPro on 2019/9/19.
//  Copyright © 2019 macPro. All rights reserved.
//

#import <UIKit/UIKit.h>
@class FMTabBar;

NS_ASSUME_NONNULL_BEGIN

@protocol FMTabBarDelegate <NSObject>

@optional
- (void)tabBar:(FMTabBar *)tabBar didSelectButtonFrom:(NSInteger)from to:(NSInteger)to;

@end

@interface FMTabBar : UIView

/**
 *  用来添加一个内部的按钮
 *
 *  @param title   标题
 */
- (void)addTabButtonWithTitle:(NSString *)title;

@property (nonatomic, weak) id<FMTabBarDelegate> delegate;

@property (nonatomic, assign) NSInteger selectedIndex;

@end

NS_ASSUME_NONNULL_END
