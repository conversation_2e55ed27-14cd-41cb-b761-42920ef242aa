//
//  UIColor+FMDarkModeChange.m
//  QCYZT
//
//  Created by zeng on 2024/12/25.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "UIColor+FMDarkModeChange.h"

@implementation UIColor (FMDarkModeChange)

// tabbar
+ (UIColor *)fm_tabbar_textColor {
    return UPTColorInModule(@"fm_tabbar_textColor", @"QCYZT");
}

+ (UIColor *)fm_tabbar_selectedTextColor {
    return UPTColorInModule(@"fm_tabbar_selectedTextColor", @"QCYZT");
}

// navigation
+ (UIColor *)fm_nav_color {
    return UPTColorInModule(@"fm_nav_color", @"QCYZT");
}

// 笔记
+ (UIColor *)fm_note_audio_borderColor {
    return UPTColorInModule(@"fm_note_audio_borderColor", @"QCYZT");
}

+ (UIColor *)fm_note_arrange_bgGradientColor1 {
    return UPTColorInModule(@"fm_note_arrange_bgGradientColor1", @"QCYZT");
}

+ (UIColor *)fm_note_arrange_bgGradientColor2 {
    return UPTColorInModule(@"fm_note_arrange_bgGradientColor2", @"QCYZT");
}

+ (UIColor *)fm_note_arrange_nameTextColor {
    return UPTColorInModule(@"fm_note_arrange_nameTextColor", @"QCYZT");
}

+ (UIColor *)fm_note_Topic_bgGradientColor1 {
    return UPTColorInModule(@"fm_note_Topic_bgGradientColor1", @"QCYZT");
}

+ (UIColor *)fm_note_Topic_bgGradientColor2 {
    return UPTColorInModule(@"fm_note_Topic_bgGradientColor2", @"QCYZT");
}

+ (UIColor *)fm_note_tag_bgColor {
    return UPTColorInModule(@"fm_note_tag_bgColor", @"QCYZT");
}

+ (UIColor *)fm_note_tag_textColor {
    return UPTColorInModule(@"fm_note_tag_textColor", @"QCYZT");
}

+ (UIColor *)fm_note_commenter_nameTextColor {
    return UPTColorInModule(@"fm_note_commenter_nameTextColor", @"QCYZT");
}

+ (UIColor *)fm_note_listBottom_numColor {
    return UPTColorInModule(@"fm_note_listBottom_numColor", @"QCYZT");
}

+ (UIColor *)fm_noteDetail_comment_bgColor {
    return UPTColorInModule(@"fm_noteDetail_comment_bgColor", @"QCYZT");
}

+ (UIColor *)fm_noteDetail_comment_textColor {
    return UPTColorInModule(@"fm_noteDetail_comment_textColor", @"QCYZT");
}

// 直播
+ (UIColor *)fm_live_chatTextFromMe_BgColor {
    return UPTColorInModule(@"fm_live_chatTextFromMe_BgColor", @"QCYZT");
}

// 个人中心
+ (UIColor *)fm_userCenter_welcomeTextColor {
    return UPTColorInModule(@"fm_userCenter_welcomeTextColor", @"QCYZT");
}

// 大咖
+ (UIColor *)fm_daka_tag_bgColor {
    return UPTColorInModule(@"fm_daka_tag_bgColor", @"QCYZT");
}

+ (UIColor *)fm_daka_tag_textColor1 {
    return UPTColorInModule(@"fm_daka_tag_textColor1", @"QCYZT");
}

+ (UIColor *)fm_daka_tag_textColor2 {
    return UPTColorInModule(@"fm_daka_tag_textColor2", @"QCYZT");
}

+ (UIColor *)fm_daka_tag_borderColor {
    return UPTColorInModule(@"fm_daka_tag_borderColor", @"QCYZT");
}

// 行情
+ (UIColor *)fm_stock_calendar_textDisabledColor {
    return UPTColorInModule(@"fm_stock_calendar_textDisabledColor", @"QCYZT");
}

// 通用
+ (UIColor *)fm_sepline_color {
    return UPTColorInModule(@"fm_sepline_color", @"QCYZT");
}

+ (UIColor *)fm_F7F7F7_2E2F33 {
    return UPTColorInModule(@"fm_F7F7F7_2E2F33", @"QCYZT");
}

+ (UIColor *)fm_F7F7F7_232426 {
    return UPTColorInModule(@"fm_F7F7F7_232426", @"QCYZT");
}

+ (UIColor *)fm_FFFFFF_2E2F33 {
    return UPTColorInModule(@"fm_FFFFFF_2E2F33", @"QCYZT");
}

+ (UIColor *)fm_BFBFBF_888888 {
    return UPTColorInModule(@"fm_BFBFBF_888888", @"QCYZT");
}

@end

