//
//  UIColor+Hexstring.h
//  QCYZT
//
//  Created by <PERSON><PERSON> on 16/5/10.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef NS_ENUM(NSUInteger, GradientDirection) {
    GradientDirectionLeftToRight,          // 从左到右
    GradientDirectionTopToBottom,          // 从上到下
    GradientDirectionTopLeftToBottomRight, // 从左上到右下
    GradientDirectionBottomLeftToTopRight, // 从左下到右上
};

@interface UIColor (Hexstring)
+ (UIColor*) colorWithHex:(NSInteger)hexValue alpha:(CGFloat)alphaValue;
+ (UIColor*) colorWithHex:(NSInteger)hexValue;
+ (NSString *) hexFromUIColor: (UIColor*) color;

// iOS中十六进制的颜色（可以以#或0x开头）转换为UIColor
+ (UIColor *) colorWithHexString: (NSString *)color;

/// 颜色之间的过渡
/// @param color1 目标颜色
/// @param color2 初始颜色
/// @param ratio 变化范围
+(UIColor *)mixColor1:(UIColor*)color1 color2:(UIColor *)color2 ratio:(CGFloat)ratio;

/**
 *  根据颜色数组生成渐变色对象
 *
 *  @param colors     颜色数组
 *  @param frame      渐变色占用的区域
 *  @param direction  渐变方向
 *
 *  @return 颜色渐变对象
 */
+ (UIColor *)lz_gradientColors:(NSArray<UIColor *> *)colors withFrame:(CGRect)frame direction:(GradientDirection)direction;

@end
