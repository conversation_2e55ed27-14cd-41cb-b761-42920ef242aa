//
//  UIButton+Init.m
//  DakaAskStock
//
//  Created by macPro on 2018/8/24.
//  Copyright © 2018年 LZKJ. All rights reserved.
//

#import "UIButton+Init.h"

@implementation UIButton (Init)

- (instancetype)initWithFrame:(CGRect)frame font:(UIFont *)font backgroundColor:(UIColor *)backgroundColor target:(id)target action:(SEL)selector {
    return [self initWithFrame:frame font:font normalTextColor:nil backgroundColor:backgroundColor title:nil image:nil target:target action:selector];
}

- (instancetype)initWithFrame:(CGRect)frame font:(UIFont *)font normalTextColor:(UIColor *)normalTextColor backgroundColor:(UIColor *)backgroundColor title:(NSString *)title image:(UIImage *)image target:(id)target action:(SEL)selector {
    self = [self initWithFrame:frame];
    self.titleLabel.font = font;
    self.titleLabel.lineBreakMode = NSLineBreakByClipping;
    [self setTitleColor:normalTextColor forState:UIControlStateNormal];
    self.backgroundColor = backgroundColor;
    if (title) {
        [self setTitle:title forState:UIControlStateNormal];
    }
    if (image) {
        [self setImage:image forState:UIControlStateNormal];
    }
    [self addTarget:target action:selector forControlEvents:UIControlEventTouchUpInside];
    
    return self;
}

//禁用高亮状态
- (void)setHighlighted:(BOOL)highlighted {
    
}
@end
