//
//  UIButton+LZEvent.m
//  QCYZT
//
//  Created by <PERSON> on 2022/8/10.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "UIButton+LZEvent.h"
#import "objc/runtime.h"
@implementation UIButton (LZEvent)

static char lz_eventBlocKey;

-(void)setLz_eventBlock:(LZBtnEventsBlock)lz_eventBlock{
    objc_setAssociatedObject(self, &lz_eventBlocKey, lz_eventBlock, OBJC_ASSOCIATION_COPY);
    [self addTarget:self action:@selector(lz_fire) forControlEvents:UIControlEventTouchUpInside];
}

-(void (^)(__kindof UIButton * _Nonnull))lz_eventBlock{
    return objc_getAssociatedObject(self, &lz_eventBlocKey);
}


-(void)lz_fire{
    if (self.lz_eventBlock) {
        self.lz_eventBlock(self);
    }
}

@end
