//
//  UIButton+TouchArea.m
//  QCYZT
//
//  Created by zeng on 2022/5/19.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "UIButton+TouchArea.h"
#import "RuntimeTool.h"

@implementation UIButton (TouchArea)

+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        [RuntimeTool swizzleClass:[self class] originalSEL:@selector(pointInside:withEvent:) withSEL:@selector(lz_pointInside:withEvent:)];
        [RuntimeTool swizzleClass:[self class] originalSEL:@selector(sendAction:to:forEvent:) withSEL:@selector(lz_sendAction:to:forEvent:)];
    });
}

// 设置按钮额外点击区域
- (void)setLz_touchAreaInsets:(UIEdgeInsets)lz_touchAreaInsets {
    NSValue *value = [NSValue valueWithUIEdgeInsets:lz_touchAreaInsets];
    objc_setAssociatedObject(self, @selector(lz_touchAreaInsets), value, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (UIEdgeInsets)lz_touchAreaInsets {
    return [objc_getAssociatedObject(self, @selector(lz_touchAreaInsets)) UIEdgeInsetsValue];
}

- (BOOL)lz_pointInside:(CGPoint)point withEvent:(UIEvent *)event {
    if (UIEdgeInsetsEqualToEdgeInsets(self.lz_touchAreaInsets, UIEdgeInsetsZero)) {
        return [self lz_pointInside:point withEvent:event];
    }
    
    CGRect hitRect = UIEdgeInsetsInsetRect(self.bounds, self.lz_touchAreaInsets);
    
    return CGRectContainsPoint(hitRect, point);
}



- (NSTimeInterval)lz_acceptEventInterval {
    return [objc_getAssociatedObject(self, "UIControl_acceptEventInterval") doubleValue];
}

- (void)setLz_acceptEventInterval:(NSTimeInterval)lz_acceptEventInterval {
    objc_setAssociatedObject(self, "UIControl_acceptEventInterval", @(lz_acceptEventInterval), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (NSTimeInterval)lz_acceptEventTime {
    return [objc_getAssociatedObject(self, "UIControl_acceptEventTime") doubleValue];
}

- (void)setLz_acceptEventTime:(NSTimeInterval)lz_acceptEventTime {
    objc_setAssociatedObject(self, "UIControl_acceptEventTime", @(lz_acceptEventTime), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)lz_sendAction:(SEL)action to:(id)target forEvent:(UIEvent *)event {
    if (NSDate.date.timeIntervalSince1970 - self.lz_acceptEventTime < self.lz_acceptEventInterval) {
        return;
    }

    if (self.lz_acceptEventInterval > 0) {
        self.lz_acceptEventTime = NSDate.date.timeIntervalSince1970;
    }

    [self lz_sendAction:action to:target forEvent:event];
}

@end
