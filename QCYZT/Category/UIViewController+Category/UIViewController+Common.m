//
//  UIViewController+Common.m
//  QCYZT
//
//  Created by MacPro on 16/8/4.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import "UIViewController+Common.h"
#import "FMAnalyticTool.h"
#import "RuntimeTool.h"
#import "FMOverLayView.h"
#import "UPViewController.h"

@interface UIViewController()


@end

@implementation UIViewController (Common)

+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        [RuntimeTool swizzleClass:[self class] originalSEL:@selector(viewWillAppear:) withSEL:@selector(zl_viewWillShow:)];
        [RuntimeTool swizzleClass:[self class] originalSEL:@selector(viewDidAppear:) withSEL:@selector(zl_viewDidShow:)];
        [RuntimeTool swizzleClass:[self class] originalSEL:@selector(viewWillDisappear:) withSEL:@selector(zl_viewWillDismiss:)];
        [RuntimeTool swizzleClass:[self class] originalSEL:@selector(setTitle:) withSEL:@selector(zl_setTitle:)];
    });
}

- (void)zl_setTitle:(NSString *)title {
    [self zl_setTitle:title];
    
    // 处理iOS16上导航栏标题可能不显示的bug
    if (self.navigationController) {
        [self.navigationController.navigationBar setNeedsLayout];
    }
}

- (void)zl_viewWillShow:(BOOL)animated {
//    FMLog(@"展示页面%@",NSStringFromClass([self class]));
    NSString *clsStr = NSStringFromClass([self class]);
    if ([clsStr isEqualToString:@"TXSSOLoginViewController"]) {
        [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
        
        [[FMHelper getCurrentVC].navigationController setNavigationBarHidden:YES animated:NO];
    } else if ([clsStr hasPrefix:@"UP"]) {
        if ([[self class] isSubclassOfClass:[UPViewController class]]) {
            self.selfNavigationBarHidden = ((UPViewController *)self).hidesNavigationBarWhenPush;
            
            [self configNavRedColor];
        }
        
        NSArray *filter = [JsonTool dicOrArrFromJsonString:[JsonTool jsonStrWithJsonName:@"UPChangeNavStatusVC"]];
        for (NSString *str in filter) {
            Class cls = NSClassFromString(str);
            if ([self isKindOfClass:cls]) {
                if ([UPThemeManager isDarkTheme]) {
                    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
                } else {
                    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
                }
                break;
            }
        }
    }

    if([self isBeingPresented] || [self isMovingToParentViewController]) {
        // push / present
        if ([self.parentViewController isKindOfClass:NSClassFromString(@"FMNavigationController")] && self.navigationController.navigationBarHidden != self.selfNavigationBarHidden) {
            [self.navigationController setNavigationBarHidden:self.selfNavigationBarHidden animated:animated];
        }
    } else {
        // pop /dismiss to here
        if ([self.parentViewController isKindOfClass:NSClassFromString(@"FMNavigationController")] && self.navigationController.navigationBarHidden != self.selfNavigationBarHidden) {
            [self.navigationController setNavigationBarHidden:self.selfNavigationBarHidden animated:animated];
        }
    }
    
    [self zl_viewWillShow:animated];
}

- (void)zl_viewDidShow:(BOOL)animated {
    self.showDate = [NSDate date];
    
    // 只有是导航控制器的子控制器时，才去判断是否显示置灰。 防止自选这种页面下FMSelfStocksViewController控制器进入判断
    if ([FMHelper getCurrentVC] == self) {
        [self judgeGrayMaskShow];
    }
    
    
    [self zl_viewDidShow:animated];
}

- (void)zl_viewWillDismiss:(BOOL)animated {
    if (!self.showDate) {
        return;
    }
    
    NSDate *now = [NSDate date];
    long long openTime = [now timeIntervalSince1970] - [self.showDate timeIntervalSince1970];
    [FMUserDefault setSeting:@"lastViewController" Value:NSStringFromClass([self class])];
    
    NSString *clsStr = NSStringFromClass([self class]);
    if ([clsStr isEqualToString:@"TXSSOLoginViewController"]) {
        [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
        [[FMHelper getCurrentVC].navigationController setNavigationBarHidden:NO animated:NO];
    } else if ([clsStr hasPrefix:@"UP"]) {
        NSArray *filter = [JsonTool dicOrArrFromJsonString:[JsonTool jsonStrWithJsonName:@"UPChangeNavStatusVC"]];
        for (NSString *str in filter) {
            Class cls = NSClassFromString(str);
            if ([self isKindOfClass:cls]) {
                [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
                break;
            }
        }

    }

    [self zl_viewWillDismiss:animated];
}

//- (NSArray<NSString *> *)getAllParentViewControllers:(UIViewController *)viewController {
//    NSMutableArray<NSString *> *parentViewControllers = [NSMutableArray array];
//    
//    UIViewController *parentViewController = viewController.parentViewController;
//    
//    while (parentViewController != nil) {
//        [parentViewControllers addObject:NSStringFromClass(parentViewController.class)];
//        parentViewController = parentViewController.parentViewController;
//    }
//    
//    return [parentViewControllers copy];
//}

#pragma mark - 分类添加属性
- (void)setShowDate:(NSDate *)showDate {
    objc_setAssociatedObject(self, @selector(showDate), showDate, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (NSDate *)showDate {
    return objc_getAssociatedObject(self, @selector(showDate));
}

- (void)setSelfNavigationBarHidden:(BOOL)selfNavigationBarHidden {
    objc_setAssociatedObject(self, @selector(selfNavigationBarHidden), @(selfNavigationBarHidden), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (BOOL)selfNavigationBarHidden {
    return [objc_getAssociatedObject(self, @selector(selfNavigationBarHidden)) boolValue];
}


/*
  如果bgColor是fm_nav_color，表示导航色底色是亮红#FC0002暗黑#232426，文字和按钮应为fm_market_nav_text_color，亮白#FFFFFF暗灰#D8D8D8
  如果bgColor是up_contentBgColor，表示导航色是亮白#FFFFFF暗黑#232426，文字和按钮应为fm_market_nav_text_zeroColor,亮黑#000000暗灰#D8D8D8
 */
- (void)configNavWithBgColor:(UIColor *)bgColor statusBarStyle:(UIStatusBarStyle)statusBarStyle titleColor:(UIColor *)titleColor tintColor:(UIColor *)tintColor closeSEL:(SEL)closeSEL {
    if (@available(iOS 15.0, *)) {
        UINavigationBarAppearance *appearance = [UINavigationBarAppearance new];
        [appearance configureWithOpaqueBackground];
        appearance.backgroundColor = bgColor;
        appearance.backgroundEffect = nil;
        appearance.shadowColor = FMClearColor;
        appearance.titleTextAttributes = @{NSForegroundColorAttributeName:titleColor,NSFontAttributeName : FontWithSize(20)};
        self.navigationController.navigationBar.standardAppearance = appearance;
        self.navigationController.navigationBar.scrollEdgeAppearance = appearance;
    } else {
        self.navigationController.navigationBar.bgColor = bgColor;
        [self.navigationController.navigationBar setTitleTextAttributes:@{NSForegroundColorAttributeName:titleColor,NSFontAttributeName : FontWithSize(20)}];
    }

    self.navigationController.navigationBar.tintColor = tintColor;
    [UIApplication sharedApplication].statusBarStyle = statusBarStyle;

    if (closeSEL) {
        UIImage *back;
        if ([bgColor isKindOfClass:[UPThemeColor class]]) {
            UPThemeColor *themeColor = (UPThemeColor *)bgColor;
            if ([themeColor.colorName isEqualToString:@"up_common_content_bg_color"]) {
                back = FMImgInBundle(@"导航/亮黑暗灰返回");
            } else {
                back = FMImgInBundle(@"导航/亮白暗灰返回");
            }
        } else {
            back = [UIImage imageWithTintColor:tintColor blendMode:kCGBlendModeDestinationIn WithImageObject:[UIImage imageNamed:@"return"]];
        }
        UIBarButtonItem *backItem = [[UIBarButtonItem alloc] initWithImage:[back imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal] style:UIBarButtonItemStylePlain target:self action:closeSEL];
        backItem.imageInsets = UIEdgeInsetsMake(0, 0, 0, 0);
        self.navigationItem.leftBarButtonItem = backItem;
    }
}

- (void)configNavWhiteColorWithCloseSEL:(SEL)closeSEL {
    [self configNavWithBgColor:UIColor.up_contentBgColor statusBarStyle:UIStatusBarStyleDefault titleColor:UIColor.fm_market_nav_text_zeroColor tintColor:UIColor.fm_market_nav_text_zeroColor closeSEL:closeSEL];
}

- (void)configNavRedColor {
    [self configNavWithBgColor:UIColor.fm_nav_color statusBarStyle:UIStatusBarStyleLightContent titleColor:UIColor.fm_market_nav_text_color tintColor:UIColor.fm_market_nav_text_color closeSEL:nil];
}

// 是否是一级页面，过滤掉StockViewController
- (BOOL)isFirstLevelVC {
    for (FMNavigationController *nav in [FMAppDelegate shareApp].main.viewControllers) {
        UIViewController *vc = nav.viewControllers.firstObject;
        if (vc == self && ![self isKindOfClass:NSClassFromString(@"StockViewController")]) {
            return YES;
        }
    }
    
    return NO;
}

// 置灰
- (void)judgeGrayMaskShow {
    NSDictionary *initDic = [[NSUserDefaults standardUserDefaults] objectForKey:AppInit_Key];
    if ([initDic[@"greyTheme"] boolValue] && [self isFirstLevelVC]) { // 置灰打开，并且是第一层级页面
        FMOverLayView *overlay = [[FMOverLayView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT)];
   //    滤镜的背景颜色，一些鲜艳的颜色设置后会无效
       overlay.backgroundColor = [UIColor lightGrayColor];
       overlay.layer.compositingFilter = @"saturationBlendMode";
   //    设置图层在最上面
       overlay.layer.zPosition = FLT_MAX;
        
        BOOL isAdd = NO;
        NSArray *windows = [UIApplication sharedApplication].windows;
        for (UIWindow *window in windows) {
            for (UIView *view in window.subviews) {
                if ([view isKindOfClass:[FMOverLayView class]]) {
                    isAdd = YES;
                }
            }
        }
        
        if (!isAdd) {
            for (UIWindow *window in windows) {
                if ([NSStringFromClass([window.rootViewController class]) isEqualToString:@"FMMainTabController"]) {
                    [window addSubview:overlay];
                }
            }
        }
    } else {
        if ([self isKindOfClass:NSClassFromString(@"FMPublishAlertViewController")] ||
            [self isKindOfClass:NSClassFromString(@"FMGuideViewController")]) {
            return;
        }
        NSArray *windows = [UIApplication sharedApplication].windows;
        for (UIWindow *window in windows) {
            for (UIView *view in window.subviews) {
                if ([view isKindOfClass:[FMOverLayView class]]) {
                    [view removeFromSuperview];
                }
            }
        }
    }
}

@end



