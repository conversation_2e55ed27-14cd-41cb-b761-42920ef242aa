//
//  UIViewController+Common.h
//  QCYZT
//
//  Created by MacPro on 16/8/4.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface UIViewController (Common)

@property (nonatomic, strong) NSDate *showDate;

/// 本页面设置的navigationBarHidden状态
/// 在viewWillAppear里调用super方法前设置可以修改当前页面导航栏显示状态
/// 如果在其它地方设置，需要调用setNavigationBarHidden:animated:方法同步修改导航栏显示状态
@property (nonatomic, assign) BOOL selfNavigationBarHidden;


- (void)configNavWithBgColor:(UIColor *)bgColor statusBarStyle:(UIStatusBarStyle)statusBarStyle titleColor:(UIColor *)titleColor tintColor:(UIColor *)tintColor closeSEL:(SEL)closeSEL;

- (void)configNavRedColor;

// 设置导航栏为白色
- (void)configNavWhiteColorWithCloseSEL:(SEL)closeSEL;

// 页面置灰
- (void)judgeGrayMaskShow;

@end
