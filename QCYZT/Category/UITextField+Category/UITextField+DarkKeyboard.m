//
//  UITextField+DarkKeyboard.m
//  QCYZT
//
//  Created by Cursor on 2024-12-26
//  Copyright © 2024 Cursor. All rights reserved.
//

#import "UITextField+DarkKeyboard.h"
#import <objc/runtime.h>

// 键盘外观类型定义
typedef NS_ENUM(NSInteger, FMKeyboardAppearanceType) {
    FMKeyboardAppearanceTypeDefault,  // 默认（亮色）
    FMKeyboardAppearanceTypeDark      // 暗色
};

@implementation UITextField (DarkKeyboard)

#pragma mark - 生命周期

// 在类加载时自动调用
+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        [self enableDarkKeyboardAdaption];
    });
}

#pragma mark - 公共方法

+ (void)enableDarkKeyboardAdaption {
    // 交换becomeFirstResponder方法
    Method originalMethod = class_getInstanceMethod(self, @selector(becomeFirstResponder));
    Method swizzledMethod = class_getInstanceMethod(self, @selector(fm_becomeFirstResponder));
    
    if (originalMethod && swizzledMethod) {
        method_exchangeImplementations(originalMethod, swizzledMethod);
    }
    
    // 交换awakeFromNib方法
    originalMethod = class_getInstanceMethod(self, @selector(awakeFromNib));
    swizzledMethod = class_getInstanceMethod(self, @selector(fm_awakeFromNib));
    
    if (originalMethod && swizzledMethod) {
        method_exchangeImplementations(originalMethod, swizzledMethod);
    }
    
    // 交换initWithFrame:方法
    originalMethod = class_getInstanceMethod(self, @selector(initWithFrame:));
    swizzledMethod = class_getInstanceMethod(self, @selector(fm_initWithFrame:));
    
    if (originalMethod && swizzledMethod) {
        method_exchangeImplementations(originalMethod, swizzledMethod);
    }
    
    // 注册主题变化通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(themeDidChange:)
                                                 name:kUPThemeDidChangeNotification
                                               object:nil];
    
    // 注册系统外观变化通知（iOS 13+）
//    if (@available(iOS 13.0, *)) {
//        [[NSNotificationCenter defaultCenter] addObserver:self
//                                                 selector:@selector(systemThemeDidChange:)
//                                                     name:UITraitCollectionDidChangeNotification
//                                                   object:nil];
//    }
}

#pragma mark - 方法交换实现

// 交换后的becomeFirstResponder方法
- (BOOL)fm_becomeFirstResponder {
    // 在成为第一响应者前更新键盘外观
    [self updateKeyboardAppearance];
    
    // 调用原始方法
    return [self fm_becomeFirstResponder];
}

// 交换后的awakeFromNib方法
//- (void)fm_awakeFromNib {
//    // 调用原始方法
//    [self fm_awakeFromNib];
//    
//    // 初始化时设置键盘外观
//    [self updateKeyboardAppearance];
//}

// 交换后的initWithFrame:方法
- (instancetype)fm_initWithFrame:(CGRect)frame {
    // 调用原始方法
    UITextField *textField = [self fm_initWithFrame:frame];
    
    // 初始化时设置键盘外观
    [textField updateKeyboardAppearance];
    
    return textField;
}

#pragma mark - 通知处理

// 处理UP主题变化通知
+ (void)themeDidChange:(NSNotification *)notification {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self updateAllTextFieldsKeyboardAppearance];
    });
}

// 处理系统外观变化通知
+ (void)systemThemeDidChange:(NSNotification *)notification API_AVAILABLE(ios(13.0)) {
    // 只有在跟随系统模式下才响应系统主题变化
    NSString *darkModelSetting = [FMUserDefault getUnArchiverDataForKey:DarkModeSetting];
    if (darkModelSetting.length > 0 && [darkModelSetting integerValue] == 0) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self updateAllTextFieldsKeyboardAppearance];
        });
    }
}

#pragma mark - 私有方法

// 更新所有可见的UITextField的键盘外观
+ (void)updateAllTextFieldsKeyboardAppearance {
    // 获取当前键盘外观类型
    FMKeyboardAppearanceType appearanceType = [self getCurrentKeyboardAppearanceType];
    
    // 遍历所有窗口
    for (UIWindow *window in [UIApplication sharedApplication].windows) {
        // 递归查找所有UITextField
        [self updateTextFieldsInView:window withAppearanceType:appearanceType];
    }
}

// 递归查找视图层次中的所有UITextField并更新键盘外观
+ (void)updateTextFieldsInView:(UIView *)view withAppearanceType:(FMKeyboardAppearanceType)appearanceType {
    // 如果当前视图是UITextField，更新其键盘外观
    if ([view isKindOfClass:[UITextField class]]) {
        UITextField *textField = (UITextField *)view;
        
        // 设置键盘外观
        UIKeyboardAppearance appearance = (appearanceType == FMKeyboardAppearanceTypeDark) ? 
                                          UIKeyboardAppearanceDark : 
                                          UIKeyboardAppearanceDefault;
        
        // 只有当键盘外观不同时才更新，避免不必要的重绘
        if (textField.keyboardAppearance != appearance) {
            textField.keyboardAppearance = appearance;
            
            // 如果正在编辑，需要重新弹出键盘以应用新外观
            if (textField.isFirstResponder) {
                [textField resignFirstResponder];
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [textField becomeFirstResponder];
                });
            }
        }
    }
    
    // 递归处理所有子视图
    for (UIView *subview in view.subviews) {
        [self updateTextFieldsInView:subview withAppearanceType:appearanceType];
    }
}

// 获取当前应该使用的键盘外观类型
+ (FMKeyboardAppearanceType)getCurrentKeyboardAppearanceType {
    // 获取当前暗黑模式设置
    NSString *darkModelSetting = [FMUserDefault getUnArchiverDataForKey:DarkModeSetting];
    
    if (darkModelSetting.length > 0) {
        NSInteger index = [darkModelSetting integerValue];
        if (index == 0) { // 跟随系统
            if (@available(iOS 13.0, *)) {
                UIUserInterfaceStyle style = [UIScreen mainScreen].traitCollection.userInterfaceStyle;
                return (style == UIUserInterfaceStyleDark) ? FMKeyboardAppearanceTypeDark : FMKeyboardAppearanceTypeDefault;
            } else {
                return FMKeyboardAppearanceTypeDefault;
            }
        } else if (index == 1) { // 白天模式
            return FMKeyboardAppearanceTypeDefault;
        } else { // 夜间模式
            return FMKeyboardAppearanceTypeDark;
        }
    } else {
        // 默认使用白天模式
        return FMKeyboardAppearanceTypeDefault;
    }
}

// 实例方法：更新当前TextField的键盘外观
- (void)updateKeyboardAppearance {
    // 获取当前键盘外观类型
    FMKeyboardAppearanceType appearanceType = [[self class] getCurrentKeyboardAppearanceType];
    
    // 设置键盘外观
    UIKeyboardAppearance appearance = (appearanceType == FMKeyboardAppearanceTypeDark) ? 
                                      UIKeyboardAppearanceDark : 
                                      UIKeyboardAppearanceDefault;
    
    // 只有当键盘外观不同时才更新，避免不必要的重绘
    if (self.keyboardAppearance != appearance) {
        self.keyboardAppearance = appearance;
    }
}

@end 
