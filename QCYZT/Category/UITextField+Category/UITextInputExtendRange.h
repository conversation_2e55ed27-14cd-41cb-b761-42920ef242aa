//
//  UITextInputExtendRange.h
//  QCYZT
//
//  Created by 石大千 on 16/3/8.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol UITextInputExtendRange <UITextInput>

- (NSRange)selectedRange;
- (void) setSelectedRange:(NSRange)range;

@property (nonatomic,copy) NSString *text;

@property (nonatomic,retain) UIView *inputView;
@property (nonatomic,retain) UIView *inputAccessoryView;

@end
