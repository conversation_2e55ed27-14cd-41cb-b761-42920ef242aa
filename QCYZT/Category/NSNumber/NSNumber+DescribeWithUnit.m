//
//  NSNumber+DescribeWithUnit.m
//  QCYZT
//
//  Created by Max on 2022/8/10.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "NSNumber+DescribeWithUnit.h"

@implementation NSNumber (DescribeWithUnit)
-(NSString *)describeWithUnit:(DescribeUnit)unit{
    NSInteger value = [self integerValue];
    if (unit == DescribeUnitTenThousand) {
        float divideNum = 10000.0;
        if (value > divideNum) {
            return [NSString stringWithFormat:@"%.1f万",value / divideNum];
        } else {
            return [NSString stringWithFormat:@"%ld",value];
        }
    } else if (unit == DescribeUnitThousand) {
        float divideNum = 1000.0;
        if (value > divideNum) {
            return [NSString stringWithFormat:@"%.1f千",value / divideNum];
        } else {
            return [NSString stringWithFormat:@"%ld",value];
        }
    } else if (unit == (DescribeUnitThousand | DescribeUnitTenThousand)){
        NSAssert(false, @"还未实现");
        return @"几万几千";
    }
    return @"";
}
@end
