//
//  NSDate+String.h
//  QCYZT
//
//  Created by <PERSON> on 2019/2/22.
//  Copyright © 2019 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSDate (String)

/**
 将已经格式化的字符串转换为format格式的NSDate
 
 @param formatedStr 已经格式化的字符串，比如
 @param format 时间格式，比如yyyyMMdd等
 @return 返回格式化的NSDate
 */
+ (NSDate *)dateFromFormatedString:(NSString *)formatedStr format:(NSString *)format;

+ (NSTimeInterval)getNowTimeStamp:(NSDate *)date;
@end

NS_ASSUME_NONNULL_END
