//
//  UIColletionViewLateralArrangeHorizontalScrollLayout.h
//  FQYFNative
//
//  Created by macPro on 2020/4/29.
//  Copyright © 2020 macPro. All rights reserved.
//  横向排列水平滚动的layout

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIColletionViewLateralArrangeHorizontalScrollLayout : UICollectionViewFlowLayout

// 一行中cell的个数
@property (nonatomic) NSUInteger itemCountPerRow;
// 一页显示多少行
@property (nonatomic) NSUInteger rowCount;


@end

NS_ASSUME_NONNULL_END
