//
//  UIColletionViewLateralArrangeHorizontalScrollLayout.m
//  FQYFNative
//
//  Created by macPro on 2020/4/29.
//  Copyright © 2020 macPro. All rights reserved.
//

#import "UIColletionViewLateralArrangeHorizontalScrollLayout.h"

@interface UIColletionViewLateralArrangeHorizontalScrollLayout()

@property (strong, nonatomic) NSMutableArray *allAttributes;

@end


@implementation UIColletionViewLateralArrangeHorizontalScrollLayout

- (void)prepareLayout {
    [super prepareLayout];
    
    [self.allAttributes removeAllObjects];
    
    NSUInteger count = [self.collectionView numberOfItemsInSection:0];
    // 一页会有几个cell
    NSInteger countInOnePage = self.rowCount * self.itemCountPerRow;
    for (NSUInteger i = 0; i < count; i++) {
        // 当前处于第几页
        NSInteger currentPage = i / countInOnePage;
        CGSize size = self.itemSize;

        UICollectionViewLayoutAttributes *attributes = [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:[NSIndexPath indexPathForItem:i inSection:0]];
        NSInteger row = i / self.itemCountPerRow - currentPage * self.rowCount; // 处于当前页的第几行
        CGFloat frameX = size.width * (i % self.itemCountPerRow) + currentPage * (size.width * self.itemCountPerRow);
        CGFloat frameY = size.height * row;
        attributes.frame = CGRectMake(frameX, frameY, size.width, size.height);
        [self.allAttributes addObject:attributes];
    }
}

- (CGSize)collectionViewContentSize {
    NSUInteger count = [self.collectionView numberOfItemsInSection:0];
    NSInteger countInOnePage = self.rowCount * self.itemCountPerRow;
    if (count <= countInOnePage) {
        return [super collectionViewContentSize];
    } else {
        NSInteger pages = (count + countInOnePage  - 1) / (countInOnePage);
        float lastPageSize = 0;
        if (count - (pages - 1) * countInOnePage < self.itemCountPerRow) {
            lastPageSize = (count - (pages - 1) * countInOnePage) * self.itemSize.width;
        } else {
            lastPageSize = self.itemSize.width * self.itemCountPerRow;
        }
        CGSize size = CGSizeMake(self.itemCountPerRow * self.itemSize.width * (pages-1) + lastPageSize, self.rowCount * self.itemSize.height);
        return size;
    }
}


- (NSArray<UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect {
    return self.allAttributes;
}

- (NSMutableArray *)allAttributes {
    if (!_allAttributes) {
        _allAttributes = [NSMutableArray array];
    }
    
    return _allAttributes;
}


@end
