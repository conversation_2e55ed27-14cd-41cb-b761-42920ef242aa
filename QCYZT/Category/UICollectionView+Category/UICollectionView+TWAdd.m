//
//  UICollectionView+TWAdd.m
//  QCYZT
//
//  Created by 涂威 on 2018/1/29.
//  Copyright © 2018年 sdcf. All rights reserved.
//

#import "UICollectionView+TWAdd.h"

@implementation UICollectionView (TWAdd)

- (instancetype)initWithFrame:(CGRect)frame
              scrollDirection:(UICollectionViewScrollDirection)scrollDirection
      minimumInteritemSpacing:(CGFloat)minimumInteritemSpacing
           minimumLineSpacing:(CGFloat)minimumLineSpacing
                     itemSize:(CGSize)itemSize
                 sectionInset:(UIEdgeInsets)sectionInset
           flowLayoutDeleaget:(id<UICollectionViewDelegateFlowLayout>)flowLayoutDeleaget
                   dataSource:(id<UICollectionViewDataSource>)dataSource
               viewController:(UIViewController *)viewController {
    return [self initWithFrame:frame
                viewFlowLayout:nil
               scrollDirection:scrollDirection
       minimumInteritemSpacing:minimumInteritemSpacing
            minimumLineSpacing:minimumLineSpacing
                      itemSize:itemSize
                  sectionInset:sectionInset
            flowLayoutDeleaget:flowLayoutDeleaget
                    dataSource:dataSource
                viewController:viewController
                  headerTarget:nil
                  headerAction:nil
                  footerTarget:nil
                  footerAction:nil];
}

- (instancetype)initWithFrame:(CGRect)frame viewFlowLayout:(UICollectionViewFlowLayout *)layout scrollDirection:(UICollectionViewScrollDirection)scrollDirection minimumInteritemSpacing:(CGFloat)minimumInteritemSpacing minimumLineSpacing:(CGFloat)minimumLineSpacing itemSize:(CGSize)itemSize sectionInset:(UIEdgeInsets)sectionInset flowLayoutDeleaget:(id<UICollectionViewDelegateFlowLayout>)flowLayoutDeleaget dataSource:(id<UICollectionViewDataSource>)dataSource viewController:(UIViewController *)viewController{
    return [self initWithFrame:frame
                viewFlowLayout:layout
                          scrollDirection:scrollDirection
                  minimumInteritemSpacing:minimumInteritemSpacing
                       minimumLineSpacing:minimumLineSpacing
                                 itemSize:itemSize
                             sectionInset:sectionInset
                       flowLayoutDeleaget:flowLayoutDeleaget
                               dataSource:dataSource
                           viewController:viewController
                             headerTarget:nil
                             headerAction:nil
                             footerTarget:nil
                             footerAction:nil];
}

- (instancetype)initWithFrame:(CGRect)frame
              scrollDirection:(UICollectionViewScrollDirection)scrollDirection
      minimumInteritemSpacing:(CGFloat)minimumInteritemSpacing
           minimumLineSpacing:(CGFloat)minimumLineSpacing
                     itemSize:(CGSize)itemSize
                 sectionInset:(UIEdgeInsets)sectionInset
           flowLayoutDeleaget:(id<UICollectionViewDelegateFlowLayout>)flowLayoutDeleaget
                   dataSource:(id<UICollectionViewDataSource>)dataSource
               viewController:(UIViewController *)viewController
                 headerTarget:(id)headerTarget
                 headerAction:(SEL)headerAction {
    return [self initWithFrame:frame
                viewFlowLayout:nil
               scrollDirection:scrollDirection
       minimumInteritemSpacing:minimumInteritemSpacing
            minimumLineSpacing:minimumLineSpacing
                      itemSize:itemSize
                  sectionInset:sectionInset
            flowLayoutDeleaget:flowLayoutDeleaget
                    dataSource:dataSource
                viewController:viewController
                  headerTarget:headerTarget
                  headerAction:headerAction
                  footerTarget:nil
                  footerAction:nil];
}

- (instancetype)initWithFrame:(CGRect)frame
              scrollDirection:(UICollectionViewScrollDirection)scrollDirection
      minimumInteritemSpacing:(CGFloat)minimumInteritemSpacing
           minimumLineSpacing:(CGFloat)minimumLineSpacing
                     itemSize:(CGSize)itemSize
                 sectionInset:(UIEdgeInsets)sectionInset
           flowLayoutDeleaget:(id<UICollectionViewDelegateFlowLayout>)flowLayoutDeleaget
                   dataSource:(id<UICollectionViewDataSource>)dataSource
               viewController:(UIViewController *)viewController
                 footerTarget:(id)footerTarget
                 footerAction:(SEL)footerAction {
    return [self initWithFrame:frame
                viewFlowLayout:nil
               scrollDirection:scrollDirection
       minimumInteritemSpacing:minimumInteritemSpacing
            minimumLineSpacing:minimumLineSpacing
                      itemSize:itemSize
                  sectionInset:sectionInset
            flowLayoutDeleaget:flowLayoutDeleaget
                    dataSource:dataSource
                viewController:viewController
                  headerTarget:nil
                  headerAction:nil
                  footerTarget:footerTarget
                  footerAction:footerAction];
}

- (instancetype)initWithFrame:(CGRect)frame
               viewFlowLayout:(UICollectionViewFlowLayout *)layout
              scrollDirection:(UICollectionViewScrollDirection)scrollDirection
      minimumInteritemSpacing:(CGFloat)minimumInteritemSpacing
           minimumLineSpacing:(CGFloat)minimumLineSpacing
                     itemSize:(CGSize)itemSize
                 sectionInset:(UIEdgeInsets)sectionInset
           flowLayoutDeleaget:(id<UICollectionViewDelegateFlowLayout>)flowLayoutDeleaget
                   dataSource:(id<UICollectionViewDataSource>)dataSource
               viewController:(UIViewController *)viewController
                 headerTarget:(id)headerTarget
                 headerAction:(SEL)headerAction
                 footerTarget:(id)footerTarget
                 footerAction:(SEL)footerAction {
    if (!layout) {
        layout = [[UICollectionViewFlowLayout alloc] init];
    }
    layout.scrollDirection         = scrollDirection;
    layout.minimumLineSpacing      = minimumLineSpacing;
    layout.minimumInteritemSpacing = minimumInteritemSpacing;
    layout.itemSize                = itemSize;
    layout.sectionInset            = sectionInset;
    self = [self initWithFrame:frame collectionViewLayout:layout];
    self.backgroundColor = [UIColor clearColor];
    self.delegate        = flowLayoutDeleaget;
    self.dataSource      = dataSource;
    if (headerTarget) {
        self.mj_header = [MJRefreshNormalHeader headerWithRefreshingTarget:headerTarget refreshingAction:headerAction];
    }
    if (footerTarget) {
        MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:footerTarget refreshingAction:footerAction];
        footer.stateLabel.textColor = ColorWithHex(0xbfbfbf);
        self.mj_footer = footer;
    }
    return self;
}

- (void)registerCellClass:(Class)cellClass {
    [self registerClass:cellClass forCellWithReuseIdentifier:NSStringFromClass(cellClass)];
}

- (void)registerCellClassArray:(NSArray *)classArray {
    for (Class class in classArray) {
        [self registerCellClass:class];
    }
}

- (void)registerViewClass:(Class)viewClass viewOfKind:(NSString *)viewKind {
    [self registerClass:viewClass forSupplementaryViewOfKind:viewKind  withReuseIdentifier:NSStringFromClass(viewClass)];
}

- (void)registerViewClassArray:(NSArray *)classArray viewOfKind:(NSString *)viewKind {
    for (Class class in classArray) {
        [self registerViewClass:class viewOfKind:viewKind];
    }
}

- (__kindof UICollectionViewCell *)reuseCellClass:(Class)cellClass indexPath:(NSIndexPath *)indexPath {
    return [self dequeueReusableCellWithReuseIdentifier:NSStringFromClass(cellClass) forIndexPath:indexPath];
}

- (__kindof UICollectionReusableView *)reuseViewClass:(Class)viewClass viewOfKind:(NSString *)viewKind indexPath:(NSIndexPath *)indexPath {
    return [self dequeueReusableSupplementaryViewOfKind:viewKind withReuseIdentifier:NSStringFromClass(viewClass) forIndexPath:indexPath];
}

@end

