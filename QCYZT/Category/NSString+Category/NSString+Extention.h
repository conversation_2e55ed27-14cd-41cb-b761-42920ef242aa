//
//  NSString+Extention.h
//  QCYZT
//
//  Created by leo on 16/8/8.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface NSString (Extention)
- (CGSize)sizeWithFont:(UIFont *)font andSize:(CGSize)size;

- (CGSize)sizeWithAttr:(NSDictionary *)attr andMaxSize:(CGSize)maxSize;

// 获取行数，这个方法不咋准
- (NSArray *)getlinesArrayWithFont:(UIFont *)font width:(CGFloat)width;

@end
