//
//  NSString+characterJudge.m
//  QCYZT
//
//  Created by MacPro on 16/8/8.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import "NSString+characterJudge.h"

@implementation NSString (characterJudge)

- (BOOL)isBlankString {
    if (self == nil || self == NULL) {
        return YES;
    }
    if ([self isKindOfClass:[NSNull class]]) {
        return YES;
    }
    if ([[self stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceCharacterSet]] length]==0) {
        return YES;
    }
    
    return NO;
}

- (BOOL)isPureNumber  {
    NSString *regex = @"^[0-9]+$";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    if ([predicate evaluateWithObject:self]) {
        return YES;
    }
    return NO;
}

- (BOOL)isPositiveNumber {
    // 正则表达式匹配正数：可以有小数点，但必须是正数
    NSString *positiveRegex = @"^\\d+(\\.\\d+)?$";
    
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", positiveRegex];
    return [predicate evaluateWithObject:self];
}

- (BOOL)isPureNumberAlphabetAndChinese {
    NSString *regex = @"^[A-Za-z0-9\u4e00-\u9fa5]+$";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    if ([predicate evaluateWithObject:self]) {
        return YES;
    }
    return NO;
}

- (BOOL)isPureNumberAndAlphabet {
    NSString *regex = @"^[A-Za-z0-9]+$";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    if ([predicate evaluateWithObject:self]) {
        return YES;
    }
    return NO;
}

/// 是否包含中文
- (BOOL)isincludeChinese {
    for(int i = 0; i < [self length]; i ++) {
        int a = [self characterAtIndex:i];
        if( a > 0x4e00 && a < 0x9fff){
            return YES;
        }
    }
    return NO;
}

- (BOOL)isPureNumberOrAlphabetOrChinese {
    NSString *regex1 = @".*?[\u4e00-\u9fa5]+.*?";
    NSPredicate *predicate1 = [NSPredicate predicateWithFormat:@"SELF matches %@", regex1];
    NSString *regex2 = @".*?[A-Za-z]+.*?";
    NSPredicate *predicate2 = [NSPredicate predicateWithFormat:@"SELF matches %@", regex2];
    NSString *regex3 = @".*?[A-Za-z0-9]+.*?";
    NSPredicate *predicate3 = [NSPredicate predicateWithFormat:@"SELF matches %@", regex3];
    return [predicate1 evaluateWithObject:self] || [predicate2 evaluateWithObject:self] || [predicate3 evaluateWithObject:self];
}

- (BOOL)isPureAlphabet {
    NSString *regex = @"^[A-Za-z]+$";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
    if ([predicate evaluateWithObject:self]) {
        return YES;
    }
    return NO;
}


- (BOOL)isPureChinese {
    NSString *regex = @"(^[\u4e00-\u9fa5]+$)";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF matches %@", regex];
    return [predicate evaluateWithObject:self];
}

- (BOOL)isPhoneNum {
//    NSString *regex = @"^(0|86|17951)?(13[0-9]|15[012356789]|16[6]|19[089]|17[01345678]|18[0-9]|14[579])[0-9]{8}$";
    NSString *regex = @"^(0|86|17951)?1[0-9]{10}";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF matches %@", regex];
    return [predicate evaluateWithObject:self];
}

- (BOOL)isQQ {
    NSString *regex = @"^[1-9]\\d{4,11}$";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF matches %@", regex];
    return [predicate evaluateWithObject:self];
}

- (BOOL)isMailAddress  {
    NSString *regex = @"^([a-zA-Z0-9]*[-_]?[a-zA-Z0-9]+)*@([a-zA-Z0-9]*[-_]?[a-zA-Z0-9]+)+[\\.][A-Za-z]{2,3}([\\.][A-Za-z]{2})?$";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF matches %@", regex];
    return [predicate evaluateWithObject:self];
}

- (BOOL)isIdCardNumber {
    return [self validateIdNumberPattern] && [self validateIdNumberProvince] && [self validateIdNumberBirthday] && [self validateIdNumberCheckNumber];
}

/*  二代身份证格式校验，总共18位
 6位地址码           d{6}，
 前2位年(18-20)       (18|19|20)，
 后2位年(00-99)       d{2}，
 2位月(01-12)       (0[1-9]|1[0-2])，
 2位日(01-31)       (0[1-9]|[12]\\d|3[01])，
 3位顺序码           d{3}，
 1位校验码           d|[xX]
 */
- (BOOL)validateIdNumberPattern {
    NSString *regex = @"\\d{6}(18|19|20)\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|[xX])";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF matches %@", regex];
    return [predicate evaluateWithObject:self];
}

// 判断省份
- (BOOL)validateIdNumberProvince {
    NSDictionary *aProvince = @{@"11": @"北京", @"12": @"天津", @"13": @"河北", @"14": @"山西", @"15": @"内蒙古", @"21": @"辽宁", @"22": @"吉林", @"23": @"黑龙江", @"31": @"上海", @"32": @"江苏", @"33": @"浙江", @"34": @"安徽", @"35": @"福建", @"36": @"江西", @"37": @"山东", @"41": @"河南", @"42": @"湖北", @"43": @"湖南", @"44": @"广东", @"45": @"广西", @"46": @"海南", @"50": @"重庆", @"51": @"四川", @"52": @"贵州", @"53": @"云南", @"54": @"西藏", @"61": @"陕西", @"62": @"甘肃", @"63": @"青海", @"64": @"宁夏", @"65": @"新疆", @"71": @"台湾", @"81": @"香港", @"82": @"澳门", @"91": @"国外"};
    NSString *key = [self substringToIndex:2];
    NSString *value = [aProvince objectForKey:key];
    if(!value){
        FMLog(@"你的身份证地区非法");
        return NO;
    } else {
        return YES;
    }
}

// 判断生日
- (BOOL)validateIdNumberBirthday {
    NSString *birthDay = [self substringWithRange:NSMakeRange(6, 8)];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    //创建一个日期格式化器
    dateFormatter.dateFormat=@"yyyyMMdd";
    NSDate *date = [dateFormatter dateFromString:birthDay];
    if(!date){
        FMLog(@"身份证上的出生日期非法");
        return NO;
    } else {
        return YES;
    }
}

// 校验位
- (BOOL)validateIdNumberCheckNumber {
    NSArray *idCardWi = @[@"7", @"9", @"10", @"5", @"8", @"4", @"2", @"1", @"6", @"3", @"7", @"9", @"10", @"5", @"8", @"4", @"2"];      // 将前17位加权因子保存在数组里
    NSArray *idCardA1 = @[@"1", @"0", @"10", @"9", @"8", @"7", @"6", @"5", @"4", @"3", @"2"];      // 这是除以11后，可能产生的11位余数、验证码，也保存成数组
    int idCardWiSum = 0; // 用来保存前17位各自乖以加权因子后的总和
    for (int i = 0; i < 17; i++) {
        idCardWiSum += [[self substringWithRange:NSMakeRange(i,1)] intValue] * [idCardWi[i] intValue];
    }
    int idCardMod = idCardWiSum % 11;// 计算出校验码所在数组的位置
    NSString *idCardLast = [self substringWithRange:NSMakeRange(17,1)];//得到最后一位身份证号码
    //如果等于2，则说明校验码是10，身份证号码最后一位应该是X
    if (idCardMod == 2) {
        if ([idCardLast isEqualToString:@"X"] || [idCardLast isEqualToString:@"x"]) {
            return YES;
        } else {
            FMLog(@"你输入的身份证号非法");
            return NO;
        }
    } else {
        //用计算出的验证码与最后一位身份证号码匹配，如果一致，说明通过，否则是无效的身份证号码
        if ([idCardLast intValue] == [idCardA1[idCardMod] intValue]) {
            return YES;
        } else {
            FMLog(@"你输入的身份证号非法");
            return NO;
        }
    }
}


+ (NSString *)getIdentityCardSex:(NSString *)numberStr
{
    int sexInt=[[numberStr substringWithRange:NSMakeRange(16,1)] intValue];
    if(sexInt%2!=0)
    {
        return @"1";
    }
    else
    {
        return @"2";
    }
}


- (BOOL)isPostcode {
    NSString *regex = @"[0-9]{6}";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF matches %@", regex];
    return [predicate evaluateWithObject:self];
}


// 掩码字符串, range变*
- (NSString *)maskStringWithRange:(NSRange)range {
    NSString *preStr = @"";
    if (range.location != 0) {
        preStr = [self substringToIndex:range.location];
    }
    
    NSString *sufStr = @"";
    if (range.location + range.length < self.length) {
        sufStr = [self substringFromIndex:range.location + range.length];
    }
    
    NSString *mask = @"";
    for (NSInteger i = 0; i < range.length; i++) {
        mask = [mask stringByAppendingString:@"*"];
    }

    return [preStr stringByAppendingFormat:@"%@%@", mask, sufStr];
}



@end





