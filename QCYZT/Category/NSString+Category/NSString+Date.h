//
//  NSString+Date.h
//  QCYZT
//
//  Created by <PERSON> on 2019/2/22.
//  Copyright © 2019 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSString (Date)

/**
 创建formate格式的NSDateFormatter,使用字典是为了节省NSDateFormatter的性能损耗
 */
- (NSDateFormatter *)dateFormatterFromFormatStr;

/**
 将已经格式化的字符串转换为format格式的NSDate
 
 @param formatedStr 已经格式化的字符串，比如"2019-10-12 09:00:00"
 @param format 时间格式，比如yyyyMMdd等
 @return 返回格式化的NSDate
 */
+ (NSDate *)dateFromFormatedString:(NSString *)formatedStr format:(NSString *)format;


//获取时间差文案
+ (NSString *)getNowTimeWithStarTime:(NSInteger)startTime endTime:(NSInteger)endTime;

/// 快讯一图看懂,时间戳显示
+ (NSString *)getFlashNewsDateFormatByTimestamp:(long long)timestamp;

// 视频直播消息 时间戳转换
+ (NSString *)getDateFormatByTimestamp:(long long)timestamp liveType:(NSInteger)type;
// 距离当前时间差的显示
+ (NSString *)getDateFormatByTimestamp:(long long)timestamp;
@end

NS_ASSUME_NONNULL_END
