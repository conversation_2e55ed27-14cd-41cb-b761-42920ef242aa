//
//  NSString+characterJudge.h
//  QCYZT
//
//  Created by MacPro on 16/8/8.
//  Copyright © 2016年 sdcf. All rights reserved.
//  正则表达式判断

#import <Foundation/Foundation.h>

@interface NSString (characterJudge)


/**
 空字符串
 */
- (BOOL)isBlankString;

/**
 纯数字
 */
- (BOOL)isPureNumber;

// 正数
- (BOOL)isPositiveNumber;

/**
 *  数字、字母和汉字
 */
- (BOOL)isPureNumberAlphabetAndChinese;

/**
 *  数字和字母
 */
- (BOOL)isPureNumberAndAlphabet;
/**
 * 包含汉字
 */
- (BOOL)isincludeChinese;

/// 是否有 汉字 英文 数字
- (BOOL)isPureNumberOrAlphabetOrChinese;
/**
 *  字母
 */
- (BOOL)isPureAlphabet;

/**
 *  汉字
 */
- (BOOL)isPureChinese;

/**
 *  手机号
 */
- (BOOL)isPhoneNum;

/**
 *  QQ号
 */
- (BOOL)isQQ;

/**
 *  邮箱地址
 */
- (BOOL)isMailAddress;

/**
 *  身份证号码
 */
- (BOOL)isIdCardNumber;
// 判断是否为正确的出生日期
- (BOOL)validateIdNumberBirthday;

//根据身份证获取性别
+ (NSString *)getIdentityCardSex:(NSString *)numberStr;

/**
 *  邮编
 */
- (BOOL)isPostcode;

// 掩码字符串, range变*
- (NSString *)maskStringWithRange:(NSRange)range;

@end
