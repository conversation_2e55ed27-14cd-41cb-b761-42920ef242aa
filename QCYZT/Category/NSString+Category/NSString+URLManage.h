//
//  NSString+URLManage.h
//  FQYFNative
//
//  Created by macPro on 2018/8/13.
//  Copyright © 2018年 macPro. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface NSString (URLManage)

/**
 编码字符串

 @return 编码后的字符串
 */
- (NSString *)URLEncodedString;

/**
 解码字符串
 
 @return 解码后的字符串
 */
- (NSString *)URLDecodedString;

/**
 分离URL参数

 @return 参数字典
 */
- (NSDictionary *)getURLParams;

/**
去除html标签

@return 参数Str
*/
-(NSString *)clearHtmltag:(NSString *)content;

/// 获取主域名
- (NSString *)getRootDomai;

@end
