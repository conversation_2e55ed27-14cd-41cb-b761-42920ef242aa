//
//  NSString+URLManage.m
//  FQYFNative
//
//  Created by macPro on 2018/8/13.
//  Copyright © 2018年 macPro. All rights reserved.
//

#import "NSString+URLManage.h"

@implementation NSString (URLManage)

-(NSString *)URLEncodedString
{
    // 包含字母数字字符
    NSMutableCharacterSet *allowedCharacterSet = [NSMutableCharacterSet alphanumericCharacterSet];
    [allowedCharacterSet addCharactersInString:@"!$&'()*+,-./:;=?@_~%#[]"];

    NSString *encodedString = [self stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacterSet];
    return encodedString;
}

- (NSString*)URLDecodedString {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    NSString *result = (__bridge_transfer NSString *)
    CFURLCreateStringByReplacingPercentEscapesUsingEncoding(kCFAllocatorDefault,
                                                            (CFStringRef)self, CFSTR(""),  kCFStringEncodingUTF8);
    return result;
#pragma clang diagnostic pop
}

- (NSDictionary*)getURLParams {
    if ([self rangeOfString:@"?"].location == NSNotFound) {
        return nil;
    }
    NSString *paramString = [self substringFromIndex:[self rangeOfString:@"?"].location+1];
    if (paramString.length == 0) {
        return nil;
    }
    NSMutableDictionary *dic = [NSMutableDictionary new];
    if ([paramString containsString:@"&"]) {
        //如果有多个参数用“&”分隔，再分离key-value
        NSArray *d = [paramString componentsSeparatedByString:@"&"];
        for (NSString *item in d) {
            if ([item containsString:@"="]) {
                NSUInteger location = [item rangeOfString:@"="].location;
                if (location>0 && location<item.length-1) {
                    NSString *key   = [item substringToIndex:location];
                    NSString *value = [item substringFromIndex:location+1];
                    [dic setObject:value forKey:key];
                }
            }
        }
    } else {
        //如果只有一个参数，直接分离key-value
        if ([paramString containsString:@"="]) {
            NSUInteger location = [paramString rangeOfString:@"="].location;
            if (location>0 && location<paramString.length-1) {
                NSString *key   = [paramString substringToIndex:location];
                NSString *value = [paramString substringFromIndex:location+1];
                [dic setObject:value forKey:key];
            }
        }
    }
    return dic;
}

- (NSString *)clearHtmltag:(NSString *)content{
    NSRegularExpression *regularExpretion=[NSRegularExpression regularExpressionWithPattern:@"<[^>]*>|\n"
                                                                                    options:0
                                                                                      error:nil];
    content = [regularExpretion stringByReplacingMatchesInString:content options:NSMatchingReportProgress range:NSMakeRange(0, content.length) withTemplate:@""];
    return content;
}

/// 获取主域名
- (NSString *)getRootDomai {
    NSString *domain = nil;
    // 主域名正则
    NSString * pattern = @"[\\w-]+\\.(com.cn|com.hk|net.cn|gov.cn|org.cn|com|net|org|gov|cc|biz|info|cn|co|tv|mobi|me|name|asia|hk|ac.cn|bj.cn|sh.cn|tj.cn|cq.cn|he.cn|sx.cn|nm.cn|ln.cn|jl.cn|hl.cn|js.cn|zj.cn|ah.cn|fj.cn|jx.cn|sd.cn|ha.cn|hb.cn|hn.cn|gd.cn|gx.cn|hi.cn|sc.cn|gz.cn|yn.cn|xz.cn|sn.cn|gs.cn|qh.cn|nx.cn|xj.cn|tw.cn|hk.cn|mo.cn|travel|tw|com.tw|la|sh|ac|io|ws|us|tm|vc|ag|bz|in|mn|sc|co|org.tw|jobs|tel|网络|公司|中国)\\b()*";
    NSRegularExpression *regex = [[NSRegularExpression alloc] initWithPattern:pattern options:0 error:nil];
    // 获取主域名字符串的范围
    NSTextCheckingResult *match = [regex firstMatchInString:self options:0 range:NSMakeRange(0, [self length])];
    if (match) {// 截获主域名
        domain = [self substringWithRange:match.range];
    }
    return domain;
}

@end
