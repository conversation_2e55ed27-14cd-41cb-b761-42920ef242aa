//
//  NSURL+String.m
//  FQJD
//
//  Created by shumi on 2022/11/4.
//  Copyright © 2022 macPro. All rights reserved.
//

#import "NSURL+String.h"
#import "NSString+CharacterJudge.h"

@implementation NSURL (String)

+ (void)load {
    Method originMethod = class_getClassMethod([self class], @selector(URLWithString:));
    Method changeMethod = class_getClassMethod([self class], @selector(pk_UTF8URLWithString:));
    method_exchangeImplementations(originMethod, changeMethod);
}

/// url中带有中文时 进行编码处理
+ (NSURL *)pk_UTF8URLWithString:(NSString *)urlString {
    if ([urlString  isincludeChinese]) {
          //对URL中的中文进行编码
          NSString *url = [urlString stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
          NSURL * resultURL = [NSURL pk_UTF8URLWithString:url];
          return resultURL;
      } else {
          NSURL * resultURL = [NSURL pk_UTF8URLWithString:urlString];
          return resultURL;
      }
}

@end
