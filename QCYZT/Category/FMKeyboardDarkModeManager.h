//
//  FMKeyboardDarkModeManager.h
//  QCYZT
//
//  Created by Cursor on 2024-12-26
//  Copyright © 2024 Cursor. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 键盘暗黑模式管理器
 * 用于统一管理键盘外观与暗黑模式的适配
 */
@interface FMKeyboardDarkModeManager : NSObject

/**
 * 获取单例实例
 */
+ (instancetype)sharedManager;

/**
 * 启动键盘外观监听
 * 在AppDelegate中调用此方法开始监听
 */
+ (void)startMonitoring;

/**
 * 停止键盘外观监听
 */
+ (void)stopMonitoring;

/**
 * 主动更新所有输入框的键盘外观
 * 在主题切换后调用
 */
+ (void)updateAllKeyboardAppearances;

/**
 * 获取当前应该使用的键盘外观
 * @return UIKeyboardAppearanceDefault 或 UIKeyboardAppearanceDark
 */
+ (UIKeyboardAppearance)currentKeyboardAppearance;

@end

NS_ASSUME_NONNULL_END 