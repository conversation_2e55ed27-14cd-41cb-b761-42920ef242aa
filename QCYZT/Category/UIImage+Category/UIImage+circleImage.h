//
//  UIImage+circleImage.h
//  QCYZT
//
//  Created by MacPro on 16/11/7.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface UIImage (circleImage)

// 给image添加边框
- (UIImage *_Nullable)circleWithSize:(CGSize)imageSize borderWidth:(CGFloat)borderWidth borderColor:(UIColor *_Nonnull)borderColor;

// 裁剪image到指定大小
- (UIImage *_Nullable)cropImageToSize:(CGSize )size;

// 给image添加圆角
- (UIImage *_Nullable)addCornerRadius:(CGFloat)cornerRadius;

//给image指定边缘绘制圆角
- (UIImage *_Nullable)addCornerRadius:(CGFloat)cornerRadius byRoundingCorners:(UIRectCorner)corner;


// 给image添加文字水印
- (UIImage *_Nonnull)addTextWithImageSize:(CGSize)imgSize text:(NSString *_Nonnull)textStr textRect:(CGRect)textRect textAttributes:(nullable NSDictionary<NSString *, id> *)attrs;

// image图片灰化
- (UIImage *_Nullable)convertImageToGreyScale:(UIImage*_Nullable) image;

// image加上遮罩
- (UIImage *_Nullable)imageByMaskWithTint:(UIColor *_Nullable)tintColor;

/**
 生成一张渐变色图片

 @param imageSize 图片大小
 @param startPoint 渐变色起点
 @param endPoint 渐变色终点
 @param colors 颜色数组
 @param locations 颜色所在位置
 @param count 渐变个数，根据sizeof(colors)/(sizeof(colors[0])*4)计算
 @return UIImage图片
 */
+ (UIImage *_Nullable)createGradientImageWithSize:(CGSize)imageSize startPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint colors:(const CGFloat * cg_nullable)colors locations:(const CGFloat * __nullable)locations count:(size_t)count;

// 给image增加一段适应iPhone X的topArea的填充，topColor为单一填充色
- (UIImage *)addIphoneXTopAreaWithColor:(UIColor *)topColor;

@end
