//
//  FMKeyboardDarkModeManager.m
//  QCYZT
//
//  Created by Cursor on 2024-12-26
//  Copyright © 2024 Cursor. All rights reserved.
//

#import "FMKeyboardDarkModeManager.h"
//#import "UITextField+DarkKeyboard.h"
//#import "UITextView+DarkKeyboard.h"

// 键盘外观变化通知名称
NSString *const kFMKeyboardAppearanceDidChangeNotification = @"FMKeyboardAppearanceDidChangeNotification";

@interface FMKeyboardDarkModeManager ()

@property (nonatomic, assign) BOOL isMonitoring;

@end

@implementation FMKeyboardDarkModeManager

#pragma mark - 单例方法

+ (instancetype)sharedManager {
    static FMKeyboardDarkModeManager *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[FMKeyboardDarkModeManager alloc] init];
    });
    return manager;
}

- (instancetype)init {
    if (self = [super init]) {
        _isMonitoring = NO;
    }
    return self;
}

#pragma mark - 公共方法

+ (void)startMonitoring {
//    FMKeyboardDarkModeManager *manager = [self sharedManager];
//    if (manager.isMonitoring) return;
//    
//    manager.isMonitoring = YES;
//    
//    // 注册UP主题变化通知
//    [[NSNotificationCenter defaultCenter] addObserver:manager
//                                             selector:@selector(themeDidChange:)
//                                                 name:kUPThemeDidChangeNotification
//                                               object:nil];
//        
//    // 初始更新一次键盘外观
//    [self updateAllKeyboardAppearances];
}

+ (void)stopMonitoring {
//    FMKeyboardDarkModeManager *manager = [self sharedManager];
//    if (!manager.isMonitoring) return;
//    
//    manager.isMonitoring = NO;
//    
//    [[NSNotificationCenter defaultCenter] removeObserver:manager];
}

+ (void)updateAllKeyboardAppearances {
//    // 发送键盘外观变化通知
//    UIKeyboardAppearance appearance = [self currentKeyboardAppearance];
//    [[NSNotificationCenter defaultCenter] postNotificationName:kFMKeyboardAppearanceDidChangeNotification 
//                                                        object:@(appearance)];
//    
//    // 更新全局外观设置
//    [[UITextField appearance] setKeyboardAppearance:appearance];
//    [[UITextView appearance] setKeyboardAppearance:appearance];
//    
//    // 更新所有可见的输入框
//    for (UIWindow *window in [UIApplication sharedApplication].windows) {
//        [self updateKeyboardAppearancesInView:window];
//    }
}

+ (UIKeyboardAppearance)currentKeyboardAppearance {
//    // 获取当前暗黑模式设置
//    NSString *darkModelSetting = [FMUserDefault getUnArchiverDataForKey:DarkModeSetting];
//    
//    if (darkModelSetting.length > 0) {
//        NSInteger index = [darkModelSetting integerValue];
//        if (index == 0) { // 跟随系统
//            if (@available(iOS 13.0, *)) {
//                UIUserInterfaceStyle style = [UIScreen mainScreen].traitCollection.userInterfaceStyle;
//                return (style == UIUserInterfaceStyleDark) ? UIKeyboardAppearanceDark : UIKeyboardAppearanceDefault;
//            } else {
//                return UIKeyboardAppearanceDefault;
//            }
//        } else if (index == 1) { // 白天模式
//            return UIKeyboardAppearanceDefault;
//        } else { // 夜间模式
//            return UIKeyboardAppearanceDark;
//        }
//    } else {
//        // 默认使用白天模式
//        return UIKeyboardAppearanceDefault;
//    }
    
    return UIKeyboardAppearanceDefault;
}

#pragma mark - 通知处理

- (void)themeDidChange:(NSNotification *)notification {
    // UP主题变化时更新键盘外观
    dispatch_async(dispatch_get_main_queue(), ^{
        [FMKeyboardDarkModeManager updateAllKeyboardAppearances];
    });
}

#pragma mark - 私有方法

// 递归更新视图层次中所有输入框的键盘外观
+ (void)updateKeyboardAppearancesInView:(UIView *)view {
//    UIKeyboardAppearance appearance = [self currentKeyboardAppearance];
//    
//    // 更新UITextField
//    if ([view isKindOfClass:[UITextField class]]) {
//        UITextField *textField = (UITextField *)view;
//        
//        // 只有当键盘外观不同时才更新，避免不必要的重绘
//        if (textField.keyboardAppearance != appearance) {
//            textField.keyboardAppearance = appearance;
//            
//            // 如果正在编辑，需要重新弹出键盘以应用新外观
//            if (textField.isFirstResponder) {
//                [textField resignFirstResponder];
//                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//                    [textField becomeFirstResponder];
//                });
//            }
//        }
//    }
//    
//    // 更新UITextView
//    else if ([view isKindOfClass:[UITextView class]]) {
//        UITextView *textView = (UITextView *)view;
//        
//        // 只有当键盘外观不同时才更新，避免不必要的重绘
//        if (textView.keyboardAppearance != appearance) {
//            textView.keyboardAppearance = appearance;
//            
//            // 如果正在编辑，需要重新弹出键盘以应用新外观
//            if (textView.isFirstResponder) {
//                [textView resignFirstResponder];
//                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//                    [textView becomeFirstResponder];
//                });
//            }
//        }
//    }
//    
//    // 递归处理所有子视图
//    for (UIView *subview in view.subviews) {
//        [self updateKeyboardAppearancesInView:subview];
//    }
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end 
