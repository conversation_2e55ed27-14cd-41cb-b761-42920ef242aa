//
//  UIView+RoundedCorners.m
//  
//
//  Created by <PERSON> on 2020/8/10.
//  Copyright © 2020 macPro. All rights reserved.
//



@implementation UIView (RoundedCorners)

- (void)layerAndBezierPathWithRect:(CGRect)bounds cornerRadii:(CGSize)size byRoundingCorners:(UIRectCorner)corner{
    UIBezierPath *path = [UIBezierPath bezierPathWithRoundedRect:bounds byRoundingCorners:corner cornerRadii:CGSizeMake(size.width, size.height)];
       CAShapeLayer *layer = [[CAShapeLayer alloc] init];
       layer.frame = bounds;
       layer.path = path.CGPath;
       self.layer.mask = layer;
       
}

//绘制渐背景色
- (void)drawCAGradientWithcolors:(NSArray *)colors {
    NSMutableArray *arr = [NSMutableArray arrayWithArray:self.layer.sublayers];
    for (NSInteger i = 0; i < self.layer.sublayers.count; i ++) {
        id layer = self.layer.sublayers[i];
        if ([layer isKindOfClass:[CAGradientLayer class]]) {
            [arr removeObjectAtIndex:i];
        }
    }
    self.layer.sublayers = arr;
    CAGradientLayer *layer = [CAGradientLayer layer];
    layer.frame = self.bounds;
    layer.colors = colors;
    layer.startPoint = CGPointMake(0, 0);
    layer.endPoint = CGPointMake(1.0, 0);
    [self.layer addSublayer:layer];
    [self.layer insertSublayer:layer atIndex:-1];
}


//指定渐变方向 绘制渐背景色
- (void)drawCAGradientWithcolors:(NSArray *)colors startPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint {
    NSMutableArray *arr = [NSMutableArray arrayWithArray:self.layer.sublayers];
    for (NSInteger i = 0; i < self.layer.sublayers.count; i ++) {
        id layer = self.layer.sublayers[i];
        if ([layer isKindOfClass:[CAGradientLayer class]]) {
            [arr removeObjectAtIndex:i];
        }
    }
    self.layer.sublayers = arr;
    CAGradientLayer *layer = [CAGradientLayer layer];
    layer.frame = self.bounds;
    layer.colors = colors;
    layer.startPoint = startPoint;
    layer.endPoint = endPoint;
    [self.layer addSublayer:layer];
    [self.layer insertSublayer:layer atIndex:-1];
}

@end
