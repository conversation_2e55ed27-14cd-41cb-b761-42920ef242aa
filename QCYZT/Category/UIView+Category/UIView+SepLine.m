//
//  UIView+SepLine.m
//  QCYZT
//
//  Created by macPro on 2019/5/29.
//  Copyright © 2019 LZKJ. All rights reserved.
//

#import "UIView+SepLine.h"

@implementation UIView (SepLine)

- (UIView *)addSepLineWithBlock:(void(^)(MASConstraintMaker *))block {
    UIView *sepline = [[UIView alloc] init];
    sepline.backgroundColor = UIColor.fm_sepline_color;
    [self addSubview:sepline];
    [sepline mas_makeConstraints:^(MASConstraintMaker *make) {
        if (block) {
            block(make);
        }
    }];
    return sepline;
}

@end
