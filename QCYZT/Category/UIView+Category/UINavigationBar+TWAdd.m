//
//  UINavigationBar+TWAdd.m
//  QCYZT
//
//  Created by 涂威 on 2017/8/1.
//  Copyright © 2017年 sdcf. All rights reserved.
//

#import "UINavigationBar+TWAdd.h"
#import "UIImage+stocking.h"

@implementation UINavigationBar (TWAdd)


- (void)setBgAlpha:(CGFloat)bgAlpha {
    UIView *v = [self valueForKey:@"_backgroundView"];
    if(v) {
        v.alpha = bgAlpha;
    }
}

- (CGFloat)bgAlpha {
    UIView *v = [self valueForKey:@"_backgroundView"];
    if(v) {
        return v.alpha;
    }
    return 0;
}

- (void)setBgColor:(UIColor *)bgColor {
    self.barTintColor = bgColor;
    [self setBackgroundImage:[UIImage imageWithColor:bgColor andSize:CGSizeMake(UI_SCREEN_WIDTH, UI_SAFEAREA_TOP_HEIGHT)] forBarMetrics:UIBarMetricsDefault];
}

- (UIColor *)bgColor {
    return self.barTintColor;
}


- (void)setBgImage:(UIImage *)bgImage {
    objc_setAssociatedObject(self, @selector(bgImage), bgImage, OBJC_ASSOCIATION_RETAIN_NONATOMIC);

    [self setBackgroundImage:bgImage forBarMetrics:UIBarMetricsDefault];
}

- (UIImage *)bgImage {
    return objc_getAssociatedObject(self, _cmd);
}

@end
