---
type: "always_apply"
description: "Example description"
---
# 项目结构与文件组织规范

## 目录结构
- `AppEntry`: 应用入口相关文件，包括AppDelegate、SceneDelegate等
- `Classes/Sections`: 主要业务代码，按照功能模块进行分类
  - `HomePage`: 首页相关功能
  - `MemberCenter`: 会员中心，包含股市赢家等VIP功能
  - `Market`: 行情相关功能
  - `Live`: 直播相关功能
  - `UserCenter(我的)`: 个人中心功能
  - `Note`: 笔记/动态相关功能
  - `Course`: 课程相关功能
  - `Search`: 搜索功能
  - `Common`: 通用组件和视图
- `Config`: 配置文件和常量定义
- `Resource`: 资源文件，包括图片、音频、视频等
- `Util`: 工具类和辅助函数
- `Category`: 类别扩展
- `CommonTool`: 通用工具和组件
- `Vendors`: 第三方库和框架
- `UPStock`: 优品股票（第三方行情）相关模块
- `Images.xcassets`: 图片资源管理，按功能模块分组

## 模块分层
- 每个业务模块应遵循MVC/MVVM架构
- 模型(Model): 数据模型和业务逻辑
- 视图(View): 界面展示
- 控制器(Controller): 业务流程控制
- （可选）视图模型(ViewModel): 视图与模型的绑定层

## 功能模块化
- 相关功能应组织在同一模块中
- 模块之间通过协议或通知进行通信
- 避免模块间直接引用，减少耦合
- 公共功能应提取到基础服务层

## 文件组织
- 每个控制器对应的视图和模型应放在同一目录下
- 相关功能的文件应放在同一目录下
- 通用组件应放在CommonTool目录下
- 工具类应放在Util目录下

## 网络架构
- 三层网络架构：
  - `QCHTTPSessionManager/NetworkManager`: 基础网络层
  - `HttpRequestTool`: 通用网络请求方法
  - `HttpRequestTool+XXX`: 业务模块网络请求扩展
- API命名规范：`request[ModuleName][FunctionDescription]With[Parameter]`
- 网络请求分类文件统一放在 `CommonTool/Http/` 目录

## 依赖管理
- 使用Cocoapods管理第三方库
- podfile中按照功能对第三方库进行分类
- 避免不必要的依赖引入
- 核心功能不应依赖不稳定的第三方库
- 项目包含多个本地Pod模块：CNCommonUI、CNMainBussiness、UPCommon等

## 资源管理
- 图片资源应使用Asset Catalog管理
- 配置信息使用plist文件或常量定义
- 避免资源文件重复

## 子模块管理
- 独立功能模块封装为单独的库或Framework
- 模块间通过协议或中介者模式通信
- 避免模块间循环依赖

## 文件位置规范

### 目录命名规范
- 目录名称应使用大驼峰命名法（如：`StatisticsHelper`、`CustomView`）
- 功能模块目录应根据功能特性命名（如：`Http`、`BaseHelper`、`PayCommonTool`）
- 公共组件和工具应放在 `CommonTool/` 目录下
- 特定业务功能应放在 `Classes/` 目录的对应业务模块下

### 文件位置指导原则

1. **网络请求相关文件**：
   - 基础请求类及其分类放在 `CommonTool/Http/` 目录
   - 按业务模块分类：`HttpRequestTool+ModuleName`
   - API模型可放在对应业务模块下

2. **工具类文件**：
   - 通用工具类放在 `CommonTool/` 目录
   - 业务特定工具类放在对应业务模块下
   - 按功能分组，如统计、文件处理、数据库等

3. **分类（Category）文件**：
   - 通用分类放在 `Category/` 目录
   - 业务特定分类放在业务模块的子目录

4. **第三方库和主题文件**：
   - 第三方库统一放在 `Vendors/` 目录
   - 主题适配文件按模块组织
   - 支持暗黑模式的颜色管理

5. **业务模块文件**：
   - 按功能模块组织，如解析、数据库、UI组件等
   - 相关文件放在同一目录下
   - 避免过深的目录嵌套

## 文件创建规范

### 新建文件夹时
- 确保文件夹命名符合大驼峰命名法
- 放置在正确的业务模块或工具目录下
- 相关功能的文件应放在同一目录

### 新建文件时
- 确保文件头部包含正确的创建信息（作者、创建日期等）
- 将文件放置在正确的目录下，遵循现有项目结构
- 创建日期必须是真实的创建时间（当前时间）

### 命名规范

**目录命名原则**：
- 使用大驼峰命名法
- 名称应清晰反映功能
- 避免使用缩写和特殊字符
- 保持命名的一致性

**文件命名原则**：
- 类名使用大驼峰命名法
- 分类使用 `ClassName+CategoryName` 格式
- 工具类以 `Tool`、`Helper`、`Manager` 等后缀结尾
- 模型类以 `Model` 后缀结尾

## 重要约定

- 新增功能时，先了解现有项目结构，根据类似功能的位置确定新文件的位置
- 如遇功能交叉或不确定，优先放在相关度最高的业务模块下
- 避免创建过多的顶层目录，保持项目结构清晰
- 严格按照目录规范组织文件，防止项目结构混乱

## 特殊文件组织规范

### 网络请求分类文件
- 按业务模块分类：HttpRequestTool+MemberCenter、HttpRequestTool+Home等
- 每个分类包含该模块的所有API请求方法
