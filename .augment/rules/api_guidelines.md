---
type: "agent_requested"
description: "网络请求接口规范"
---
# 网络请求与API规范

## 网络层架构

项目采用三层网络架构：
- **底层**: `QCHTTPSessionManager`/`NetworkManager` - 基础网络请求封装
- **中层**: `HttpRequestTool` - 通用请求方法和参数处理
- **上层**: `HttpRequestTool+XXX` - 业务模块分类扩展

## API命名规范

- 接口命名采用 `request[模块名][功能描述]With[主要参数名（可选）]` 格式
  - 示例：`requestMemberCenterLHZYBadgesWithDay`、`requestGSYJTodayOperationWithServerType`
  - 模块名应清晰反映其所属业务领域，如 `MemberCenter`、`GSYJ`、`Market`
  - 功能描述应简洁明了，如 `LHZYBadges`（龙虎捉妖角标）、`TodayOperation`（今日操作）
- 查询接口统一使用 `request` 前缀
- 添加操作使用 `add` 前缀，删除操作使用 `delete` 前缀，修改操作使用 `update` 前缀

## API路径定义规范

- API路径在模块的 `.m` 文件顶部定义为常量：
  ```objective-c
  // 使用静态常量
  static NSString *const kAPI_GSYJ_TodayOperation = @"/api/v2/csp/todayOpera.do";
  ```
- 常量命名规范：`kAPI_[模块名]_[功能描述]`
- 每个常量定义后必须跟随注释说明用途

## 参数处理规范

- 方法参数使用小驼峰命名法
- 时间类参数使用 `day`、`date`、`time` 等，如 `tradingDay`
- ID类参数使用具体实体+Id命名，如 `bigcastId`、`groupId`
- 页码相关参数统一使用 `page`、`pageNo` 和 `pageSize`
- 可选参数在加入params前必须进行有效性检查：
  ```objective-c
  if (day.length) {
      [params setObject:day forKey:@"day"];
  }
  ```

## 回调规范

- 所有接口必须提供三个基础回调：
  ```objective-c
  start:(void (^)())startBlock
  failure:(void (^)())failBlock
  success:(requestSuccessBlock)success
  ```
- 请求成功回调统一使用 `requestSuccessBlock`（即 `NSDictionary*` 类型）
- 下载类接口需要额外提供进度回调 `progress:(void(^)(float progress))progressBlock`

## 业务模块分类

- 按业务功能划分，每个模块对应一个分类文件：
  - `HttpRequestTool+MemberCenter` - 会员中心相关接口
  - `HttpRequestTool+Stock` - 股票相关接口
  - `HttpRequestTool+Live` - 直播相关接口
  - `HttpRequestTool+LoginRegister` - 登录注册相关接口
- 所有接口方法均为类方法（以 `+` 开头）
- 新增接口必须添加到对应的功能模块分类文件中

## 通用请求方法

HttpRequestTool基类提供以下通用方法：
- `getDataInfoWithUrl:params:start:failure:success:` - GET请求
- `postDataInfoWithUrl:params:start:failure:success:` - POST请求
- `putDataInfoWithUrl:params:start:failure:success:` - PUT请求
- `postDataJsonInfoWithUrl:params:start:failure:success:` - JSON格式POST请求
- `normalHttpDataInfoWithType:url:params:start:failure:success:` - 不做异常处理的普通请求

## 响应数据处理

- 使用 `lz_HttpStatusCheck(dic)` 宏检查响应状态（成功为"1"）
- 响应数据统一使用 `NSDictionary` 解析
- 通过 `status` 字段判断请求是否成功

## 参数加密与公共参数

- 使用 `hashWithParams:` 方法处理参数加密
- 自动添加公共参数：版本号(version)、平台(platform)、时间戳(timestamp)、签名(sign)

## 缓存策略

- 使用 `CacheRequestManager` 进行网络请求缓存管理
- 适用于需要本地缓存的数据接口
- 支持缓存过期时间设置和清理机制

## API安全和性能规范

### 安全要求
- 所有API请求必须包含必要的身份验证
- 敏感参数必须进行加密传输
- 实施请求频率限制，防止恶意调用
- 对返回数据进行适当的过滤和验证

### 性能优化
- 合理设置请求超时时间
- 实施请求缓存策略，避免重复请求
- 大数据传输时考虑分页处理
- 监控API响应时间，及时优化慢接口

### 错误处理规范
- 统一的错误码定义和处理
- 详细的错误日志记录
- 用户友好的错误提示信息
- 网络异常时的重试机制

## API调用示例

### 标准GET请求示例：
```objective-c
// .m文件顶部定义API路径
static NSString *const kAPI_GSYJ_TodayOperation = @"/api/v2/csp/todayOpera.do";

// 股市赢家今日操作
+ (void)requestGSYJTodayOperationWithServerType:(NSString *)serverType
                                           start:(void (^)(void))startBlock
                                         failure:(void (^)(void))failBlock
                                         success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    // 可选参数检查
    if (serverType.length) {
        [params setObject:serverType forKey:@"serverType"];
    }
    [self getDataInfoWithUrl:KDakaBaseUrl(kAPI_GSYJ_TodayOperation)
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}
```

### 带Header的请求示例：
```objective-c
[self getDataInfoWithUrl:url
                  header:(serverType.length ? @{@"serverType": serverType} : nil)
                  params:params
                   start:startBlock
                 failure:failBlock
                 success:success];
```

### 分页请求示例：
```objective-c
+ (void)requestBigcastRecordListWithPage:(NSUInteger)page
                                pageSize:(NSUInteger)pageSize
                                   start:(void (^)())startBlock
                                 failure:(void (^)())failBlock
                                 success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:[NSString stringWithFormat:@"%zd", page] forKey:@"pageNo"];
    [params setObject:[NSString stringWithFormat:@"%zd", pageSize] forKey:@"pageSize"];
    [self getDataInfoWithUrl:kAPI_MemberCenter_BigcastRecordList
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}
```