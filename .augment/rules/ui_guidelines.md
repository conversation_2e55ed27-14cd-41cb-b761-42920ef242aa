---
type: "agent_requested"
description: "UI开发规范"
---
# UI开发核心规范

## 强制性要求（违反将导致代码不规范）

### 字体设置强制规范
- **禁止**直接使用 `[UIFont systemFontOfSize:]` 等系统方法
- **必须**使用项目字体宏：
  - 普通字体: `FontWithSize(size)`
  - 粗体字体: `BoldFontWithSize(size)`
  - 中等字体: `MediumFontWithSize(size)`
  - 数字字体: `kFontNumber(size)`
- **必须**使用Label便捷初始化方法而非系统init方法

### 颜色设置强制规范
- **禁止**直接使用 `[UIColor blackColor]` 等系统方法
- **必须**使用项目颜色宏：
  - RGB: `ColorWithRGB(r, g, b)`
  - 十六进制: `ColorWithHex(0xRRGGBB)`
  - 常用颜色: `FMClearColor`, `FMNavColor`, `FMWhiteColor`
  - UI主题色: `UIColor.up_textPrimaryColor`, `UIColor.up_riseColor`

### UI组件创建强制规范
- **禁止**使用系统默认初始化方法
- **必须**优先使用项目中的分类初始化方法：
  - UILabel: 使用 `initWithFrame:font:textColor:backgroundColor:numberOfLines:` 系列方法
  - UIButton: 使用 `initWithFrame:font:normalTextColor:backgroundColor:title:image:target:action:` 方法
  - UITableView: 使用 `initWithFrame:style:delegate:dataSource:viewController:` 方法
  - UIStackView: 使用 `initWithAxis:alignment:distribution:spacing:arrangedSubviews:` 方法

### 日期处理强制规范
- **禁止**手动创建NSDateFormatter进行日期转换
- **必须**使用项目日期分类方法：
  - NSDate转字符串: `[date dateStringWithFormatString:]`
  - 字符串转NSDate: `[NSDate dateFromFormatedString:format:]`

### 约束设置强制规范
- **必须**使用Masonry进行布局
- **必须**使用项目简写宏: `equalTo()`, `offset()` 等
- **推荐**使用UIStackView管理多视图布局

### UI工具方法强制规范
- **必须**使用项目UI工具宏而非手动设置：
  - 圆角: `UI_View_Radius(view, radius)`
  - 边框: `UI_View_Border(view, width, color)`
  - 圆角+边框: `UI_View_BorderRadius(view, radius, width, color)`
  - 分割线: `[view addSepLineWithBlock:]`

## 重复方法调用优先级
- **最高优先级**: 项目中已有的分类方法、工具类方法
- **次要优先级**: 项目中已声明的宏定义
- **禁止**: 重新实现已有功能

## 参考文档引用
- 快速查找索引: `.cursor/docs/UI规范/UI规范快速索引.md`
- 详细UI组件方法说明: `.cursor/docs/UI规范/UI组件API参考.md`
- UI工具方法文档: `.cursor/docs/UI规范/UI工具方法参考.md`
- UI代码示例: `.cursor/docs/UI规范/UI最佳实践示例.md`  

## UI性能优化规范

### 布局性能
- 避免在主线程进行复杂的布局计算
- 合理使用 Auto Layout，避免过度约束
- 大列表使用 cell 重用机制
- 避免频繁的 addSubview 和 removeFromSuperview 操作

### 内存管理
- 及时释放不需要的视图和资源
- 避免循环引用，特别是 block 中的强引用
- 大图片使用懒加载和缓存机制
- 监控内存使用情况，及时优化

### 渲染优化
- 避免透明视图的过度使用
- 合理设置视图的 opaque 属性
- 减少离屏渲染，如阴影、圆角等效果
- 使用 CALayer 进行复杂动画

## UI测试规范

### 界面测试要点
- 验证各种屏幕尺寸的适配效果
- 测试暗黑模式和普通模式的显示
- 检查文字截断和布局异常
- 验证交互响应和动画效果

## 常见错误检查清单
1. ❌ 使用 `[UIFont systemFontOfSize:]` → ✅ 使用 `FontWithSize()`
2. ❌ 使用 `[UIColor blackColor]` → ✅ 使用 `UIColor.up_textPrimaryColor`
3. ❌ 使用 `[[UILabel alloc] init]` → ✅ 使用分类初始化方法
4. ❌ 手动NSDateFormatter → ✅ 使用 `[date dateStringWithFormatString:]`
5. ❌ 手动设置圆角 → ✅ 使用 `UI_View_Radius()`
6. ❌ 主线程进行复杂布局 → ✅ 异步处理或优化算法
7. ❌ 忘记设置 opaque 属性 → ✅ 合理设置提升渲染性能
